{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
      {
        "type": "node",
        "request": "launch",
        "name": "Launch Program",
        "program": "${workspaceFolder}/src/index.ts",
        "preLaunchTask": "tsc: build - tsconfig.json",
        "outFiles": [
            "${workspaceFolder}/lib/**/*.js"
        ],
        "showAsyncStacks": true,
        "env": {
          "BLUEBIRD_DEBUG": "1"
        },
        "runtimeArgs": [
          "--max-old-space-size=8192"
        ],
        "outputCapture": "std"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Run API, no build",
      "program": "${workspaceFolder}/src/index.ts",
      "outFiles": [
          "${workspaceFolder}/lib/**/*.js"
      ],
      "showAsyncStacks": true,
      "env": {
        "BLUEBIRD_DEBUG": "1"
      },
      "runtimeArgs": [
        "--max-old-space-size=8192"
      ],
      "outputCapture": "std"
    },
    {
      "name": "Mocha test DAG transforms",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/ts-mocha/bin/ts-mocha",
      "stopOnEntry": false,
      "args": [ "--recursive", "${workspaceFolder}/test/services/public/data-exporter/data-export-queries/**/*.test.ts" ]
    }
  ]
}
