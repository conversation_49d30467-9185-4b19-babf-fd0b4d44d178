import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';
import { getSysConstString } from '../../../../util/sys-const-string'
import { Errors } from '../../../../errors/general';
import { Knex } from 'knex';
import {DbRawSeq, dbRawRead, dbRawReadReporting, dbRawReadSingle, dbRawWrite, dbRawWriteMulti} from '../../../../util/db-raw';
import moment from 'moment';
import {Student} from '../student/student.class'
import { arrToMap } from '../../../../util/param-sanitization';
import logger from '../../../../logger';
import { isABED } from '../../../../util/whiteLabelParser';
import { SQL_ACTIVE_SC_BY_GID, SQL_SCHL_SESSIONS, SQL_SC_BY_S_GID_AND_SS, SQL_SC_INVIGILATORS, SQL_SC_STUDENTS, SQL_SC_STUDENTS_GUESTS, SQL_SC_TEACHERS, SQL_SD_BY_GID, SQL_SS_BY_TW, SQL_STA_BY_TW, SQL_STU_ACCOMM_BY_UIDS, SQL_STU_ATT, SQL_STU_INFO_BY_UIDS, SQL_STU_META_ALL_BY_UIDS, SQL_STU_META_BY_UIDS, SQL_STU_SUBMISSIONS, SQL_S_BY_UR, SQL_S_PAY_AGR_BY_S_GID, SQL_S_TW_TA, SQL_TEACHER_DETAILS, SQL_TWTAR_BY_TW, SQL_TW_BY_TYPE_SLUGS, SQL_TW_TYPE_SLUG_MAPPINGS, SQL_UNACCEPTED_TW, SQL_TS_PENDING_SCANS, SQL_SESSION_TEST_DESIGN_IDS} from './sql';
import {isAdminAltReqAllowed} from './../../alt-version-ctrl/feature-flag-config'
import { SQL_NUM_QUESTION_REQUIRING_SCAN } from '../session/sql';
interface Data { }

interface ServiceOptions { }

type ISchool = any;

interface ScanSessionInfo {
  [test_session_id: string]: {
    test_design_id?: number;
    is_scan_session?: boolean;
    n_students?: number;
    n_scans_expected?: number;
    n_scans_received?: number;
  }
};

export async function getTwTypeSlugs(app: any, dbRawSeq?:DbRawSeq) {
  let records:any[] = [];
  if (dbRawSeq){
    records = await dbRawSeq.read(SQL_TW_TYPE_SLUG_MAPPINGS, {});
  }
  else {
    records = await dbRawReadReporting(app, [], SQL_TW_TYPE_SLUG_MAPPINGS);
  }
  return records.map(row => row.type_slug);
}

export class School implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  overlapVariables = Student.overlapVariables;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async getUnacceptedTestWindows(test_windows: any[], uid: number) {
    const unacceptedTestWindows = dbRawReadReporting(this.app, {uid}, SQL_UNACCEPTED_TW)
    return unacceptedTestWindows;
  }

  async getSchoolPaymentAgreement(dbRawSeq:DbRawSeq, schoolFirst:ISchool){
    let showPaymentAgreement = false;
    const s_group_id = schoolFirst.group_id
    if(schoolFirst.payment_req_g9 == 1 || schoolFirst.payment_req_osslt == 1 || schoolFirst.payment_req_pj == 1){
      const paymentAgreementRecord = await dbRawSeq.read(SQL_S_PAY_AGR_BY_S_GID, {s_group_id});
      if(paymentAgreementRecord.length == 0){
        showPaymentAgreement = true;
      }
      if(schoolFirst.school_type === 'first_nations') {
        showPaymentAgreement = false;
      }
    }
    return showPaymentAgreement
  }

  sanitizeTwStudentMetaAll(all_tw_student_meta:any[], tw_student_meta:any[], schoolFirst:ISchool){
    all_tw_student_meta.forEach(tw_um => {
      try{
        tw_um.meta = JSON.parse(tw_um.meta);
        if(tw_um.key != 'StudentSchoolEnrolmentInfo'){
          tw_student_meta.push(tw_um);
          return;
        }
        if(!tw_um.meta.SchoolCode){
          return;
        }
        if(schoolFirst.foreign_id == `S.${tw_um.meta.SchoolCode}`){
          tw_student_meta.push(tw_um);
        }
      }catch(e){
        tw_um.meta = null;
      }
    })
  }

  sanitizeSchoolSessions(school_sessions:any[]){
    school_sessions.forEach(schoolSession => {
      if (schoolSession.num_attempts > schoolSession.num_students){
        schoolSession.num_students = schoolSession.num_attempts;
      }
      if (schoolSession.sc_is_active == 0 && schoolSession.is_closed == 0){
        schoolSession.closedBecauseInactive = true;
        schoolSession.is_closed = 1;
      }
    })
  }

  /** Determine if sessions are scan sessions based of scan questions in their test design */
  async getSessionScanProgress(test_session_ids: number[]) {

    const sessionScanProgress: ScanSessionInfo= {};

    // get test designs 
    const sessionDesignRecords = await dbRawReadReporting(this.app, { test_session_ids }, SQL_SESSION_TEST_DESIGN_IDS);
    const test_design_ids = [...new Set(sessionDesignRecords.map(s => s.test_design_id))]
    if (!test_design_ids.length) return sessionScanProgress;
    const tsToTdIdRef = new Map();
    sessionDesignRecords.forEach((row: any) => {
      tsToTdIdRef.set(+row.test_session_id, +row.test_design_id)
    });

    const tdScanNums = await dbRawReadReporting(this.app,{test_design_ids},SQL_NUM_QUESTION_REQUIRING_SCAN);
    const scanProgressRecords:any[] = await dbRawReadReporting(this.app,{test_session_ids}, SQL_TS_PENDING_SCANS);
    const hasScansRef = new Map()
    for (let record of tdScanNums){
      if (record.num_scans > 0){
        hasScansRef.set(+record.test_design_id, true)
      }
    }
    for (let scanProgress of scanProgressRecords) {
      sessionScanProgress[''+scanProgress.test_session_id] = scanProgress;
      scanProgress.test_design_id = tsToTdIdRef.get(scanProgress.test_session_id);
      const hasScans = !!hasScansRef.get(scanProgress.test_design_id);
      if (hasScans) {
        scanProgress.is_scan_session = true;
      }
    }
    return sessionScanProgress;
  }

  sanitizeClassStudents(classes_students:any[], guest_classes_students:any[]){
    classes_students.forEach(cs => {
      cs.is_guest = 0
      cs.scg_id = -1
    });
    guest_classes_students.forEach(gcs => gcs.is_guest = 1)
  }

  sanitizeTestAttempts(test_attempts:any[], school_sessions:any[]){
    const sessionsById = arrToMap(school_sessions, 'test_session_id');
    test_attempts.forEach(sessionSubmissionInfo => {
      const session = sessionsById.get(sessionSubmissionInfo.submitted_test_session_id);
      if (session){
        logger.silly('sessionSubmissionInfo %s %s', session.num_students, sessionSubmissionInfo.num_attempts)
        if (session.num_students < sessionSubmissionInfo.num_attempts){
          session.num_students = sessionSubmissionInfo.num_attempts;
        }
      }
    })
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {

    const whiteLabel = this.app.get('whiteLabel');
    const isStrictSessions = isABED(whiteLabel);

    if (!params || !params.query) {
      throw new Errors.BadRequest();
    }
    
    const uid = await currentUid(this.app, params);
    const isReadReplica = params.query.isReadReplica;
    const group_id = params.query.group_id;

    const dbRawSeq = new DbRawSeq(this.app, 'FIND:public/school-admin/school');

    let school_extra_course_type :any[] = [];
    let school_semesters:any[] = [];
    let classes:any[] = []
    let teachers:any[] = [];
    let classes_invigilators:any[] = [];
    let classes_students:any[] = [];
    let guest_classes_students: any[] = [];
    var student_accommodations: any[] = [];
    var students:any[] = [];
    var student_submissions:any[]=[];
    var student_testAttempts:any[]=[];
    var student_meta:any[]=[];
    let tw_student_meta:any[] = [];
    let all_tw_student_meta:any[] = [];
    var school_sessions:any[]=[];

    const TW_TYPE_SLUGS = await getTwTypeSlugs(this.app, dbRawSeq);
    // const classrooms = await dbRawSeq.read(SQL_ACTIVE_SC_BY_GID, {group_id}) // why are we calling this first
    const school = await dbRawSeq.read(SQL_S_BY_UR, {group_id, uid})
    if (school.length === 0) {
      throw new Errors.NotFound('NO_SCHOOL_ROLE_FOR_USER');
      // throw new Errors.NotFound('NO_SCHOOLS_FOR_USER');
    }
    const schoolFirst = school[0];
    const schl_dist_group_id = schoolFirst.schl_dist_group_id;

    const showPaymentAgreement = await this.getSchoolPaymentAgreement(dbRawSeq, schoolFirst);
    const sch_group_id = schoolFirst.group_id;

    const schl_dist = await dbRawSeq.read(SQL_SD_BY_GID, {schl_dist_group_id})
    const isAllowQaWindows = (+schl_dist[0].is_sample == 1);
    
    let test_windows = await dbRawSeq.read(SQL_TW_BY_TYPE_SLUGS(isAllowQaWindows), {TW_TYPE_SLUGS})
    const test_windows_ids:any[] = test_windows.map( (test_window:any) => {return test_window.id });

    const test_window_td_alloc_rules = await dbRawSeq.read(SQL_TWTAR_BY_TW, {test_windows_ids});
    test_windows.map((test_window) => {
      test_window.assessments = test_window_td_alloc_rules.filter((twtarEntry) => {
        return twtarEntry.test_window_id === test_window.id
      })
    });

    if(sch_group_id && test_windows_ids.length > 1){
      school_extra_course_type = await dbRawSeq.read(SQL_STA_BY_TW, {sch_group_id, test_windows_ids})
    }

    if(test_windows_ids.length > 0){
      school_semesters = await dbRawSeq.read(SQL_SS_BY_TW, {test_windows_ids})
    }
    const school_semesters_id:any[] = school_semesters.map( (semester:any) => {return semester.id });

    if(school_semesters_id.length >0){
      classes = await dbRawSeq.read(SQL_SC_BY_S_GID_AND_SS, {sch_group_id, school_semesters_id})
    }
    const classes_group_id:any[] = classes.map( (theClass:any) => {return theClass.group_id })
    const classes_id:any[] = classes.map( (theClass:any) => {return theClass.id });

    const classes_teachers = await dbRawSeq.read(SQL_SC_TEACHERS, {sch_group_id})
    // todo: this seems redundant because we dont use the list of uids for the next query that gets called
    teachers = await dbRawSeq.read(SQL_TEACHER_DETAILS, {sch_group_id}, {isAvoidReplica: !isReadReplica})

    if(classes_group_id.length > 0){
      classes_invigilators = await dbRawSeq.read(SQL_SC_INVIGILATORS, {classes_group_id, sch_group_id})
      // we only pull regular students for the school admin views.
      classes_students = await dbRawSeq.read(SQL_SC_STUDENTS, {classes_group_id})
      // pulling and displaying guest student for school admin view as well.
      guest_classes_students = await dbRawSeq.read(SQL_SC_STUDENTS_GUESTS, {classes_group_id})
    }
    this.sanitizeClassStudents(classes_students, guest_classes_students);
    classes_students = classes_students.concat(guest_classes_students);
    const classes_students_uids:any[] = classes_students.map( (cs:any) => {return cs.uid });
    
    if(classes_students_uids.length>0){
      student_accommodations = await dbRawSeq.read(SQL_STU_ACCOMM_BY_UIDS, {classes_students_uids});
      students = await dbRawSeq.read(SQL_STU_INFO_BY_UIDS, {classes_students_uids});
      student_submissions = await dbRawSeq.read(SQL_STU_SUBMISSIONS, {classes_students_uids});
      student_testAttempts = await dbRawSeq.read(SQL_STU_ATT, {classes_students_uids});
      student_meta = await dbRawSeq.read(SQL_STU_META_BY_UIDS, {classes_students_uids}, {isAvoidReplica: !isReadReplica});
      all_tw_student_meta = await dbRawSeq.read(SQL_STU_META_ALL_BY_UIDS, {classes_students_uids});
      this.sanitizeTwStudentMetaAll(all_tw_student_meta, tw_student_meta, schoolFirst);
    }
    
    if(test_windows_ids.length > 0){
      school_sessions = await dbRawSeq.read(SQL_SCHL_SESSIONS(isStrictSessions), {test_windows_ids, sch_group_id});
      this.sanitizeSchoolSessions(school_sessions)
    }

    var test_attempts:any[]=[];
    if(classes_id.length > 0){
      test_attempts = await dbRawSeq.read(SQL_S_TW_TA, {test_windows_ids, sch_group_id});
      this.sanitizeTestAttempts(test_attempts, school_sessions)
    }
    // Responsibility Agreement
    const totalNonAcceptedRecords = await this.getUnacceptedTestWindows(test_windows, uid);

    let alt_version_format_values;
    let alt_version_information;

    if (isAdminAltReqAllowed(params?.headers?.host)){
      //Alternative Version Formats Values
      alt_version_format_values = await this.getAltVersionFormatValues();
      //Alternative Version Information
      const classes_students_id:any[] = classes_students.map( (cs:any) => {return cs.uid });
      if(test_windows_ids.length>0 && classes_students_id.length > 0){
        alt_version_information = await this.app.service('public/school-admin/student').getAltVersionInformation(classes_students_id,test_windows_ids);
      }
    }


    await this.validateStudents(sch_group_id, classes_students, classes, students, student_meta, uid, schoolFirst.is_sasn_login, schoolFirst.is_private, school_semesters); //check if the student data is valide
    const SDC_conflicts = await this.checkSDCConflict(sch_group_id, classes_students, classes, students, student_meta, uid , schoolFirst.is_sasn_login, schoolFirst.is_private, school_semesters, isReadReplica); //check if the student conflict SDC import
    const SDC_conflicts_g3 = SDC_conflicts.SDCconflicts_g3
    const SDC_conflicts_g6 = SDC_conflicts.SDCconflicts_g6
    const SDC_conflicts_g9 = SDC_conflicts.SDCconflicts_g9
    const SDC_conflicts_g10 = SDC_conflicts.SDCconflicts_g10

    await this.app.service('public/abed-pasi').setDiplomaExamInfo(students, sch_group_id);

    console.log('DB_BENCHMARK | FIND:public/school-admin/school | ', dbRawSeq.getBenchmarks())

    return [{
      test_windows,
      school,
      schl_dist,
      teachers,
      classes_invigilators,
      classes,
      classes_teachers,
      students,
      classes_students,
      student_meta,
      tw_student_meta,
      student_accommodations,
      school_semesters,
      test_attempts,
      student_testAttempts,
      student_submissions,
      SDC_conflicts_g3,
      SDC_conflicts_g6,
      SDC_conflicts_g9,
      SDC_conflicts_g10,
      school_extra_course_type,
      showPaymentAgreement,
      totalNonAcceptedRecords,
      dbBenchmarks: dbRawSeq.getBenchmarks(),
      alt_version_format_values : alt_version_format_values ?? undefined,
      alt_version_information: alt_version_information ?? undefined,
    }];
  }

  async get(id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create(data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }

  async getAltVersionFormatValues(){
    const alt_version_format_values = await dbRawWrite(this.app, [], `
       select avf.id as format_id
            , avf.format_name
            , avfv.id as format_value_id
            , avfv.format_value
            , avfv.format_value_label
            , avfv.format_value_order
            , avfv.is_g9
            , avfv.is_osslt_en
            , avfv.is_osslt_fr
            , avfv.is_primary
            , avfv.is_junior
        from alt_version_format_values avfv
        join alt_version_formats avf on avf.id = avfv.format_name_id
       where avfv.is_depleted = 0
         and avf.is_depleted = 0
    ;`)

    return alt_version_format_values
  }


  private async validateStudents(sch_group_id:any, classes_students:any, classes:any[], students:any, students_meta:any, created_by_uid:number, IsSASNLogin:any, isprivate:any, school_semesters:any){
    var new_errors:any[] = [];
    for (let student of students){
      let student_meta = students_meta.filter((record:any)=>  record.uid == student.id )
      let lang = 'en';
      const errMsg_g3 = student_meta.find( (meta_record:any) => (meta_record.key =='errMsg' && meta_record.key_namespace == 'eqao_sdc_g3'))
      const errMsg_g6 = student_meta.find( (meta_record:any) => (meta_record.key =='errMsg' && meta_record.key_namespace == 'eqao_sdc_g6'))
      const errMsg = student_meta.find( (meta_record:any) => (meta_record.key =='errMsg' && meta_record.key_namespace == 'eqao_sdc'))
      const errMsg_g10 = student_meta.find( (meta_record:any) => (meta_record.key =='errMsg' && meta_record.key_namespace == 'eqao_sdc_g10'))
      const g3Class = classes_students.find( (record:any) => record.uid == student.id && classes.find(theClass => theClass.group_id == record.group_id && theClass.group_type=='EQAO_G3')!=undefined)
      const g6Class = classes_students.find( (record:any) => record.uid == student.id && classes.find(theClass => theClass.group_id == record.group_id && theClass.group_type=='EQAO_G6')!=undefined)
      const g9Class = classes_students.find( (record:any) => record.uid == student.id && classes.find(theClass => theClass.group_id == record.group_id && theClass.group_type=='EQAO_G9')!=undefined)
      const g10Class = classes_students.find( (record:any) => record.uid == student.id && classes.find(theClass => theClass.group_id == record.group_id && theClass.group_type=='EQAO_G10')!=undefined)

      if((g3Class != undefined && (errMsg_g3 == undefined || errMsg_g3 == null))){
        const school_class_id_g3 = g3Class.group_id
        const student_meta_g3 = await this.convertGradeOnlyMeta(student_meta, 'G3');
        const validateData_g3 = await this.convertData(student, sch_group_id, lang, student_meta_g3, school_class_id_g3, classes, IsSASNLogin, isprivate, school_semesters)
        const validateResult_g3:any = await this.app
          .service('private/schools/student/validate')
          .create(validateData_g3);
        const newMeta1 = {uid:student.id, key_namespace:'eqao_sdc_g3', key:'errMsg', value:JSON.stringify(validateResult_g3.ErrorMessages),created_by_uid:created_by_uid, updated_by_uid:created_by_uid};
        const newMeta2 = {uid:student.id, key_namespace:'eqao_sdc_g3', key:'errMsg', value:JSON.stringify(validateResult_g3.ErrorMessages),created_by_uid:created_by_uid, updated_by_uid:created_by_uid};
        students_meta.push(newMeta1);
        new_errors.push(newMeta2);
      }

      if((g6Class != undefined && (errMsg_g6 == undefined || errMsg_g6 == null))){
        const school_class_id_g6 = g6Class.group_id
        const student_meta_g6 = await this.convertGradeOnlyMeta(student_meta, 'G6');
        const validateData_g6 = await this.convertData(student, sch_group_id, lang, student_meta_g6, school_class_id_g6, classes, IsSASNLogin, isprivate, school_semesters)
        const validateResult_g6:any = await this.app
          .service('private/schools/student/validate')
          .create(validateData_g6);
        const newMeta1 = {uid:student.id, key_namespace:'eqao_sdc_g6', key:'errMsg', value:JSON.stringify(validateResult_g6.ErrorMessages),created_by_uid:created_by_uid, updated_by_uid:created_by_uid};
        const newMeta2 = {uid:student.id, key_namespace:'eqao_sdc_g6', key:'errMsg', value:JSON.stringify(validateResult_g6.ErrorMessages),created_by_uid:created_by_uid, updated_by_uid:created_by_uid};
        students_meta.push(newMeta1);
        new_errors.push(newMeta2);
      }

      if((g9Class != undefined && (errMsg == undefined || errMsg == null))){
        const school_class_id_g9 = g9Class.group_id
        const student_meta_g9 = await this.convertGradeOnlyMeta(student_meta, 'G9');
        const validateData_g9 = await this.convertData(student, sch_group_id, lang, student_meta_g9, school_class_id_g9, classes, IsSASNLogin, isprivate, school_semesters)
        const validateResult_g9:any = await this.app
          .service('private/schools/student/validate')
          .create(validateData_g9);
        const newMeta1 = {uid:student.id, key_namespace:'eqao_sdc', key:'errMsg', value:JSON.stringify(validateResult_g9.ErrorMessages),created_by_uid:created_by_uid, updated_by_uid:created_by_uid};
        const newMeta2 = {uid:student.id, key_namespace:'eqao_sdc', key:'errMsg', value:JSON.stringify(validateResult_g9.ErrorMessages),created_by_uid:created_by_uid, updated_by_uid:created_by_uid};
        students_meta.push(newMeta1);
        new_errors.push(newMeta2);
      }

      if((g10Class != undefined && (errMsg_g10 == undefined || errMsg_g10 == null))){
        const school_class_id_g10 = g10Class.group_id
        const student_meta_g10 = await this.convertGradeOnlyMeta(student_meta, 'G10');
        const validateData_g10 = await this.convertData(student, sch_group_id, lang, student_meta_g10, school_class_id_g10, classes, IsSASNLogin, isprivate, school_semesters)
        const validateResult_g10:any = await this.app
          .service('private/schools/student/validate')
          .create(validateData_g10);
        const newMeta1 = {uid:student.id, key_namespace:'eqao_sdc_g10', key:'errMsg', value:JSON.stringify(validateResult_g10.ErrorMessages),created_by_uid:created_by_uid, updated_by_uid:created_by_uid};
        const newMeta2 = {uid:student.id, key_namespace:'eqao_sdc_g10', key:'errMsg', value:JSON.stringify(validateResult_g10.ErrorMessages),created_by_uid:created_by_uid, updated_by_uid:created_by_uid};
        students_meta.push(newMeta1);
        new_errors.push(newMeta2);
      }
    }
    if(new_errors.length > 0){
      await this.app.service('db/write/user-metas').create(new_errors);
    }
  }

  private async convertGradeOnlyMeta(student_meta:any[], grade:string){
    var returnMeta:any[] = [];
    student_meta.forEach( (metaRecord:any) => {
      if(metaRecord.key == 'IS_G3' ||metaRecord.key == 'IS_G6' ||metaRecord.key == 'IS_G9' || metaRecord.key == 'IS_G10'){
        return;
      }
      if(grade =='G3' && (metaRecord.key_namespace == 'eqao_sdc_g3'||this.overlapVariables.indexOf(metaRecord.key)!== -1)){
        returnMeta.push(metaRecord)
      }
      if(grade =='G6' && (metaRecord.key_namespace == 'eqao_sdc_g6'||this.overlapVariables.indexOf(metaRecord.key)!== -1)){
        returnMeta.push(metaRecord)
      }
      if(grade =='G9' && metaRecord.key_namespace == 'eqao_sdc'){
        returnMeta.push(metaRecord)
      }
      if(grade =='G10' && (metaRecord.key_namespace == 'eqao_sdc_g10'||this.overlapVariables.indexOf(metaRecord.key)!== -1)){
        returnMeta.push(metaRecord)
      }
    });
    if(grade == 'G3'){
      returnMeta.push({key:'IS_G3', value:'1'})
      returnMeta.push({key:'IS_G6', value:''})
      returnMeta.push({key:'IS_G9', value:''})
      returnMeta.push({key:'IS_G10', value:''})
    }
    if(grade == 'G6'){
      returnMeta.push({key:'IS_G3', value:''})
      returnMeta.push({key:'IS_G6', value:'1'})
      returnMeta.push({key:'IS_G9', value:''})
      returnMeta.push({key:'IS_G10', value:''})
    }
    if(grade == 'G9'){
      returnMeta.push({key:'IS_G3', value:''})
      returnMeta.push({key:'IS_G6', value:''})
      returnMeta.push({key:'IS_G9', value:'1'})
      returnMeta.push({key:'IS_G10', value:''})
    }
    if(grade == 'G10'){
      returnMeta.push({key:'IS_G3', value:''})
      returnMeta.push({key:'IS_G6', value:''})
      returnMeta.push({key:'IS_G9', value:''})
      returnMeta.push({key:'IS_G10', value:'1'})
    }
    return returnMeta;
  }

  async checkSDCConflict(sch_group_id:any, classes_students:any, classes:any[], students:any, students_meta:any, created_by_uid:number, IsSASNLogin:any, isprivate:any, school_semesters:any, isReadReplica:boolean){
    let completeValidateData_g3 :any[] = [];
    let completeValidateData_g6 :any[] = [];
    let completeValidateData_g9 :any[] = [];
    let completeValidateData_g10: any[] = [];
    logger.silly("student number: %s", students.length )
    var i = 0;
    for (let student of students){
      let lang = 'en';
      let student_meta = students_meta.filter((record:any)=>  record.uid == student.id )

      const g3Class = classes_students.find( (record:any) => record.uid == student.id && classes.find(theClass => theClass.group_id == record.group_id && theClass.group_type=='EQAO_G3')!=undefined)
      const g6Class = classes_students.find( (record:any) => record.uid == student.id && classes.find(theClass => theClass.group_id == record.group_id && theClass.group_type=='EQAO_G6')!=undefined)
      const g9Class = classes_students.find( (record:any) => record.uid == student.id && classes.find(theClass => theClass.group_id == record.group_id && theClass.group_type=='EQAO_G9')!=undefined)
      const g10Class = classes_students.find( (record:any) => record.uid == student.id && classes.find(theClass => theClass.group_id == record.group_id && theClass.group_type=='EQAO_G10')!=undefined)

      if(g3Class != undefined){
        const school_class_id_g3 = g3Class.group_id
        const student_meta_g3 = await this.convertGradeOnlyMeta(student_meta, 'G3');
        const validateData_g3 = await this.convertData(student, sch_group_id, lang, student_meta_g3, school_class_id_g3, classes, IsSASNLogin, isprivate, school_semesters)
        completeValidateData_g3.push(validateData_g3);
      }

      if(g6Class != undefined){
        const school_class_id_g6 = g6Class.group_id
        const student_meta_g6 = await this.convertGradeOnlyMeta(student_meta, 'G6');
        const validateData_g6 = await this.convertData(student, sch_group_id, lang, student_meta_g6, school_class_id_g6, classes, IsSASNLogin, isprivate, school_semesters)
        completeValidateData_g6.push(validateData_g6);
      }

      if(g9Class != undefined){
        const school_class_id_g9 = g9Class.group_id
        const student_meta_g9 = await this.convertGradeOnlyMeta(student_meta, 'G9');
        const validateData_g9 = await this.convertData(student, sch_group_id, lang, student_meta_g9, school_class_id_g9, classes, IsSASNLogin, isprivate, school_semesters)
        completeValidateData_g9.push(validateData_g9);
      }

      if(g10Class != undefined){
         const school_class_id_g10 = g10Class.group_id
         const student_meta_g10 = await this.convertGradeOnlyMeta(student_meta, 'G10');
         const validateData_g10 = await this.convertData(student, sch_group_id, lang, student_meta_g10, school_class_id_g10, classes, IsSASNLogin, isprivate, school_semesters)
        completeValidateData_g10.push(validateData_g10);
      }
    }
    //1. Search user_meta_imports to see if the student have import from eqao and not compared and saved in user_meta_imports_disc
    //2. Do the comparison and save the unsaved result in 1. into user_meta_imports_disc.
    await this.checkNewImport(completeValidateData_g3, completeValidateData_g6, completeValidateData_g9, completeValidateData_g10, isReadReplica);

    const students_id_g3 = completeValidateData_g3.map(ValidateData => {return Number(ValidateData["StudentID"])} )
    const students_id_g6 = completeValidateData_g6.map(ValidateData => {return Number(ValidateData["StudentID"])} )
    const students_id_g9 = completeValidateData_g9.map(ValidateData => {return Number(ValidateData["StudentID"])} )
    const students_id_g10 = completeValidateData_g10.map(ValidateData => {return Number(ValidateData["StudentID"])} )

    //3. Sent the unsolved result in sdc_compare_result to the stdent.
    let SDCconflicts_g3:any[] = [];
    if(students_id_g3.length > 0){
      SDCconflicts_g3 =  await dbRawWrite(this.app, [[students_id_g3]], `
      select umid1.*, umi.IsImportUpdate
        from user_metas_import_disc umid1
        join user_metas_imports umi
          on umi.id = umid1.user_metas_imports_id
       where umid1.uid in ?
          and class_type = 'eqao_g3'
          -- and umid1.created_on = (
          and umid1.id = (
            -- select max(umid2.created_on)
            select max(umid2.id)
            from user_metas_import_disc umid2
            where class_type = 'eqao_g3'
            and umid2.uid = umid1.uid
          );
      `);
      SDCconflicts_g3 = SDCconflicts_g3.filter(conflict => conflict.is_resolved == 0)
    }

    let SDCconflicts_g6:any[] = [];
    if(students_id_g6.length > 0){
      SDCconflicts_g6 =  await dbRawWrite(this.app, [[students_id_g6]], `
      select umid1.*, umi.IsImportUpdate
        from user_metas_import_disc umid1
        join user_metas_imports umi
          on umi.id = umid1.user_metas_imports_id
       where umid1.uid in ?
          and class_type = 'eqao_g6'
          -- and umid1.created_on = (
          and umid1.id = (
            -- select max(umid2.created_on)
            select max(umid2.id)
            from user_metas_import_disc umid2
            where class_type = 'eqao_g6'
            and umid2.uid = umid1.uid
          );
      `);
      SDCconflicts_g6 = SDCconflicts_g6.filter(conflict => conflict.is_resolved == 0)
    }

    let SDCconflicts_g9:any[] = [];
    if(students_id_g9.length > 0){
      SDCconflicts_g9 =  await dbRawWrite(this.app, [[students_id_g9]], `
      select umid1.*, umi.IsImportUpdate
        from user_metas_import_disc umid1
        join user_metas_imports umi
          on umi.id = umid1.user_metas_imports_id
       where umid1.uid in ?
          and class_type = 'eqao_g9'
          -- and umid1.created_on = (
          and umid1.id = (
            -- select max(umid2.created_on)
            select max(umid2.id)
            from user_metas_import_disc umid2
            where class_type = 'eqao_g9'
            and umid2.uid = umid1.uid
          );
      `);
      SDCconflicts_g9 = SDCconflicts_g9.filter(conflict => conflict.is_resolved == 0)
    }
    let SDCconflicts_g10:any[] = [];
    if(students_id_g10.length > 0){
      SDCconflicts_g10 =  await dbRawWrite(this.app, [[students_id_g10]], `
        select umid1.*, umi.IsImportUpdate
          from user_metas_import_disc umid1
          join user_metas_imports umi
            on umi.id = umid1.user_metas_imports_id
         where umid1.uid in ?
           and class_type = 'eqao_g10'
           -- and umid1.created_on = (select max(umid2.created_on)
           and umid1.id = (select max(umid2.id)
                                     from user_metas_import_disc umid2
                                    where class_type = 'eqao_g10'
                                      and umid2.uid = umid1.uid);
      `);
      SDCconflicts_g10 = SDCconflicts_g10.filter(conflict => conflict.is_resolved == 0)
    }
    return {SDCconflicts_g3, SDCconflicts_g6, SDCconflicts_g9, SDCconflicts_g10};
  }

  private async checkNewImport(completeValidateData_g3:any, completeValidateData_g6:any, completeValidateData_g9:any, completeValidateData_g10:any, isReadReplica:boolean ){
    const students_id_g3 = completeValidateData_g3.map((ValidateData:any) => {return Number(ValidateData["StudentID"])})
    const students_id_g6 = completeValidateData_g6.map((ValidateData:any) => {return Number(ValidateData["StudentID"])})
    const students_id_g9 = completeValidateData_g9.map((ValidateData:any) => {return Number(ValidateData["StudentID"])})
    const students_id_g10 = completeValidateData_g10.map((ValidateData:any) => {return Number(ValidateData["StudentID"])})
    var insertResults: any[]=[]
    if(students_id_g3.length == 0 && students_id_g6.length == 0 && students_id_g9.length == 0 && students_id_g10.length == 0 ){
      return;
    }

    //1.1 Search user_metas_imports for the 'lastest' imports
    let new_user_metas_imports_g3;
    let new_user_metas_imports_g6;
    let new_user_metas_imports_g9;
    let new_user_metas_imports_g10;
    if (isReadReplica) {
      new_user_metas_imports_g3 = await dbRawReadReporting(this.app, [students_id_g3.concat([0])], `
            select * from
              (select umi.*, umid.id as disc_id
                from user_metas_imports umi
          left join user_metas_import_disc umid
                  on umid.user_metas_imports_id = umi.id
              where umi.uid in (?)
                and umi.ClassCode is not null
            order by umi.id asc) b
              where b.disc_id is null;
      `);

      new_user_metas_imports_g6 = await dbRawReadReporting(this.app, [students_id_g6.concat([0])], `
            select * from
              (select umi.*, umid.id as disc_id
                from user_metas_imports umi
          left join user_metas_import_disc umid
                  on umid.user_metas_imports_id = umi.id
              where umi.uid in (?)
                and umi.ClassCode is not null
            order by umi.id asc) b
              where b.disc_id is null;
      `);

      new_user_metas_imports_g9 = await dbRawReadReporting(this.app, [students_id_g9.concat([0])], `
            select * from
              (select umi.*, umid.id as disc_id
                from user_metas_imports umi
          left join user_metas_import_disc umid
                  on umid.user_metas_imports_id = umi.id
              where umi.uid in (?)
                and umi.ClassCode is not null
            order by umi.id asc) b
              where b.disc_id is null;
      `);

      new_user_metas_imports_g10 = await dbRawReadReporting(this.app, [students_id_g10.concat([0])], `
                select * from
                (select umi.*, umid.id as disc_id
                  from user_metas_imports umi
            left join user_metas_import_disc umid
                    on umid.user_metas_imports_id = umi.id
                where umi.uid in (?)
                  and umi.Grouping is not null
              order by umi.id asc) b
                where b.disc_id is null;
      `);
    } else {
      new_user_metas_imports_g3 = await dbRawRead(this.app, [students_id_g3.concat([0])], `
            select * from
              (select umi.*, umid.id as disc_id
                from user_metas_imports umi
          left join user_metas_import_disc umid
                  on umid.user_metas_imports_id = umi.id
              where umi.uid in (?)
                and umi.ClassCode is not null
            order by umi.id asc) b
              where b.disc_id is null;
      `);

      new_user_metas_imports_g6 = await dbRawRead(this.app, [students_id_g6.concat([0])], `
            select * from
              (select umi.*, umid.id as disc_id
                from user_metas_imports umi
          left join user_metas_import_disc umid
                  on umid.user_metas_imports_id = umi.id
              where umi.uid in (?)
                and umi.ClassCode is not null
            order by umi.id asc) b
              where b.disc_id is null;
      `);

      new_user_metas_imports_g9 = await dbRawRead(this.app, [students_id_g9.concat([0])], `
            select * from
              (select umi.*, umid.id as disc_id
                from user_metas_imports umi
          left join user_metas_import_disc umid
                  on umid.user_metas_imports_id = umi.id
              where umi.uid in (?)
                and umi.ClassCode is not null
            order by umi.id asc) b
              where b.disc_id is null;
      `);

      new_user_metas_imports_g10 = await dbRawRead(this.app, [students_id_g10.concat([0])], `
                select * from
                (select umi.*, umid.id as disc_id
                  from user_metas_imports umi
            left join user_metas_import_disc umid
                    on umid.user_metas_imports_id = umi.id
                where umi.uid in (?)
                  and umi.Grouping is not null
              order by umi.id asc) b
                where b.disc_id is null;
      `);
    }
    const gradeImports: any[] = [];
    gradeImports.push({user_metas_imports:new_user_metas_imports_g3, complete_validate_data: completeValidateData_g3 })
    gradeImports.push({user_metas_imports:new_user_metas_imports_g6, complete_validate_data: completeValidateData_g6 })
    gradeImports.push({user_metas_imports:new_user_metas_imports_g9, complete_validate_data: completeValidateData_g9 })
    gradeImports.push({user_metas_imports:new_user_metas_imports_g10, complete_validate_data: completeValidateData_g10 })

    const genCheckingFields = () => ["FirstName","LastName","StudentType","StudentOEN","SASN","DateofBirth","Gender","LearningFormat","DateEnteredSchool","DateEnteredBoard","IndigenousType",
      "FirstLanguage","EnrolledOntario","OutOfProvinceResidence","StatusInCanada","Refugee","BornOutsideCanada","TimeInCanada","IEP","IPRCExceptionalities","AccAssistiveTech","AccBraille","AccAudioVersion",
      "AccBreaks","AccSign","AccAudioResponse","AccScribing","AccOther","SpecPermTemp","SpecPermMoved","LanguageLearner","SpecProvBreaks"
    ]

    const genPJCheckingFields = () => [
      'ClassCode','Grade','FrenchImmersion','ClassTeacherFirstName','ClassTeacherLastName','JrKindergarten','SrKindergarten','SpecEdNoExpectationMath',
      'ExemptionRead','ExemptionWrite','ExemptionMath','AccSignRead','AccSignWrite','AccSignMath','AccBrailleRead','AccBrailleWrite','AccBrailleMath',
      'AccAudioVersionRead','AccAudioVersionWrite','AccAudioVersionMath','AccAssistiveTechRead','AccAssistiveTechWrite','AccAssistiveTechMath',
      'AccScribingRead','AccScribingWrite','AccScribingMath','AccAudioResponseRead','AccAudioResponseWrite','AccAudioResponseMath'
    ]

    //2. Have sdc_import but no sdc_compare, do the comparison for new import data and save the result into sdc_compare_result.
    for(let gradeImport of gradeImports){
      for ( let new_import of gradeImport.user_metas_imports){
        var sdc_compare_result:any;
        var compar_result_value:any[] = [];
        const ValidateData = gradeImport.complete_validate_data.find((data:any) =>Number(data.StudentID)===new_import.uid)
        const namespace = ValidateData['Namespace'];
        const StudentID = ValidateData['StudentID'];
        let checkingFields = genCheckingFields()

        if(namespace == 'eqao_g3'||namespace == 'eqao_g6'){
          checkingFields = checkingFields.concat(genPJCheckingFields())
        }

        if(namespace == 'eqao_g9'){
          checkingFields.push("ClassCode","TermFormat")
        }
        if(namespace == 'eqao_g10'){
          checkingFields.push("Grouping","Homeroom","EligibilityStatus","TermFormat","LevelofStudyLanguage","DateOfFTE","Graduating","AccVideotapeResponse","SpecPermIEP","NonParticipationStatus")
        }

        const sdc_import_0 = new_import;

        //2.1 do the comparison for new import data
        checkingFields.forEach(field =>{
          var SDCData = sdc_import_0[field];
          if(SDCData == undefined || SDCData == null || SDCData == ''|| SDCData == 0){
            SDCData = '#'
          }
          var localData =  ValidateData[field];
          if(localData == undefined || localData == null || localData == '' || localData == 0){
            localData = '#'
          }
          if(SDCData!=localData){
            var obj:any = {};
            obj.fieldname = field;
            obj.value = SDCData;
            compar_result_value.push(obj)
          }
        })

        //2.2 save the result into sdc_compare_result
        sdc_compare_result = {
          uid: Number(StudentID),
          user_metas_imports_id:sdc_import_0.id,
          user_metas_import_created_date:sdc_import_0.CreatedDate|| moment(new Date(sdc_import_0.batch_timestamp)).format( 'YYYY-MM-DDTHH:mm:ss.sss-00:00'),
          compare_result:JSON.stringify(compar_result_value),
          class_type:namespace,
        }
        insertResults.push(sdc_compare_result)
      }
    }
    const createQuery = 'INSERT INTO user_metas_import_disc (uid, user_metas_imports_id, user_metas_import_created_date, compare_result, class_type) VALUES (?, ?, ?, ?, ?)'
    await dbRawWriteMulti(this.app, createQuery, insertResults, (entry) => {return [entry.uid, entry.user_metas_imports_id, entry.user_metas_import_created_date, entry.compare_result, entry.class_type]})
  }

  private async convertData(student:any, sch_group_id:any, lang:any, student_meta:any, school_class_id:any, classes:any, IsSASNLogin:any, isPrivate:any, school_semesters:any){
    var validateData:any = {};
    validateData['StudentID'] = String(student.id);
    validateData['SchGroupID'] = String(sch_group_id);
    validateData['IsPrivate'] = String(isPrivate);
    validateData['Language'] = lang;
    validateData['FirstName'] = student.first_name;
    validateData['LastName'] = student.last_name;
    validateData['IsSASNLogin'] = IsSASNLogin;

    for(let i =0; i < student_meta.length; i++){
      validateData[student_meta[i].key] = student_meta[i].value;
      if(validateData[student_meta[i].key] == '#' ||validateData[student_meta[i].key] == '0' ){
        validateData[student_meta[i].key] = '';
      }
    }
    if(validateData['IS_G3'] == '1' && validateData['IS_G6'] == '' && validateData['IS_G9'] == '' && validateData['IS_G10'] == ''){
      validateData['Namespace'] = 'eqao_g3';
    }
    if(validateData['IS_G3'] == '' && validateData['IS_G6'] == '1' && validateData['IS_G9'] == '' && validateData['IS_G10'] == ''){
      validateData['Namespace'] = 'eqao_g6';
    }
    if(validateData['IS_G3'] == '' && validateData['IS_G6'] == '' && validateData['IS_G9'] == '1' && validateData['IS_G10'] == ''){
      validateData['Namespace'] = 'eqao_g9';
    }
    if(validateData['IS_G3'] == '' && validateData['IS_G6'] == '' && validateData['IS_G9'] == '' && validateData['IS_G10'] == '1' ){
      validateData['Namespace'] = 'eqao_g10';
    }

    if(school_class_id && validateData['Namespace'] =='eqao_g10'){
      let grouping = classes.find( (record:any) => record.schl_group_id == Number(sch_group_id) &&  record.group_id == Number(school_class_id));
      if(grouping != undefined){
        validateData['Grouping'] = grouping.name
      }
    }
    if(school_class_id && (validateData['Namespace'] =='eqao_g3'||validateData['Namespace'] =='eqao_g6'||validateData['Namespace'] =='eqao_g9')){
      let ClassCode = classes.find( (record:any) => record.schl_group_id == Number(sch_group_id) &&  record.group_id == Number(school_class_id));
      if(ClassCode != undefined){
        validateData['ClassCode'] = ClassCode.name
      }
      if(ClassCode != undefined && validateData['Namespace'] =='eqao_g9'){
        const semester = school_semesters.find( (ss:any) => ss.id == ClassCode.semester_id)
        validateData['ClassTermFormat'] = ''+semester.foreign_id
      }
    }

    //*** End of convert student's data to business rules checking format ************************************************************/
    return validateData;
  }
}
