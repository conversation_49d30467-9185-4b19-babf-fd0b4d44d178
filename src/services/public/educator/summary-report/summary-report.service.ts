// Initializes the `summary-report` service on path `/src/services/public/educator/summary-report`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { SummaryReport } from './summary-report.class';
import hooks from './summary-report.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/summary-report': SummaryReport & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/summary-report', new SummaryReport(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/summary-report');

  service.hooks(hooks);
}
