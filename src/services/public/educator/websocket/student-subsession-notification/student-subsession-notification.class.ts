import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { WS_ROLE_TYPE } from '../connected-users/connected-users.class';

interface Data {}

interface ServiceOptions {}

export class StudentSubsessionNotification implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (params && data) {
      const {uids, available, caption, asmtSlug, subSessionSlug, classId, apiHost, isSubmitting} = <any> data;

      if (!uids || available === undefined || !caption || !asmtSlug || !subSessionSlug) {
        throw new Errors.BadRequest('ERR_MISSING_PARAMS');
      }

      // grab connected users for class
      const connectedUsers = await this.app
          .service('public/educator/websocket/connected-users')
          .getConnectedUsers(classId, WS_ROLE_TYPE.STUDENT) // Get all connected users for the class
          .then((users) => users.filter((user) => uids.includes(user.uid))); // Filter out users that are not in the uids array

      const responseData = {
        eventType: 'notifyAssessmentAvailable',
        available,
        caption,
        asmtSlug,
        subSessionSlug,
        isSubmitting
      }
      
      // notify assessment available to students
      const sendSSMessages = connectedUsers.map((user) => {
        return this.app.service('public/educator/websocket/connected-users')
          .sendToConnectionId(responseData, user);
      });

      try {
        await Promise.all(sendSSMessages);
      } catch(e: any) {
        return { statusCode: 500, body: e.stack };
      }
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
