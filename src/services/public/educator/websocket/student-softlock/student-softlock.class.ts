import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { WS_ROLE_TYPE } from '../connected-users/connected-users.class';
import { isRecentlyUnpaused } from '../../../../../util/time-control'
import { ITestAttempt } from '../../../../db/schemas/test_attempts.schema'
import { dbRawReadSingle } from '../../../../../util/db-raw'

interface Data {}

interface ServiceOptions {}

export class StudentSoftlock implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (params && data) {
      const {stageIndex, questionCaption, apiHost, classId, softLock, submitConfig, numTimesOffScreen, reentryPause, test_attempt_id} = <any> data;

      const uid = await currentUid(this.app, params);

      if (!apiHost || !classId) {
        throw new Errors.BadRequest('ERR_MISSING_PARAMS');
      }

      let responseData = {
        eventType: 'updateStudentPosition',
        uid,
        stageIndex,
        questionCaption,
        softLock,
        submitConfig,
        numTimesOffScreen
      };

      if (reentryPause && softLock === 1 && test_attempt_id) {
        const attemptRecord:ITestAttempt = await dbRawReadSingle(this.app, {test_attempt_id}, `
          select unpaused_on
          from test_attempts
          where id = :test_attempt_id
        `)

        if (attemptRecord && isRecentlyUnpaused(attemptRecord.unpaused_on)) {
          delete responseData.softLock;
        }
      }

      // grab connected teacherse for class
      const connnectedTeachers = await this.app
        .service('public/educator/websocket/connected-users')
        .getConnectedUsers(classId, WS_ROLE_TYPE.EDUCATOR);

      // notify teachers that student navigated off screen
      const sendSSMessages = connnectedTeachers.map((user) => {
        return this.app.service('public/educator/websocket/connected-users')
          .sendToConnectionId(responseData, user, false);
      });

      try {
        await Promise.all(sendSSMessages);
      } catch(e: any) {
        return { statusCode: 500, body: e.stack };
      }
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
