import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Redis } from 'ioredis';
import { Knex } from 'knex';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbDateNow } from '../../../../../util/db-dates';
import AWS from 'aws-sdk';
import { getSysConstNumeric } from '../../../../../util/sys-const-numeric';
import { REDIS_ASSESSMENT_DATA_EXPIRE } from '../../../../../redis/redis';
import { IWebsocketConfig } from '../../../../../types/app-types';
import logger from '../../../../../logger';

interface Data {}

interface ServiceOptions {}

const AWS_CONFIG = {
  accessKeyId: '********************',
  secretAccessKey: 'Czd414/T9Agn0+d4P2xo1Z6ejQxNdaURBdf6WmAF',
};

export interface ISocketConnection {
  connection_id: string;
  uid: string;
  class_id: string;
  api_host: string;
  role_type: WS_ROLE_TYPE;
  created_on?: number;
  source_ip?: string,
}

export enum WS_ROLE_TYPE {
  STUDENT = 'student',
  EDUCATOR = 'educator'
}

export class ConnectedUsers implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  private apigwManagementApi: AWS.ApiGatewayManagementApi;
  private wsSecretPass: string;
  private websocketUrl: string;
  private wsGracePeriod: number;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;

    const wsConfig: IWebsocketConfig = this.app.get('websockets');
    this.websocketUrl = wsConfig.invigilation.url;
    this.wsSecretPass = wsConfig.invigilation.secretPass;
    this.wsGracePeriod = wsConfig.invigilation.gracePeriod;

    this.apigwManagementApi = new AWS.ApiGatewayManagementApi({
      apiVersion: '2018-11-29',
      endpoint: this.websocketUrl,
      region: wsConfig.invigilation.region,
      ...AWS_CONFIG,
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data | Paginated<Data>> {
    return {
      websocketUrl: this.websocketUrl,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(class_id: Id, params?: Params): Promise<Data> {
    if (!params || !params.query || !class_id) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS_AND_ID');
    }
    const { secretPass, userType, apiHost } = <any>params.query;

    if (!secretPass || secretPass !== this.wsSecretPass) {
      throw new Errors.Forbidden('ERR_REQUIRES_PASS');
    }

    const connectedUsers = await this.getConnectedUsers(class_id, userType, apiHost);

    return connectedUsers;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: Data, params?: Params): Promise<Data> {
    if (!params || !data) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS_BODY');
    }

    const { connectionId, uid, classId, userType, apiHost, secretPass, source_ip } = <any>data;

    if (!secretPass || secretPass !== this.wsSecretPass) {
      throw new Errors.Forbidden('ERR_REQUIRES_PASS');
    }

    if (!connectionId || !uid || !classId || !userType || !apiHost) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    if (userType === WS_ROLE_TYPE.STUDENT) {
      if(this.app.get('context').isEQAO || this.app.get('context').isABED) {
        const ENABLE_WS_MULTITAB_BLOCK = await getSysConstNumeric(this.app, 'ENABLE_WS_MULTITAB_BLOCK')
  
        if(ENABLE_WS_MULTITAB_BLOCK) {
          this.forceLogout(uid, classId, apiHost, source_ip)
        }
      }

      await this.revokeStaleConnections(uid, classId);
    }

    return this.addConnectionID(connectionId, uid, classId, userType, apiHost, source_ip);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(class_id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (!class_id) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    this.pingOnlineStudents(class_id)
      .then(() => {
        this.updateConnectedTeachers(class_id)
      })
    
    return {};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  public async sendToConnectionId(messageData: any, userConnectionRec: ISocketConnection, updateTeachers = true) {
    return this.apigwManagementApi
        .postToConnection({ ConnectionId: userConnectionRec.connection_id, Data: JSON.stringify(messageData) })
        .promise()
        .catch((e: any) => {
          if (e.statusCode === 410) {
            logger.info('WS_STALE_CONNECTION', { connection: userConnectionRec })
            this.deleteConnectionID(userConnectionRec.connection_id);
            this.apigwManagementApi.deleteConnection({ ConnectionId: userConnectionRec.connection_id })
              .promise()
              .catch((e: any) => {
                logger.info('WS_ALREADY_DELETED', { connection: userConnectionRec })
              });

            if (updateTeachers && userConnectionRec.role_type === WS_ROLE_TYPE.STUDENT) {
              this.updateConnectedTeachers(userConnectionRec.class_id);
            }
            return;
          }
          throw e;
        });
  }

  private async pingOnlineStudents(class_id: Id) {
    const studentsOnline = await this.getConnectedUsers(class_id, WS_ROLE_TYPE.STUDENT);

    const pingStudentPromise = studentsOnline.map((student) => {
      return this.sendToConnectionId(
        {
          eventType: 'ping',
        },
        student, false
      );
    });

    return Promise.all(pingStudentPromise);
  }

  /**
   * Update all connected teachers with the list of currently connected students
   */
  private async updateConnectedTeachers(class_id: Id) {
    const studentsOnline = await this.getConnectedUsers(class_id, WS_ROLE_TYPE.STUDENT);
        
    return this.sendToAllTeachers(class_id, {
      eventType: 'updateConnectedStudents',
      connectedStudents: studentsOnline,
    });
  }
    
  /**
   * Sends a message to all teachers in a given class
   * @param class_id class **group** ID
   * @returns single Promise for all sends
   */
  public async sendToAllTeachers(class_id: Id, messageData: any) {
    const teachersOnline = await this.getConnectedUsers(class_id, WS_ROLE_TYPE.EDUCATOR);
  
    const messageTeachersPromises = teachersOnline.map((teacher) => this.sendToConnectionId(messageData, teacher, false));
  
    return Promise.all(messageTeachersPromises);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<Data> {
    if(!id || !params?.query) {
      throw new Errors.BadRequest('ERR_PARAMS_MISSING')
    }

    const { userType, connectionId, classId } = params.query;

    if(!userType || !connectionId || !classId) {
      throw new Errors.BadRequest('ERR_PARAMS_MISSING')
    }

    // revoke student's socket given the id of the realtime_class_users table
    await this.deleteConnectionID(connectionId);

    if (userType === WS_ROLE_TYPE.STUDENT) { // send the updated students online list to teachers
      this.updateConnectedTeachers(classId);
    }
    return { id };
  }

  /**
   * Creates a new connection record
   * Falls back to SQL DB if Redis is not available
   * Redis:
   *  - TTL is equal to REDIS_TTL (1 day)
   *    - Sets TTL on :connection_id
   *    - Sets/Renews TTL for the class array :class_id:role_type
   *  - Stores the connection record as a stringified JSON object
   *  - Stores all connected students/educators (for a class) in a set
   * 
   */
  private async addConnectionID(connection_id: string, uid: string, class_id: string, role_type: WS_ROLE_TYPE, api_host: string, source_ip: string) {
    const newConnection: ISocketConnection = {
      connection_id,
      uid,
      class_id,
      api_host,
      role_type,
      source_ip
    }
    const redis: Redis = this.app.get('redis');
    if(!redis) { // SQL DB Fallback
      return this.app.service('db/write/realtime-class-users').create({
        ...newConnection,
        last_updated_on: dbDateNow(this.app),
      }) as Promise<ISocketConnection>;
    }

    await redis.pipeline()
      .sadd(`class:${class_id}:${role_type}`, connection_id)
      .expire(`class:${class_id}:${role_type}`, REDIS_ASSESSMENT_DATA_EXPIRE)
      .sadd(`user:${uid}:connections`, connection_id)
      .expire(`user:${uid}:connections`, REDIS_ASSESSMENT_DATA_EXPIRE)
      .set(connection_id, JSON.stringify({
        ...newConnection,
        created_on: Date.now(),
      }), 'EX', REDIS_ASSESSMENT_DATA_EXPIRE)
      .exec();

    return newConnection;
  }

  /**
   * Delete a connection ID from the realtime_class_users table / redis
   * Falls back to SQL DB if redis is not available
   */
  private async deleteConnectionID(connectionId: string) {
    const redis: Redis = this.app.get('redis');
    if(!redis) { // SQL DB Fallback
      return this.app
        .service('db/write/realtime-class-users')
        .db()
        .where('connection_id', connectionId)
        .where('is_revoked', 0)
        .update({
          is_revoked: 1,
          last_updated_on: dbDateNow(this.app),
        });
    }

    const connectionRec = await redis.get(connectionId);
    if (connectionRec) {
      const { uid, class_id } = JSON.parse(connectionRec);

      await redis.pipeline()
        .srem(`class:${class_id}:${WS_ROLE_TYPE.STUDENT}`, connectionId)
        .srem(`class:${class_id}:${WS_ROLE_TYPE.EDUCATOR}`, connectionId)
        .srem(`user:${uid}:connections`, connectionId)
        .del(connectionId)
        .exec();
    }
  }

  /**
   * Find and delete all previous connections for a given user and class
   * Needed to clean up stale connections
   */
  private async revokeStaleConnections(uid: string, class_id: string) {
    const redis: Redis = this.app.get('redis');
    if(!redis) {
      const usersToRemove = await this.app.service('db/read/realtime-class-users').db()
        .select('id')
        .where('uid', uid)
        .where('class_id', class_id)
        .where('is_revoked', 0)
        .where('role_type', WS_ROLE_TYPE.STUDENT);
      if(usersToRemove.length) {
        await this.app.service('db/write/realtime-class-users').db()
          .whereIn('id', usersToRemove.map((u: any) => u.id))
          .update('is_revoked', 1)
          .update('last_updated_on', dbDateNow(this.app))
      }
      return;
    }

    const staleConnectionIds = await redis.smembers(`user:${uid}:connections`);
    if(staleConnectionIds.length === 0) {
      return;
    }

    const staleConnections = (await redis.mget(staleConnectionIds))
      .filter((connection) => connection !== null)
      .map((connection) => JSON.parse(connection!))
      .filter((connection: ISocketConnection) => {
        if(!connection.created_on) {
          return true;
        }
        return Date.now() - connection.created_on > this.wsGracePeriod;
      }) as ISocketConnection[];
      
    if (staleConnections.length) {
      const staleConnectionPromises = staleConnections.map((connection) => {
        return this.deleteConnectionID(connection.connection_id);
      });
      await Promise.all(staleConnectionPromises);
    }
  }

  /**
   * Force logout any existing connections for a given user and class
   */
  async forceLogout(uid: Id, class_id: Id, apiHost: string, source_ip = '') {
    const SINGLE_IP_MULTITAB_BLOCK = await getSysConstNumeric(this.app, 'SINGLE_IP_MULTITAB_BLOCK').catch(() => true)

    const connectedUsers = await this.getConnectedUsers(class_id, WS_ROLE_TYPE.STUDENT, apiHost);
    const oldConnections = connectedUsers.filter((user) => user.uid == uid);

    if (oldConnections.length) {
      const oldConnectionPromises = oldConnections.map((connection) => {
        // Do not disconnect if it's the same IP address
        if(SINGLE_IP_MULTITAB_BLOCK && source_ip?.length && connection?.source_ip?.length && connection.source_ip == source_ip) {
          return undefined;
        }

        return this.sendToConnectionId({eventType: 'forceLogout'}, connection, false);
      });
      await Promise.all(oldConnectionPromises);
    }
  }

  /**
   * Get list of connected users for a given class and role
   * Falls back to SQL DB if Redis is not available
   * @returns {ISocketConnection[]} - list of connected users
   */
  public async getConnectedUsers(class_id: Id, role_type: WS_ROLE_TYPE, api_host?: string): Promise<ISocketConnection[]> {
    const redis: Redis = this.app.get('redis');
    if(!redis) { // SQL DB Fallback
      return this.app
        .service('db/read/realtime-class-users')
        .db()
        .modify((qb: Knex.QueryBuilder) => { // limit
          if(role_type) qb.where('role_type', role_type)
          if(api_host)  qb.where('api_host', api_host)
        })
        .where('class_id', class_id)
        .where('is_revoked', 0);
    }

    const onlineUsers = await redis.smembers(`class:${class_id}:${role_type}`);
    if(!onlineUsers.length) {
      return [];
    }
    
    const onlineUserConnections = await redis.mget(onlineUsers);
    return onlineUserConnections
      .filter((connection) => connection !== null)
      .map((connection) => JSON.parse(connection!))
  }

}