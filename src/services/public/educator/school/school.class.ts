import { IDBSchoolClass_View1, IDBUmCaptions } from './model';
import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { Knex } from 'knex';
import { currentUid } from '../../../../util/uid';
import { dbRawRead } from '../../../../util/db-raw';
import { getSysConstString } from '../../../../util/sys-const-string'
import { getSysConstMdTranslation } from '../../../../util/sys-const-md-translations'
import { getTwTypeSlugs } from '../../school-admin/school/school.class';
import { isABED } from '../../../../util/whiteLabelParser';
import { MAX_SC_INVIGILATORS } from './constants';
import { SQL_SCHL_STU, SQL_TW_USER_META } from './model/sql';
import {
  SQL_ACADEMIC_YEARS_BY_CODE,
  SQL_SA_TEST_WINDOWS,
  SQL_SA_TEST_WINDOWS_ASSOC,
  SQL_SCHOOL_ASSESSMENT_DEFS
} from './queries';

interface Data {}

interface ServiceOptions {}

export class School implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  /**
   * Retrieves test windows filtered by school's assessment mappings.
   * This method ensures that educators only see test windows for assessments
   * that their school has access to via the school_assessments_map table.
   *
   * @param schoolId - The school's database ID (not group_id)
   * @param schl_group_id - The school's group ID for legacy test access queries
   * @param isAllowQaWindows - Whether to include QA test windows
   * @param TW_TYPE_SLUGS - Array of test window type slugs to filter by
   * @returns Promise<any[]> - Array of filtered test windows with extra test windows merged
   */
  private async getFilteredTestWindows(
    schoolId: number,
    schl_group_id: number,
    isAllowQaWindows: boolean,
    TW_TYPE_SLUGS: string[]
  ): Promise<any[]> {
    // 1. Get school profile
    const school_assessment_defs = await dbRawRead(this.app, { school_id: schoolId }, SQL_SCHOOL_ASSESSMENT_DEFS);
    const school_assessment_slugs = school_assessment_defs.map(ad => ad.assessment_slug);

    // 2. Get base test windows
    let test_windows = await dbRawRead(this.app, [TW_TYPE_SLUGS], SQL_SA_TEST_WINDOWS(isAllowQaWindows));

    // 3. Get additional test windows from school_test_access (legacy code)
    const extra_test_windows = await dbRawRead(this.app, { schl_group_id }, SQL_SA_TEST_WINDOWS_ASSOC);
    extra_test_windows.forEach(test_window => {
      const existingWindow = test_windows.find((tw: any) => tw.id === test_window.id);
      if (!existingWindow) {
        test_windows.push(test_window);
      }
    });

    // 4. Filter test windows based on school's assessment mappings
    if (school_assessment_slugs.length > 0) {
      test_windows = test_windows.filter(tw => {
        return school_assessment_slugs.some(slug =>
          tw.type_slug.includes(slug)
        );
      });
    }

    return test_windows;
  }



  async find (params?: Params): Promise<Data[] | Paginated<Data>> {

    if (!params){
      throw new Errors.BadRequest();
    }

    const db:Knex = this.app.get('knexClientReadReporting');
    const getData = async (props: any[], query: string) => {
      const res = await db.raw(query, props);
      return <any[]>res[0];
    }

    // get the teacher's first school
    const uid = await currentUid(this.app, params);
    const whiteLabel = this.app.get('whiteLabel');

    let { schl_group_id} = (<any>params).query;
    let school: any
    const TW_TYPE_SLUGS = await getTwTypeSlugs(this.app);
    if(schl_group_id){
      school = await dbRawRead(this.app, [uid, schl_group_id], `
        select s.*
            , sd.id sd_id
            , sd.group_id sd_group_id
            , sd.name sd_name
            , sd.foreign_id sd_foreign_id
        from schools s
        join school_districts sd on sd.group_id = s.schl_dist_group_id
        join user_roles ur
          on ur.group_id = s.group_id
          and uid = ?
          and ur.is_revoked != 1
          and s.group_id = ?
      `);
    }else{
      school = await dbRawRead(this.app, [uid], `
        select s.*
            , sd.id sd_id
            , sd.group_id sd_group_id
            , sd.name sd_name
            , sd.foreign_id sd_foreign_id
        from schools s
        join school_districts sd on sd.group_id = s.schl_dist_group_id
        join user_roles ur
          on ur.group_id = s.group_id
          and uid = ?
          and ur.is_revoked != 1
      `);
    }
    if (school.length === 0){
      throw new Errors.NotFound('NO_SCHOOL_ROLE_FOR_USER');
    }

    const schl_dist =await dbRawRead(this.app, [school[0].sd_group_id], `
      SELECT * FROM mpt_dev.school_districts where group_id = ?;
    `)
    const isAllowQaWindows = (+schl_dist[0].is_sample == 1);

    schl_group_id = school[0].group_id

    // Get filtered test windows based on school's assessment mappings
    const test_windows = await this.getFilteredTestWindows(
      school[0].id,
      schl_group_id,
      isAllowQaWindows,
      TW_TYPE_SLUGS
    );

    const academicYearCodes:number[] = test_windows.map( tw => tw.academic_year );
    const test_windows_id:number[] = test_windows.map( tw => tw.id );

    const academic_years = await dbRawRead(this.app, {academicYearCodes}, SQL_ACADEMIC_YEARS_BY_CODE)

    const test_window_td_alloc_rules = await getData([test_windows_id], `
      select
        twtar.test_window_id,
        twtar.type_slug as asmt_type_slug,
        twtar.slug as asmt_slug,
        twtar.assessment_def_id,
        twtar.is_field_test,
        twtar.lang,
        twtar.test_duration,
        twtar.test_date_start
      from test_window_td_alloc_rules twtar
      join test_windows tw
        on tw.id = twtar.test_window_id
      where twtar.test_window_id in (?)
    `);

    test_windows.map((test_window) => {
      test_window.assessments = test_window_td_alloc_rules.filter((twtarEntry) => {
        return twtarEntry.test_window_id === test_window.id
      })
    });

    var school_semesters  :any[] = [];
    if(test_windows_id.length > 0){
      school_semesters = await dbRawRead(this.app,[[test_windows_id]], `
      select id, foreign_scope_id, foreign_id, name, test_window_id from school_semesters where test_window_id in ?;
    `);
    }

    const sch_group_id = school[0].group_id;

    const teachers = await dbRawRead(this.app, [uid], `
      select u.id, u.first_name, u.last_name, u.contact_email
      from user_roles urt
      join users u
        on u.id = urt.uid
        and urt.role_type = 'schl_teacher'
        and urt.is_revoked != 1
      where u.id = ?
      group by u.id
    ;`)

    const school_semesters_id:any[] = school_semesters.map( (semester:any) => {return semester.id });
    const classes_teachers = await dbRawRead(this.app, [school_semesters_id, uid, sch_group_id], `
      select sc.group_id, urt.uid, urt.role_type
      from mpt_dev.school_classes sc
      join mpt_dev.school_semesters sm
        on sm.id = sc.semester_id
       and sm.id in (?)
      join mpt_dev.user_roles urt
        on urt.group_id = sc.group_id
        and urt.uid = ?
        and urt.role_type in ('schl_teacher', 'schl_teacher_invig')
        and urt.is_revoked != 1
      where sc.schl_group_id = ?
        and sc.is_active = 1
    ;`);


    const classGroupIds = classes_teachers.map(entry => entry.group_id)
    let classes:IDBSchoolClass_View1[] = []; // overridden below
    let studentIdentifierCaptions:IDBUmCaptions[] = []; // overridden below
    let classes_invigilators
    let guest_classes;
    let classIds;
    let classes_sessions;
    let classes_students
    let guest_classes_students;
    let guest_classes_invigilators;
    let students;
    let student_meta
    let tw_student_meta: any[] = [];
    let classes_closed_sessions
    let student_attempt_info
    if (classGroupIds.length > 0){
      classes = await dbRawRead(this.app, {classGroupIds}, `
        select
            sc.id
          , sc.group_id
          , sc.schl_group_id
          , sc.schl_dist_group_id
          , sc.name
          , sc.access_code
          , sc.notes
          , sc.group_type
          , sc.is_grouping
          , sc.semester_id
          , ss.test_window_id
          , sc.is_placeholder
          , sc.is_fi
          , sc.is_archived
          , sc.foreign_id
          , sc.key student_identifier_key
          , ac.course_name_short
          , ac.course_name_full
        from school_classes sc
        inner join school_semesters ss on ss.id = sc.semester_id
        left join assessment_courses ac -- todo:dbmodel need db schema sync for other environments, but safe because left join
          on ac.course_code_foreign = sc.foreign_id
        where sc.group_id IN (:classGroupIds)
          and sc.is_active = 1
        GROUP BY sc.id
      ;`); // TODO fix confitional GROUP BY

      // get usermeta naming info
      // todo:move to a more generalized function that takes a list of user meta keys and gives language
      const studentIdentifierKeys = ['-1'].concat(...new Set(classes.map(sc => sc.student_identifier_key)))
      studentIdentifierCaptions = await dbRawRead(this.app, {studentIdentifierKeys}, `
        select
            umfc.um_key
          , umfc.lang
          , umfc.use_case_slug
          , umfc.caption
        from user_meta_field_captions umfc
        where um_key in (:studentIdentifierKeys)
      `)

      //get allow_ISR info
      if(schl_group_id && test_windows_id.length > 1){
        const allowISRGroupIds = await dbRawRead(this.app, [classGroupIds, schl_group_id, test_windows_id], `
          select distinct sc.group_id
            from school_test_access sta
            join school_semesters ss on ss.test_window_id = sta.test_window_id
            join school_classes sc on sc.group_id in (?) and sc.semester_id = ss.id
           where sta.school_group_id = ?
             and sta.test_window_id in (?)
             and sta.is_revoked != 1
             and sta.allow_ISR = 1
        ;`);

        allowISRGroupIds.forEach ( allowISRGroupId => {
          const targetClass = classes.find( (aClass:any) =>  aClass.group_id == allowISRGroupId.group_id);
          if (targetClass){
            targetClass.allow_ISR = 1
          }
        })
      }

      // get all other teachers in the school and see if any is an invigilator of any classes
      classes_invigilators = await dbRawRead(this.app, [classGroupIds, sch_group_id, uid], `
          select ur.uid, u.contact_email, u.first_name, u.last_name, ur2.group_id
            from user_roles ur
            join users u on u.id = ur.uid
       left join user_roles ur2 on ur2.uid = ur.uid and ur2.is_revoked != 1 and ur2.role_type = 'schl_teacher_invig' and ur2.group_id in (?)
           where ur.role_type ='schl_teacher'
             and ur.is_revoked != 1
             and ur.group_id = ?
             and ur.uid != ?
        group by ur.uid, ur2.group_id
        limit ${MAX_SC_INVIGILATORS}
      ;`);

      guest_classes_invigilators = await dbRawRead(this.app, [classGroupIds], `
        select scg.invig_sc_group_id as group_id, urt.uid, scg.id as scg_id, urs.first_name, urs.last_name
        from school_classes_guest scg
        join mpt_dev.school_classes sc on sc.group_id = scg.guest_sc_group_id and sc.is_active = 1
        join mpt_dev.user_roles urt on urt.group_id = sc.group_id and urt.role_type = 'schl_teacher' and urt.is_revoked != 1
        join mpt_dev.users urs on urt.uid = urs.id
        where scg.invig_sc_group_id IN (?)
          and scg.is_revoked != 1
      ;`);

      guest_classes_invigilators.forEach(gci => gci.is_guest = 1);

      classIds = classes.map((entry:any) => entry.id)
      guest_classes = await dbRawRead(this.app, [classGroupIds], `
        select
        sc.id,
        sc.group_id,
        sc.schl_group_id,
        sc.schl_dist_group_id,
        sc.name,
        sc.access_code,
        sc.notes,
        sc.group_type,
        sc.is_grouping,
        sc.semester_id,
        ss.test_window_id,
        sc.is_placeholder,
        scg.id as scg_id,
        scg.invig_sc_group_id
          from school_classes_guest scg
          join school_classes sc on sc.group_id = scg.invig_sc_group_id and sc.is_active = 1
          inner join school_semesters ss on ss.id = sc.semester_id
         where scg.invig_sc_group_id in (?)
           and scg.is_revoked != 1
      ;`);

      classes_sessions = await dbRawRead(this.app, [classIds], `
      select scts.school_class_id
           , scts.test_session_id
           , scts.slug
           , scts.caption
           , twtt.caption_short  asmt_design_caption
           , ac.course_name_short
           , ac.course_name_full
           , ts.name_custom
           , ts.date_time_start
           , ts.access_code
           , ts.is_score_entry
           , twtdar.test_design_id
      from school_class_test_sessions scts
      join test_sessions ts on ts.id = scts.test_session_id
      left join test_window_td_types twtt
      	on twtt.type_slug = scts.slug
      	and twtt.test_window_id is null
          and twtt.is_revoked = 0
      left join assessment_courses ac
      	on ac.course_code = twtt.course_code
      left join test_window_td_alloc_rules twtdar 
        on twtdar.test_window_id = ts.test_window_id 
        and twtdar.slug = scts.slug
      where scts.school_class_id IN (?)
        and ts.is_cancelled = 0
        and ts.is_closed = 0
        GROUP BY scts.test_session_id
      ;`);
      classes_closed_sessions = await dbRawRead(this.app, [classIds], `
        select scts.school_class_id
             , scts.test_session_id
             , scts.slug
             , scts.caption
             , twtt.caption_short asmt_design_caption
             , ac.course_name_short
             , ac.course_name_full
             , ts.name_custom
             , ts.date_time_start
             , ts.closed_on
             , ts.access_code
             , ts.is_score_entry
             , twtdar.test_design_id
        from school_class_test_sessions scts
        join test_sessions ts on ts.id = scts.test_session_id
        left join test_window_td_types twtt
          on twtt.type_slug = scts.slug
          and twtt.test_window_id is null
          and twtt.is_revoked = 0
        left join assessment_courses ac
          on ac.course_code = twtt.course_code
        left join test_window_td_alloc_rules twtdar 
          on twtdar.test_window_id = ts.test_window_id 
          and twtdar.slug = scts.slug
        where scts.school_class_id IN (?)
          and ts.is_cancelled = 0
          and ts.is_closed = 1
        GROUP BY scts.test_session_id
        ORDER BY ts.date_time_start DESC
      ;`);
      
      // Add data on whether session has scans and the scan upload progress
      // await this.app.service('public/school-admin/school').getIsSessionScan(classes_sessions)
      // await this.app.service('public/school-admin/school').getIsSessionScan(classes_closed_sessions)

      classes_students = await dbRawRead(this.app, [classGroupIds], `
        select sc.group_id, urt.uid
        from mpt_dev.school_classes sc
        join mpt_dev.user_roles urt on urt.group_id = sc.group_id and urt.role_type = 'schl_student' and urt.is_revoked != 1
        where sc.group_id IN (?)
          and sc.is_active = 1
      ;`);

      guest_classes_students = await dbRawRead(this.app, [classGroupIds], `
        select scg.invig_sc_group_id as group_id, urt.uid, scg.id as scg_id
        from school_classes_guest scg
        join mpt_dev.user_roles urt on urt.group_id = scg.guest_sc_group_id and urt.role_type = 'schl_student' and urt.is_revoked != 1
        where scg.invig_sc_group_id IN (?)
          and scg.is_revoked != 1
      ;`);
      classes_students.forEach(cs => {
        cs.is_guest = 0
        cs.scg_id = -1
      })
      guest_classes_students.forEach(gcs => gcs.is_guest = 1)

      classes_students = classes_students.concat(guest_classes_students)

      const studentIds = classes_students.map(entry => entry.uid);

      if (studentIds.length > 0){
        students = await dbRawRead(this.app, [studentIds], `
        SELECT
          u.id id,
          u.first_name first_name,
          u.middle_name middle_name,
          u.last_name last_name,
          u.is_PASI_student,
          tsp.purchase_method_id purchase_method_id,
          (CASE
            WHEN (tsp.alternative_status IS not null)
              THEN tsp.alternative_status
            ELSE 0
          END
          ) altPaymentStatus,
          (CASE
            WHEN (tsp.alternative_status = 3)
              THEN 1
            ELSE 0
            END
          ) isPaid
        FROM users u
        LEFT JOIN student_attempt_purchases sap
               ON u.id = sap.uid
              AND sap.is_revoked != 1
              AND (sap.is_refunded != 1 OR sap.is_refunded IS null)
        LEFT JOIN test_session_purchases tsp
               ON sap.ts_purchase_id = tsp.id
              AND tsp.is_revoked != 1
        LEFT JOIN student_attempt_purchases sap2
               ON u.id = sap2.uid
              AND sap.id < sap2.id
              AND sap2.is_revoked != 1
              AND (sap2.is_refunded != 1 OR sap2.is_refunded IS null)
        WHERE u.id IN (?) AND sap2.id is NULL;
        `);

        student_meta = await dbRawRead(this.app, [studentIds], `
          select um.uid, um.key_namespace, um.key, um.value
          from user_metas um
          where um.uid IN (?)
        ;`);

        const all_tw_student_meta = await dbRawRead(this.app, [studentIds], `
          select um.uid, um.key_namespace, um.key, um.meta, um.value
          from tw_user_metas um
          where um.uid IN (?)
        ;`);

        all_tw_student_meta.forEach(tw_um => {
          tw_um.meta = JSON.parse(tw_um.meta);
          if(tw_um.key != 'StudentSchoolEnrolmentInfo'){
            tw_student_meta.push(tw_um);
            return;
          }
          if(!tw_um.meta.SchoolCode){
            return;
          }
          if(school[0].foreign_id == `S.${tw_um.meta.SchoolCode}`){
            tw_student_meta.push(tw_um);
          }
        })

        const sessionIds = classes_sessions.map(entry => entry.test_session_id);
        if(sessionIds.length > 0){
          student_attempt_info = await dbRawRead(this.app, [sessionIds, studentIds], `
            select ta.uid
                , ta.test_session_id
                , ta.twtdar_id
                , ta.created_on as ta_created_on
                , ta.twtdar_order as ta_twtdar_order
                , twtdar.user_metas_filter as twtdar_user_metas_filter
              from test_attempts ta
              join test_sessions ts on ts.id = ta.test_session_id and ts.id in (?)
              join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id
             where ta.uid in (?)
               and ta.is_invalid = 0
          ;`);
        }
      }
    }

    // Responsibility agreement
    const totalNonAcceptedRecords = await this.app.service('public/school-admin/school').getUnacceptedTestWindows(test_windows, uid);

    await this.app.service('public/abed-pasi').setDiplomaExamInfo(students, sch_group_id);
    // console.log("STUDENTS", JSON.stringify(students));

    const school_students = await this.getSchoolStudentsRecords(schl_group_id, {keys: ['StudentGrade', 'StudentIdentificationNumber', 'DateofBirth', 'StudentGrade']});

    return [{
      test_windows,
      academic_years,
      school_semesters,
      school,
      schl_dist,
      classGroupIds, //test
      teachers, // self only
      classes,
      studentIdentifierCaptions,
      classes_invigilators,
      guest_classes,
      classes_sessions,
      classes_closed_sessions,
      classes_teachers,
      students,
      classes_students,
      student_meta,
      tw_student_meta,
      guest_classes_invigilators,
      totalNonAcceptedRecords,
      student_attempt_info,
      school_students
    }];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();

  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  getSchoolStudentsRecords = async(
    schl_group_id: number, 
    meta_keys?: {
      keys?: string[],       
    }
  ) => {
    // find students
    const students = await dbRawRead(this.app, 
      { schl_group_id },
       SQL_SCHL_STU
    );
    // get student Ids
    const studentIds = students.map(s => s.uid);

    const keys = meta_keys?.keys    

    // get student's metas
    let stuMetas:any[] = [];
    if (studentIds.length){
      stuMetas = await dbRawRead(
        this.app,
        {uids: studentIds, keys},
        SQL_TW_USER_META(keys)
      )
    }

    let stuMetaMap: Map<number, any[]> = new Map();
    stuMetas.forEach(meta => {
      if(!stuMetaMap.has(meta.uid)) {
        stuMetaMap.set(meta.uid, [])
      } 
      stuMetaMap.get(meta.uid)?.push(meta);
    });

    students.forEach(student => {
      student['tw_user_metas'] = stuMetaMap.get(student.uid)
    });

    return students;
  }
}
