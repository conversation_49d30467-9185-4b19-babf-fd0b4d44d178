import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { Knex } from 'knex';
import { dbRawWrite } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class UpdateTestForm implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!id || !params?.query?.test_session_id) {
      throw new Errors.BadRequest();
    }

    const student_uid = id;
    const {test_session_id} = params.query;

    const db:Knex = await this.app.get('knexClientRead');

    const attemptRecords = <any[]>await db('test_attempts as ta')
    .join('test_forms as tf', 'tf.id', 'ta.test_form_id')
    .where('ta.uid', student_uid)
    .where('ta.test_session_id', test_session_id)
    .whereNot('ta.is_invalid', 1)
    .select('ta.*', 'tf.test_design_id')

    let alreadyBegun;
    for(const attempt of attemptRecords) {

      if(attempt.is_submitted || attempt.last_touch_on || attempt.started_on) {
        alreadyBegun = true; //Flag to throw an error, but continue to try to update other test attempts
      }
      const testDesign = await this.app.service('public/student/session').loadNewStudentTestDesign(attempt.uid, attempt.test_session_id, attempt.twtdar_order);
      if(testDesign.test_form_linear != attempt.test_form_linear && testDesign.test_design_id != attempt.test_design_id) {
        //Update it
        const test_form_id = <number> testDesign.test_form_id;
        const test_form_cache = await this.app.service('public/student/session').generateTestFormCache(testDesign);
        const test_form_linear = testDesign.test_form_linear;
        const twtdar_id = testDesign.alloc_id;
        this.app.service('db/write/test-attempts').patch(attempt.id, {
          is_invalid: 1
        })

        const createAttemptFields  = {
          ...attempt,
          test_form_id,
          twtdar_id,
          test_form_cache,
          test_form_linear
        };

        delete createAttemptFields.test_design_id;
        delete createAttemptFields.id;

        const newTestAttempt = await this.app.service('db/write/test-attempts').create(createAttemptFields);

        /**
         *     const attemptRecords = <any[]>await db('test_attempts as ta')
                .join('test_forms as tf', 'tf.id', 'ta.test_form_id')
                .where('ta.uid', student_uid)
                .where('ta.test_session_id', test_session_id)
                .whereNot('ta.is_invalid', 1)
                .select('ta.*', 'tf.test_design_id')
         */
        const tassRecs = await db('test_attempt_sub_sessions as tass')
        .join('test_session_sub_sessions as tsss', 'tsss.id', 'tass.sub_session_id')
        .where('tass.test_attempt_id', attempt.id)
        .whereNot('tass.is_invalid', 1)
        .select('tass.*', 'tsss.slug')

        const tassIds = tassRecs.map ( (t:any) => t.id );

        dbRawWrite(this.app, [tassIds], `
          UPDATE test_attempt_sub_sessions
          SET is_invalid = 1
          WHERE id IN (?)
        `)

        for(const rec of tassRecs) {

          const sections_allowed = this.app.service('public/educator/session-sub').getSectionsAllowed(testDesign?.subsession_meta, rec.slug);

          const createFields = {
            ...rec,
            test_attempt_id: newTestAttempt.id,
            sections_allowed
          }

          delete createFields.id;
          delete createFields.slug;
          this.app.service('db/write/test-attempt-sub-sessions').create(createFields);
        }
      }
    }

    if(alreadyBegun) {
      throw new Errors.Forbidden('ALREADY_BEGUN');
    }

    return {};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
