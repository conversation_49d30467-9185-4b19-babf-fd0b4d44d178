// Initializes the `public/educator/walk-in-students` service on path `/public/educator/walk-in-students`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { WalkInStudents } from './walk-in-students.class';
import hooks from './walk-in-students.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/walk-in-students': WalkInStudents & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/walk-in-students', new WalkInStudents(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/walk-in-students');

  service.hooks(hooks);
}
