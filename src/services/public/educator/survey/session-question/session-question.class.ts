import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { QuestionResponseEntry } from '../../../test-taker/invigilation/question-response/question-response.class';

interface Data {}

interface ServiceOptions {}

export class SessionQuestion implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: QuestionResponseEntry, params?: Params): Promise<Data> {
    if (params && data) {
      const uid = await currentUid(this.app, params);
      return this.app.service('public/student/session-question').submitQuestionReq(uid, data);
    }
    throw new Errors.BadRequest();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (params && data) {
      const uid = await currentUid(this.app, params);
      return this.app.service('public/student/session-question').submitTestReq(uid, data);
    }
    throw new Errors.BadRequest();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
