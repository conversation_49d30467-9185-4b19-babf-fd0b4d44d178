import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbDateNow } from '../../../../../util/db-dates';
import { currentUid } from '../../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class Session implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed()
  }

  async get (id: Id, params?: Params): Promise<Data> {
    // throw new Errors.GeneralError('WINDOWS_CLOSED');
    if (params && id){
      const {lang} = (<any>params).query;
      const test_session_id = <number> id;
      const uid = await currentUid(this.app, params);
      // to do: validate access of this user to this test session.
      return this.app
        .service('public/student/session')
        .findAttemptPayload({
          uid, 
          test_session_id,
          lang
        }, params);
    }
    throw new Errors.BadRequest();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const {test_attempt_id} = <any> data;
    this.app.service('db/write/test-attempts').patch(test_attempt_id, {last_touch_on: dbDateNow(this.app)});
    return {};
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
