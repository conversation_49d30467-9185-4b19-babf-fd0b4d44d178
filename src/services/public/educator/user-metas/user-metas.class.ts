import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';

interface Data {
  key_namespace: string,
  key: string,
  value: string,
  created_by_uid?: Id,
  uid?: Id
}

interface ServiceOptions {}

export class UserMetas implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    return this.app.service('db/write/user-metas').create(data);
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: Id, data: Data, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {key_namespace, key, value} = data;

    const uid = await currentUid(this.app, params);

    const existingIdRec = await this.app.service('db/read/user-metas').db().where('uid', id)
    .where('key_namespace', key_namespace)
    .where('key', key)
    .pluck('id')
    .limit(1)

    if(!existingIdRec?.length) {
      return this.create({...data, created_by_uid: uid, uid: id}, params);
    }

    const existingId = existingIdRec[0];
    
    const patchData = {value, updated_by_uid: uid};

    return this.app.service('db/write/user-metas').patch(existingId, patchData)
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
