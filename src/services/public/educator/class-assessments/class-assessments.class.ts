import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead, dbRawReadCountReporting, dbRawReadReporting, dbRawReadSingle } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';

interface Data {}

interface ServiceOptions {}

export interface IAsmtAllocInfo {
  test_window_id :number,
  twtar_id:number,
  type_slug:string,
  long_name:string,
  is_secured:number,
  is_questionnaire:number,
  is_scheduled:number,
  is_sample:number,
  test_duration:number,
  test_date_start:string,
  tw_date_start:string,
  tw_date_end:string,
  is_school_allowed_strict:number,
  isDateOverriden:boolean,
  isSchoolAllowed:boolean,
  is_active: number,
  is_active_for_qa: number,
  is_qa_school: number,
  req_sd_lang: number,
  req_sd_lang_not: number,
  sd_lang:string,
  lang:string,
}

export class ClassAssessments implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if (params && params.query){
      const sch_class_group_id = +id;
      // todo: also pass along date options based on twtdar_schools_allowed, can nest it in the relevant twtdar records
      // todo: might need to aggregate multi-part (order>0) records for jurisdictions that use it
      return this.getAllocWithOverridesByClass(sch_class_group_id);
    }
    throw new Errors.BadRequest();
  }

  async getDateExceptions(sc_group_id:number){
    const dateExceptions:{[prop:string]:string[]} = {};
    const records = await dbRawRead(this.app, {sc_group_id}, `
      select tsa.type_slug
            , tsa.date_start_override 
      from school_classes sc 
      join school_semesters ss 
        on ss.id  = sc.semester_id 
      join schools s 
        on s.group_id  = sc.schl_group_id 
      join twtdar_schools_allowed tsa 
        on tsa.school_id = s.id 
        and tsa.test_window_id = ss.test_window_id
        and tsa.is_date_override = 1
        and tsa.is_revoked = 0
      where sc.group_id = :sc_group_id
    `);
    for (let record of records){
      const {type_slug, date_start_override} = record;
      if (!dateExceptions[type_slug]){
        dateExceptions[type_slug] = [];
      }
      dateExceptions[type_slug].push(date_start_override);
    }
    return dateExceptions;
  }

  processCoursesFromAssessments(assessments: any[]){
    let courses: any[] = [];
    for(let asmnt of assessments){
      if(!asmnt.ac_id){
        continue;
      }
      const alreadyTracked = courses.findIndex(c => c.id == asmnt.ac_id && c.is_sample == asmnt.is_sample && c.is_field_test == asmnt.is_field_test) != -1;
      if(alreadyTracked){
        continue;
      }
      courses.push({
        id: asmnt.ac_id, 
        label: asmnt.course_name_full, 
        is_sample: asmnt.is_sample, 
        is_field_test: asmnt.is_field_test
      });
    }
    return courses;
  }

  async getAllocWithOverridesByClass(sch_class_group_id:number, type_slug?:string) {
    const assessments = await this.getTwAssessmentsForClassPreOverrides(sch_class_group_id, type_slug);
    const assessmentsFiltered = await this.applyTSAOverridesToAsmtAlloc(assessments, sch_class_group_id, type_slug)
    const dateExceptions = await this.getDateExceptions(sch_class_group_id);
    const assessmentTypeOptions = await this.getAssessmentClassAsmtTypeOptions(sch_class_group_id);
    const courseOptions = this.processCoursesFromAssessments(assessmentsFiltered);
    // const systemMessages = await this.getSystemMessages()
    return {
      assessments: assessmentsFiltered,
      dateExceptions,
      assessmentTypeOptions,
      courseOptions,
      // systemMessages,
    }
  }

  // async getSystemMessages(){
  //   const slugs = [
  //     'SCHED_MSG', 
  //     'SCHED_FT_MSG', 
  //     'SCHED_PR_MSG'
  //   ]
  //   const records = await dbRawRead(this.app, {slugs}, `
  //     select ssm.slug
  //          , ssm.data
  //     from support_system_messages ssm
  //     where ssm.is_revoked = 0
  //       and ssm.slug in (:slugs)
  //   `)
  //   const mappings:{[key:string]: string} = {};
  //   for (let ssm of records){
  //     try {
  //       mappings[ssm.slug] = JSON.parse(ssm.data).message || ''
  //     }
  //     catch(e){}
  //   }
  //   return mappings;
  // }

  checkLanguageRules(asmt:Partial<IAsmtAllocInfo>){
    if (asmt.sd_lang == asmt.lang){
      if (asmt.req_sd_lang_not==1){
        return false
      }
    }
    else {
      if (asmt.req_sd_lang==1){
        return false
      }
    }
    return true
  }

  applyAllocAvailabilityFilter(assessments: Partial<IAsmtAllocInfo>[], isLangOverride:boolean) {
    return assessments.filter(asmt => {

      if (!isLangOverride && !this.checkLanguageRules(asmt)){
        return false;
      }

      if (asmt.is_active == 1){
        return true;
      }
      else if (asmt.is_active_for_qa == 1 && asmt.is_qa_school == 1) {
        asmt.long_name = '[QA-ONLY ACCESS] ' + asmt.long_name
        return true;
      }
      else {
        return false;
      }
    })
  }

  async getAssessmentClassAsmtTypeOptions(sch_class_group_id:number){
    return dbRawRead(this.app, {sch_class_group_id}, `
      select acato.type_option_slug
          , acato.asmt_cat_slug
          , acato.label
          , acato.filter
          , acato.order
          , acato.is_use_course_sub_selection
      from school_classes sc 
      join school_semesters ss on ss.id = sc.semester_id 
      join test_windows tw on tw.id = ss.test_window_id 
      join assessment_class_asmt_type_options acato on acato.type_option_slug = tw.type_slug
      where sc.group_id = :sch_class_group_id
      group by acato.asmt_cat_slug
      order by acato.order
    `)
  }

  async getTwAssessmentsForClassPreOverrides(sch_class_group_id:number, type_slug?:string){
    const records:Partial<IAsmtAllocInfo>[] = await dbRawRead(this.app, { sch_class_group_id }, `
      select ss.test_window_id 
          , twtar.id twtar_id
          , twtar.type_slug
          , ac.id ac_id
          , ac.course_name_full
          , ifnull(twtt.caption_short, twtar.long_name) as long_name
          , twtar.is_secured
          , twtar.is_questionnaire
          , twtar.is_scheduled
          , twtar.is_schedule_range
          , twtar.is_sample
          , twtar.is_field_test
          , twtar.test_duration
          , twtar.test_date_start
          , twtar.test_date_end
          , tw.date_start tw_date_start
          , tw.date_end tw_date_end
          , tw.is_school_allowed_strict
          , twtar.is_active
          , twtar.is_active_for_qa
          , sd.brd_lang sd_lang
          , twtar.lang
          , twtar.req_sd_lang
          , twtar.req_sd_lang_not
          , twtar.is_session_name_excluded
          , twtar.is_session_duration_excluded
          , sd.is_sample as is_qa_school
      from school_classes sc 
      join school_semesters ss on ss.id = sc.semester_id 
      join test_windows tw on tw.id = ss.test_window_id 
      join test_window_td_alloc_rules twtar on twtar.test_window_id = ss.test_window_id 
      join schools s on s.group_id = sc.schl_group_id
      join school_districts sd on sd.group_id = s.schl_dist_group_id
      left join test_window_td_types twtt 
        on twtt.type_slug = twtar.slug 
        and twtt.test_window_id is null
          and twtt.is_revoked = 0
      left join assessment_courses ac on twtar.slug like concat(ac.course_code, "%")
      where sc.group_id = :sch_class_group_id
      group by twtar.is_active, twtar.is_active_for_qa, twtar.selection_order, twtar.slug, twtar.type_slug, twtar.order
      order by twtar.selection_order, twtar.slug, twtar.order
    `);
    const isLangOverride = await this.checkLangOverride(sch_class_group_id);
    const activeRecords = this.applyAllocAvailabilityFilter(records, isLangOverride);
    return activeRecords
  }

  async checkLangOverride(sch_class_group_id:number){
    const records = await dbRawReadReporting(this.app, {sch_class_group_id}, `
        select tsa.id
        from school_classes sc 
        join school_semesters ss 
          on ss.id = sc.semester_id 
        join schools s 
          on s.group_id = sc.schl_group_id 
        join twtdar_schools_allowed tsa 
          on tsa.school_id = s.id 
          and tsa.test_window_id = ss.test_window_id 
          and tsa.is_revoked  = 0
          and tsa.is_lang_override = 1 
        where sc.group_id = :sch_class_group_id
    `)
    return records.length > 0;
  }
  
  async applyTSAOverridesToAsmtAlloc(assessments:any[], sch_class_group_id:number, type_slug?:string){
    if (assessments.length){
      const tsaRecords = await this.getTwtarSchoolAllowForClass(sch_class_group_id);
      for (let tsaRecord of tsaRecords){
        const { test_window_id, is_all_type_slugs, type_slug, is_date_override, date_start_override } = tsaRecord;
        for (let asmt of assessments){
          if (asmt.test_window_id == test_window_id){
            if (is_all_type_slugs == 1 || type_slug){
              asmt.isSchoolAllowed = true;
              // if (is_date_override == 1){
              //   asmt.test_date_start = asmt.date_start_override
              //   asmt.isDateOverriden = true
              // }
            }
          }
        }
      }
    }
    const assessmentsFiltered:IAsmtAllocInfo[] = assessments.filter(asmt => {
      // if (asmt.current_timestamp_minus_duration > test_date_start){  return false }
      return (asmt.is_school_allowed_strict == 0 || asmt.isSchoolAllowed || asmt.is_sample==1)
    })
    return assessmentsFiltered
  }

  async getTwtarSchoolAllowForClass(sch_class_group_id:number, type_slug?:string){
    return dbRawRead(this.app, { sch_class_group_id}, `
      select tsa.test_window_id 
          , tsa.is_all_type_slugs 
          , tsa.type_slug 
          , tsa.is_date_override 
          , tsa.date_start_override 
      from school_classes sc 
      join school_semesters ss on ss.id = sc.semester_id 
      join schools s on s.group_id = sc.schl_group_id 
      left join twtdar_schools_allowed tsa 
        on tsa.school_id = s.id 
        and tsa.test_window_id = ss.test_window_id
        and tsa.is_revoked = 0
        and tsa.is_date_override = 0
      where sc.group_id = :sch_class_group_id
  `)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
