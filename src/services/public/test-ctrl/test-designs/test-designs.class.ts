import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead, dbEscapeNum, dbRawReadSingle, dbRawWrite } from '../../../../util/db-raw';
import { arrToMap } from '../../../../util/param-sanitization';
import { currentUid } from '../../../../util/uid';
import { SQL_SELECT_TWTAR_TEST_DESIGNS } from './model/sql';

interface Data {}

interface ServiceOptions {}

const twtdarPatchFields = [
  'num_sub_sessions',
  'type_slug',
  'slug',
  'order',
  'lang',
  'form_code',
  'test_design_id',
  'long_name',
  'is_scheduled',
  'is_secured',
  'is_questionnaire',
  'is_sample',
  'is_classroom_common_form',
  'can_credential',
  'is_pipeline_exclude',
  'req_sd_lang',
  'req_sd_lang_not',
  'subsession_meta',
  'user_metas_filter',
  'accomm_user_meta_constraint',
  'max_condition_on_option',
  'test_duration',
  'test_date_start',
  'test_date_end',
  'perusal_type',
  'perusal_end_type',
  'perusal_offset_hours',
  'perusal_duration_hours',
  'perusal_date_start',
  'perusal_date_end',
  'is_schedule_range',
  'is_field_test',
  'is_print',
  'print_configs',
  'selection_order',
  'tqr_ovrd_td_id',
  'is_active',
  'is_active_for_auth',
  'is_date_restricted',
  'is_school_allowed_strict',
  'is_active_for_qa',
  'hard_close_on',
  'is_marking_req',
  'is_score_entry',
  'is_session_duration_excluded',
  'is_session_name_excluded',
  'print_before_days',
  'upload_until_days',
  'is_exclude_weekends',
  'exception_dates',
]

const resp_sheet_config_fields = {
  'print_before_days': 'number', // number of days before the test date to print the response sheet until the test date start time
  'upload_until_days': 'number', // number of days after the test date to upload the response sheet
  'is_exclude_weekends': 'boolean', // consider only business days for print_before_days and upload_until_days
  'exception_dates': 'array', // list of dates to exclude from the print_before_days and upload_until_days
}

export class TestDesigns implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {test_window_id, tc_group_id} = params.query;
      // pull tw allocs
      const records = await dbRawRead(this.app, [test_window_id, tc_group_id], SQL_SELECT_TWTAR_TEST_DESIGNS);
      if (!records.length){
        return [];
      }
      const qsIds = records.map(r => r.source_item_set_id);
      // get the latest published test design
      const latestTdByQS = await dbRawRead(this.app, [qsIds], `
        select t.source_item_set_id
            , t.lang
            , tf.test_design_id
        from (
          select td.source_item_set_id
              , tf.lang
              , max(tf.id) max_tf_id
          from test_designs td
          join test_forms tf
            on tf.test_design_id = td.id
            and tf.is_revoked = 0
          where td.source_item_set_id in (?)
          group by td.source_item_set_id, tf.lang
        )  t
        join test_forms tf
          on tf.id = t.max_tf_id
      ;`);
      const keyJoinChar = ';'
      const latestTdByQSRef = arrToMap(latestTdByQS, ['source_item_set_id', 'lang'], {reduceToProp: 'test_design_id', keyJoinChar});
      records.forEach(record => {
        const {source_item_set_id, lang, test_design_id} = record;
        const key = [source_item_set_id, lang].join(keyJoinChar)
        const latestTdId = latestTdByQSRef.get(key);
        if (+latestTdId !== +test_design_id){
          record.isNotLatestTd = 1
        }
        if(record.question_requiring_scan > 0){
          const resp_sheet_config = record.resp_sheet_config;
          if (resp_sheet_config){
            record.resp_sheet_config = JSON.parse(resp_sheet_config);
            for(let f in resp_sheet_config_fields){ 
              record[f] = record.resp_sheet_config[f];
            }
          } else {
            for(const [key,value] of Object.entries(resp_sheet_config_fields)){
              switch(value){
                case 'array':
                  record[key] = [];
                  break;
                case 'number':
                  record[key] = 0;
                  break;
                case 'boolean':
                  record[key] = undefined;
                  break;
                default:
                  record[key] = null;
              }
            }
          }
        }
      })
      return records;
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    // load test designs (filter on local)
    const source_item_set_id = id;
    if (params && params.query){
      const {test_window_id, tc_group_id} = params.query;
      const testDesigns = await dbRawRead(this.app, [], `
        select td.id
              , td.created_on
              , td.created_by_uid
              , td.name
              , count(tf.id) num_forms
              , concat(u.first_name, ' ', u.last_name) td_created_by
              , td.source_item_set_id
              , tf.lang
              , qs.slug qs_slug
              , qs.is_test_design qs_is_test_design
              , qs.name qs_name
              , qs.description qs_description
              , qs.languages qs_languages
        from test_designs td
        join temp_question_set qs 
          on qs.id = td.source_item_set_id
        join test_forms tf
          on tf.test_design_id = td.id
          and tf.is_revoked = 0
        join users u 
          on u.id = td.created_by_uid
        ${
          (source_item_set_id === -1) ? '' : `
            WHERE td.source_item_set_id = ${await dbEscapeNum(source_item_set_id)}
          `
        }
        group by td.id
        order by td.id desc
        limit 500
      `);
      return testDesigns;
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (params && params.query){
      const {test_window_id, tc_group_id} = params.query;
      const resp_sheet_config_fields_array = Object.keys(resp_sheet_config_fields);
      const payload:any = {};
      for (let prop of twtdarPatchFields){
        if(!resp_sheet_config_fields_array.includes(prop)){
          payload[prop] = (<any>data)[prop]
        }
      }
      // set questionSetId with twtarSlug and set the newly created twtarSlug with the questionSetId
      const test_design_id = (<any>payload).test_design_id || null;
      const qsId = await this.updateQuestionSetTwtarSlug(test_design_id, (<any>data)['type_slug'])
      if(qsId){
        payload['item_set_id'] = qsId;
      }
      // set resp_sheet_config to null
      payload['resp_sheet_config'] = null;
      const td_assigned_by_uid = await currentUid(this.app, params);
      return <any>this.app
        .service('db/write/test-window-td-alloc-rules')
        .create({
          ... payload,
          td_assigned_by_uid,
          td_assigned_on: dbDateNow(this.app),
          test_window_id
        })
    }
    throw new Error();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async validateWriteAccess(test_window_id:number, tc_group_id:number, twtar_id:NullableId) {
    const records = await dbRawRead(this.app, [test_window_id, tc_group_id, twtar_id], `
      select twtar.id
      from test_windows tw 
      join test_window_td_alloc_rules twtar 
        on twtar.test_window_id = tw.id 
      where tw.id = ?
        and tw.test_ctrl_group_id = ?
        and twtar.id = ?
    `);
    if (records.length === 0){
      throw new Errors.Forbidden('INVALID-test_ctrl_group_id')
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const twtar_id = id;
    if (params && params.query){
      const patched_by_uid = await currentUid(this.app, params);
      const {test_window_id, tc_group_id} = params.query;
      // note: the extra stuff here is for access control
      await this.validateWriteAccess(test_window_id, tc_group_id, twtar_id);
      const resp_sheet_config_fields_array = Object.keys(resp_sheet_config_fields);
      const payload:any = {}; 
      const isScanAssessment = (<any>data)['isScanAssessment'] // needed only to check if the assessment is a scan assessment not a patch field. comes based on question_requiring_scan > 0.
      if(isScanAssessment){
        payload['resp_sheet_config'] = {is_exclude_weekends: true}; // setting is_exlude_weekends to true by default because, the check done on the frontend always stays true.
      }
      for (let prop of twtdarPatchFields){
        const val = (<any>data)[prop];
        if (val !== undefined){
          if(!resp_sheet_config_fields_array.includes(prop)){
            payload[prop] = val;
          } else {
            payload['resp_sheet_config'][prop] = val;
          }
        }
      }
      if(payload['resp_sheet_config'] && typeof payload['resp_sheet_config'] === 'object' && isScanAssessment){
        payload['resp_sheet_config'] = JSON.stringify(payload['resp_sheet_config']);
      }
      //Note: set questionSetId with twtarSlug and set only the newly created twtarSlug with the questionSetId for older assessments when updating them if they don't exist.
      const test_design_id = (<any>payload).test_design_id || null;
      const qsId = await this.updateQuestionSetTwtarSlug(test_design_id, (<any>data)['type_slug'])
      if(qsId){
        payload['item_set_id'] = qsId;
      }
      await this.app
        .service('db/write/test-window-td-alloc-rules-log')
        .create({
          twtar_id,
          patched_by_uid,
          test_design_id,
          patches: JSON.stringify(payload, null, 2),
        })
      await this.app
        .service('db/write/test-window-td-alloc-rules')
        .patch(twtar_id, {
          ...payload,
          td_assigned_by_uid: patched_by_uid,
          td_assigned_on: dbDateNow(this.app),
        })
      return data;
    }

    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    const twtar_id = id;
    if (params && params.query){
      const patched_by_uid = await currentUid(this.app, params);
      const {test_window_id, tc_group_id} = params.query;
      // note: the extra stuff here is for access control
      await this.validateWriteAccess(test_window_id, tc_group_id, twtar_id);
      const payload:any = {
        is_active: 0
      };
      await this.app
        .service('db/write/test-window-td-alloc-rules-log')
        .create({
          twtar_id,
          patched_by_uid,
          patches: JSON.stringify(payload, null, 2),
        })
      await this.app
        .service('db/write/test-window-td-alloc-rules')
        .patch(twtar_id, {
          ... payload,
          td_assigned_by_uid: patched_by_uid,
          td_assigned_on: dbDateNow(this.app),
        })
      return {};
    }
    throw new Errors.BadRequest();
  }
  async updateQuestionSetTwtarSlug(test_design_id : number, twtarSlug: string) {
    if(!test_design_id || !twtarSlug){
      return null;
    }
    const getQuestionSet = await dbRawReadSingle(this.app, {test_design_id}, `
      SELECT tqs.* FROM test_designs td
      INNER JOIN temp_question_set tqs ON td.source_item_set_id = tqs.id
      WHERE td.id = :test_design_id`)
    if (!getQuestionSet) {
      throw new Errors.NotFound('TD_OR_QS_NOT_FOUND')
    }
    const { id: tqsId } = getQuestionSet
    await dbRawWrite(this.app, {tqsId, twtarSlug}, `UPDATE temp_question_set SET twtar_type_slug = :twtarSlug WHERE id = :tqsId`)
    return tqsId;
  }
}
