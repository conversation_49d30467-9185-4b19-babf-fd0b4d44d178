
export const SQL_SELECT_TWTAR_TEST_DESIGNS = `  /*SQL_SELECT_TWTAR_TEST_DESIGNS*/ 
    select twtar.id
            , twtar.type_slug 
            , twtar.slug
            , twtar.form_code
            , twtar.long_name
            , twtar.test_window_id
            , twtar.test_design_id
            , twtar.num_sub_sessions
            , twtar.is_active
            , twtar.is_active_for_auth
            , twtar.is_secured
            , twtar.is_questionnaire
            , twtar.is_sample
            , twtar.is_outside_window
            , twtar.is_alternative
            , twtar.order
            , twtar.lang
            , twtar.user_metas_filter
            , twtar.subsession_meta
            , twtar.generate_report
            , twtar.is_scheduled
            , twtar.cloned_from_id
            , twtar.is_classroom_common_form
            , twtar.is_date_restricted
            , twtar.subsession_meta
            , twtar.td_assigned_on
            , twtar.is_classroom_common_form
            , twtar.can_credential
            , twtar.is_pipeline_exclude
            , twtar.req_sd_lang
            , twtar.req_sd_lang_not
            , twtar.accomm_user_meta_constraint
            , twtar.max_condition_on_option
            , twtar.test_duration
            , twtar.test_date_start
            , twtar.test_date_end
            , twtar.perusal_type
            , twtar.perusal_end_type
            , twtar.perusal_offset_hours
            , twtar.perusal_duration_hours
            , twtar.perusal_date_start
            , twtar.perusal_date_end
            , twtar.is_schedule_range
            , twtar.is_field_test
            , twtar.is_marking_req
            , twtar.component_code
            , twtar.selection_order
            , twtar.tqr_ovrd_td_id
            , twtar.is_school_allowed_strict
            , twtar.is_active_for_qa
            , twtar.hard_close_on
            , twtar.is_print
            , twtar.print_configs
            , twtar.is_session_duration_excluded
            , twtar.is_session_name_excluded
            , twtar.resp_sheet_config
            , COUNT(distinct tqsi.id) as question_requiring_scan
            , concat(u_twtdar.first_name, ' ', u_twtdar.last_name) td_assigned_by
            , td.created_on td_created_on
            , td.created_by_uid td_created_by_uid
            , concat(u.first_name, ' ', u.last_name) td_created_by
            , count(distinct tf.id) num_forms
            , count(distinct tqr.question_id) n_items
            , count(distinct tqr.student_question) n_item_numbers
            , sum(tqr.score_points) total_item_score_points
            , td.source_item_set_id
            , qs.slug qs_slug
            , qs.is_test_design qs_is_test_design
            , qs.name qs_name
            , qs.is_score_entry
            , qs.description qs_description
            , qs.languages qs_languages
            , tw.PASI_school_year
            , twtt.is_perusal_allow
            , twtt.perusal_configs
    from test_windows tw 
    join test_window_td_alloc_rules twtar 
        on twtar.test_window_id = tw.id 
    left join test_designs td
        on td.id = twtar.test_design_id
    left join test_forms tf
        on tf.test_design_id = td.id
        and tf.is_revoked = 0
    left join test_question_register tqr 
        on tqr.test_design_id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
    LEFT JOIN test_question_scoring_info tqsi 
		ON tqr.tqsi_id = tqsi.id  
    	AND tqsi.is_paper_response = 1 
    left join temp_question_set qs 
        on qs.id = td.source_item_set_id
    left join users u 
        on u.id = td.created_by_uid
    left join users u_twtdar
        on u_twtdar.id = twtar.td_assigned_by_uid
    left join test_window_td_types twtt 
 	    on twtt.type_slug = twtar.type_slug
        and twtt.tw_type_slug = tw.type_slug
        and twtt.is_revoked = 0
    where tw.id = ?
        and tw.test_ctrl_group_id = ?
    group by twtar.id
    order by twtar.selection_order, twtar.slug, twtar.order
`
