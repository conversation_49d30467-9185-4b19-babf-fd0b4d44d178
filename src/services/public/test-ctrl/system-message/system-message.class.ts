import { MethodNotAllowed } from '@feathersjs/errors';
import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {}

interface ServiceOptions {}

export class SystemMessage implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return dbRawRead(this.app, {}, `
      select ssm.slug
           , ssm.data
           , ssm.description
           , ssm.created_on
           , ssm.color
      from support_system_messages ssm
      where ssm.is_revoked = 0
      order by ssm.slug
    `)
  }

  

  async get (id: Id, params?: Params): Promise<Data> {
    const slug = id;
    return dbRawReadSingle(this.app, {slug}, `
      select ssm.slug
           , ssm.data
           , ssm.description
           , ssm.created_on
           , ssm.color
      from support_system_messages ssm
      where ssm.is_revoked = 0
      and ssm.slug = :slug
    `)
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (params){
      const uid = await currentUid(this.app, params);
      const {slug, payload, description, color} = <any> data;
      const previousRecords = await dbRawRead(this.app, {slug}, `
        select ssm.id
        from support_system_messages ssm
        where ssm.is_revoked = 0
        and ssm.slug = :slug
      `)
      if (previousRecords.length){
        const prev_ids = previousRecords.map(record => record.id)
        await dbRawWrite(this.app, {prev_ids, uid}, `
          UPDATE support_system_messages
          SET is_revoked=1
            , revoked_on=now()
            , revoked_by_uid=:uid
          WHERE id in (:prev_ids);
        `)
      }
      return  this.app.service('db/write/support-system-messages').create({
        slug, 
        data: payload, 
        description,
        created_by_uid: uid,
        color: color,
        created_on: dbDateNow(this.app),
      })
    }
    throw new Errors.BadRequest();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
