// Initializes the `public/error` service on path `/public/error`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { Error } from './error.class';
import hooks from './error.hooks';

// Add this service to the service type index
declare module '../../../declarations' {
  interface ServiceTypes {
    'public/error': Error & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/error', new Error(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/error');

  service.hooks(hooks);
}
