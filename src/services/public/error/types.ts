// ref from feathers Errors interface
export enum ErrorTypes {
    'BadRequest'       = 400,
    'NotAuthenticated' = 401,
    'PaymentError'     = 402,
    'Forbidden'        = 403,
    'NotFound'         = 404,
    'MethodNotAllowed' = 405,
    'NotAcceptable'    = 406,
    'Timeout'          = 408,
    'Conflict'         = 409,
    'LengthRequired'   = 411,
    'Unprocessable'    = 422,
    'TooManyRequests'  = 429,
    'GeneralError'     = 500,
    'NotImplemented'   = 501,
    'BadGateway'       = 502,
    'Unavailable'      = 503,
 }