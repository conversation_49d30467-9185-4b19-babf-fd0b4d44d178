import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { Errors } from '../../../errors/general';
import { currentUid } from '../../../util/uid';
import { ErrorTypes } from './types';

interface Data {
  type: ErrorTypes
  message?: string,
  data?: any,
}

interface ServiceOptions {}

export class Error implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (payload: Data, params?: Params): Promise<Data> {
    let created_by_uid;    
    if (params){
      try {
        created_by_uid = await currentUid(this.app, params);
      }
      catch(e){}
    }   

    const err = {
      name: payload.type,
      message: payload.message || '--',
      data: {
        created_by_uid,
        ...payload.data
      }
    }
    
    throw Errors.convert(err)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

}
