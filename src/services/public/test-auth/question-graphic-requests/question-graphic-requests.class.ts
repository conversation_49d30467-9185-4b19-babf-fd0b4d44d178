import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { currentUid } from "../../../../util/uid";
import { Errors } from '../../../../errors/general';
import {ChangeLogType} from './../question-change-log/types'

interface Data {
  test_question_id?:number,
  lang?:string,
  created_by_uid?:number,
  title?:string,
  is_revision?:number,
  is_approved_high_contrast?:number,
  is_signed_off?:number,
}

interface ServiceOptions {}

export class QuestionGraphicRequests implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  /** Given a list of question IDs, return how many pending graphic requests each has in each language */
  async getPendingCountByQIds(questionIds:number[]){
    if (!questionIds.length) return {};
    const pendingCountMap:any = {};
    const pendingRequests = <any[]>await this.app.service('db/write/test-question-graphic-requests').find({
      query: {
        $select: ['lang', 'test_question_id'],
        test_question_id: { $in: questionIds},
        is_deleted: { $ne: 1 },
        is_signed_off: { $ne: 1 }
      },
      paginate: false
    });

    pendingRequests.forEach(req => {
      const {lang, test_question_id} = req;
      if (!pendingCountMap[test_question_id]) pendingCountMap[test_question_id] = {}
      const reqLangs = (lang === 'both') ? ['en', 'fr'] : [lang]
      reqLangs.forEach(lang => {
        const currentCount = pendingCountMap[test_question_id][lang] || 0
        pendingCountMap[test_question_id][lang] = currentCount + 1
      })
    })

    return pendingCountMap;

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query) throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    const {lang, test_question_id} = params.query

    // Find graphic requests for a given question and language
    const requests = <any[]>await this.app.service('db/write/test-question-graphic-requests').find({
      query: {
        test_question_id,
        lang: { $in: [lang, 'both'] },
        is_deleted: { $ne: 1 }
      },
      paginate: false
    });

    // Find and include full names of request authors
    const authorUids: Set<number> = new Set();
    requests.forEach(r => authorUids.add(r.created_by_uid))
    let authorNames:any[] = []
    if (requests.length) {
      authorNames = <any[]>await this.app.service('db/write/users').find({
        query: { id: { $in: [...authorUids] }},
        paginate: false
      });
    }
    requests.forEach(r => {
      const userName = authorNames.find(n => n.id === r.created_by_uid)
      r.created_by_name = userName.first_name + " " + userName.last_name
    })

    return requests;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if(!params || !data) throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    const {test_question_id, lang} = data;
    const newRequest = await this.app.service('db/write/test-question-graphic-requests').create({
      ...data,
      created_by_uid: await currentUid(this.app, params)
    });
    // Make a record in the log
    this.app.service('public/test-auth/question-change-log').create({
      test_question_id,
      lang,
      log_type: ChangeLogType.GRAPHIC_REQUEST_CREATION,
      log_data: {
        graphic_request_id: newRequest.id,
        is_revision: newRequest.is_revision
      }
    }, params)
    return newRequest;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!params || !data) throw new Errors.BadRequest('ERR_MISSING_PARAMS');

    const {is_signed_off, is_approved_high_contrast} = data;
    const patchedRequest = await this.app.service('db/write/test-question-graphic-requests').patch(id, 
      {is_signed_off, is_approved_high_contrast}  
    );

    const {test_question_id, lang, is_revision} = patchedRequest;

    // Make a record in the log
    this.app.service('public/test-auth/question-change-log').create({
      test_question_id,
      lang,
      log_type: ChangeLogType.GRAPHIC_REQUEST_STATUS,
      log_data: {
        graphic_request_id: id,
        is_revision,
        // Only one of these will be true
        is_signed_off: is_signed_off ? true : undefined,
        is_remove_signed_off: is_signed_off == 0 ? true : undefined,
        is_approved_high_contrast: is_approved_high_contrast ? true : undefined,
        is_remove_approved_high_contrast: is_approved_high_contrast == 0 ? true : undefined,
      }
    }, params)

    return patchedRequest;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if(!params || !params.query) throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    const {test_question_id, lang, is_revision} = params.query;
    await this.app.service('db/write/test-question-graphic-requests').patch(id, {
      is_deleted: 1
    });

    // Make a record in the log
    this.app.service('public/test-auth/question-change-log').create({
      test_question_id,
      lang,
      log_type: ChangeLogType.GRAPHIC_REQUEST_DELETION,
      log_data: {
        graphic_request_id: id,
        is_revision
      }
    }, params)

    return {}
  }
}
