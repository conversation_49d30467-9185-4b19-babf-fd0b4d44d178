import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Knex } from 'knex';
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { camelify, snakeify } from '../../../../util/caseify';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {
  id: number,
  group_id: number,
  current_version_id: number,
  is_revoked: number,
  created_on: Date,
  config: string,
}

export enum LangTypes {
  BILINGUAL = 'bilingual',
  REGULAR = 'regular'
}

export enum DimensionType {
  // MULTI_SELECT = 'multi-select',
  SELECT = 'select-sub',
  BINARY = 'binary',
  NUMERIC = 'numeric',
  LABEL = 'label',
  TEXT = 'text',
  IMAGE = 'image',
  AUDIO = 'audio',
  COORD = 'coordinate',
  VOICE = 'voice',
} 
;

export enum DimensionCategory {
  META = 'meta',
  SCORING = 'scoring'
}

export interface IAssessmentFrameworkDimensionDetail {
  name: string, // ex: Big Ideas
  code: string, // ex: D1
  type: DimensionType,
  category?: DimensionCategory
  color?: string,
  isHidden?: boolean,
  key?: string,
  config: {
    special?: {
      [key:string]: boolean,
    },
    tags? : Array<{
      name: string, // ex: Number Sense
      code: string, // ex: NS
    }>,
  }
}


export interface IAssessmentParameter {
  id: number, type_slug: string | null, assessment_slug: string | null, parameters: string, extend_id: number, lang_type: string
}

export interface IAssessmentParameterInfo {
  en: IAssessmentFrameworkDimensionDetail[],
  fr: IAssessmentFrameworkDimensionDetail[]
}

interface ServiceOptions {}

export class QuestionSetParameters implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Data | Paginated<Data>> {
    if(!params) {
      throw new Errors.BadRequest();
    }

    return await dbRawRead(this.app, [], `
      select 
          qsp.id
        , qsp.type_slug
        , qsp.assessment_slug
        , qsp.extend_id
        , qsp.lang_type
        , qspv.config parameters
      from question_set_parameters qsp 
      join question_set_parameters_versions qspv 
        on qspv.id = qsp.current_version_id
      where qsp.is_revoked = 0
      order by id, assessment_slug desc;
    `)
  }

  async get (id: Id, params?: Params): Promise<any> {
    if(!params || !params.query || !params.query.lang) {
      throw new Errors.BadRequest("MISSING_PARAMS");
    }

    const {lang} = params.query;

    const MAX_LOOPS = 10;
    const baseAssessmentParam = await dbRawReadSingle(this.app, {assessment_slug: id}, `
      select qsp.id, qsp.type_slug, qsp.assessment_slug, qsp.lang_type, qsp.extend_id, qspv.config parameters 
      from question_set_parameters qsp 
      join question_set_parameters_versions qspv 
        on qspv.id = qsp.current_version_id
      where qsp.is_revoked = 0
      and qsp.assessment_slug = :assessment_slug
      order by id, assessment_slug desc;
    `);

    if(!baseAssessmentParam) {
      return [];
    }

    let extend_ids: number[] = [baseAssessmentParam?.extend_id];
    let current_extend_id: number = -1; // Initially -1;
    const assessmentParams = [baseAssessmentParam];

    let failsafeCounter = 0;
    while(current_extend_id != null && current_extend_id != undefined && !extend_ids.includes(current_extend_id)) {
      if(current_extend_id < 0) { // If current ID hasnt been initalized, use base assessment
        current_extend_id = baseAssessmentParam?.extend_id;
        if(current_extend_id == null || current_extend_id == undefined) {
          break;
        }
      }

      failsafeCounter++;
      const assessmentParam = await dbRawReadSingle(this.app, {current_extend_id}, `
        select qsp.id, qsp.type_slug, qsp.assessment_slug, qsp.lang_type, qsp.extend_id, qspv.config parameters
        from question_set_parameters qsp 
        join question_set_parameters_versions qspv 
          on qspv.id = qsp.current_version_id
        where qsp.is_revoked = 0
        and qsp.id = :current_extend_id
        order by id, assessment_slug desc;
      `);

      if(assessmentParam) {
        assessmentParams.push(assessmentParam);
      }

      current_extend_id = assessmentParam?.extend_id;

      if(failsafeCounter >= MAX_LOOPS) { // adding a failsafe at 10 loops to safeguard against an infinite while loop.
        break;
      }
    }
    assessmentParams.reverse();
    
    const parsedParameters = this.combineParameters(assessmentParams, lang);

    return parsedParameters;
  }

  combineParameters(parameters: IAssessmentParameter[], lang: 'en' | 'fr') {
    // Initialize an empty array to hold the combined parameters
    let combinedObjects: IAssessmentFrameworkDimensionDetail[] = [];

    // Iterate over the array of JSON strings
    parameters.forEach(param => {
        // Parse the JSON string into an object
        const parsedObj: IAssessmentParameterInfo = JSON.parse(param.parameters);

        // Extract the array and concatenate it with combinedObjects
        if (param.lang_type == LangTypes.REGULAR && parsedObj[lang] && Array.isArray(parsedObj[lang])) {
            combinedObjects = combinedObjects.concat(parsedObj[lang]);
        }
        // If assessment parameter is bilingual, default to english
        if (param.lang_type == LangTypes.BILINGUAL && parsedObj['en'] && Array.isArray(parsedObj['en'])) {
          combinedObjects = combinedObjects.concat(parsedObj['en']);
      }
    });

    return combinedObjects;
  }

  async create (data: any, params?: Params): Promise<Data> {
    const {assessment_slug, type_slug, config, extend_id, lang_type} = data;
    if(!assessment_slug || !config || !lang_type) {
      throw new Errors.BadRequest('MISSING_DATA')
    }
    
    const slugExists = await this.checkAssessmentSlugExists(assessment_slug);
    if(slugExists) {
      throw new Errors.BadRequest('ASSESSMENT_SLUG_EXISTS');
    }

    // Create parameter with blank version ID
    const parameter = await this.app.service('db/write/question-set-parameters').create({assessment_slug, type_slug, extend_id, lang_type});
    const qsp_id = parameter.id;
    const newVersionData = {
      qsp_id,
      config
    };
    // Create new version linked to new parameter
    const newVersion = await this.app.service('db/write/question-set-parameters-versions').create(newVersionData);
    // Assign new version ID to the parameter
    return await this.app.service('db/write/question-set-parameters').patch(qsp_id, {current_version_id: newVersion.id});
  }

  async checkAssessmentSlugExists(assessment_slug: string) {
    const param = await dbRawRead(this.app, {assessment_slug}, `
      select qsp.id
      from question_set_parameters qsp 
      where qsp.is_revoked = 0
      and qsp.assessment_slug = :assessment_slug
      order by id, assessment_slug desc;
    `);

    return param.length > 0;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: IAssessmentParameter, params?: Params): Promise<any> {
    return await this.app.service('db/write/question-set-parameters').patch(id, data);
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    const parameterSet = await dbRawReadSingle(this.app, {id}, `
      select qsp.assessment_slug
      from question_set_parameters qsp 
      where qsp.id = :id;
    `);
    const assessment_slug = parameterSet?.assessment_slug ?? null;
    return await this.app.service('db/write/question-set-parameters').patch(id, {assessment_slug: null, revoked_assessment_slug: assessment_slug, is_revoked: 1, revoked_on: dbDateNow(this.app)});
  }
}
