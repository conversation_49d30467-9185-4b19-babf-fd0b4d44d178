import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import {Errors} from "../../../../errors/general";

interface Data {}

interface ServiceOptions {}

export class QuestionRevisions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const {test_question_id} = (<any> params).query;
    return this.app
      .service('db/read/test-question-revisions')
      .find({
        query: {
          test_question_id,
          $limit: 1000000,
          $sort: {
            created_on: -1
          }
        }
      })
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return this.app
      .service('db/read/test-question-versions')
      .get(id);
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const {message} = <any>data;
    if(message) {
      return this.app.service('db/write/test-question-versions').patch(id, {message});
    }
    return {};
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
