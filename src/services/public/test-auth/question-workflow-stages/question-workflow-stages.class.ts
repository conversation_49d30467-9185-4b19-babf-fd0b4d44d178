import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Knex } from 'knex';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import {ChangeLogType} from './../question-change-log/types'

interface Data {
  [key: string]: any,
}

interface ServiceOptions {}

export class QuestionWorkflowStages implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented();
  }

  async getStageIdByOrder(stage_order:number){
    const stageRecords = <any[]>await this.app
    .service('db/read/auth-workflow-stages')
    .find({query: {stage_order}, paginate: false});
    return stageRecords[0]?.id;
  }

  /** Return full info about the current stage the item is in */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if (!params || !params.query || !params.query.lang) throw new Errors.BadRequest("MISSING_PARAMS_REQ");
    const {lang} = params.query;

    const db:Knex = this.app.get('knexClientRead');
    // Find and return the current stage data of the item (or none)
    const stageRecords = await db('test_question_workflow_status as status')
    .where('status.lang', lang)
    .where('status.test_question_id', id)
    .join('auth_workflow_stages as stage', 'stage.id', 'status.stage_id')
    .select('status.*', 'stage.stage_order')
    .limit(1);

    if (!stageRecords.length) return {}
    const stageRecord = stageRecords[0];

    // If a stage exists, find any users assigned to it
    const assignedRecords = await db('test_question_workflow_assignments as a')
    .where('a.lang', lang)
    .where('a.test_question_id', id)
    .where('a.stage_id', stageRecord.stage_id)
    .select('a.uid')
    const assignedUids = assignedRecords.map(r => r.uid)

    return {...stageRecord, assignedUids}
  }

  /** To move the item into a new stage */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {

    if (!params || !params.query) throw new Errors.BadRequest("MISSING_PARAMS_REQ");

    const {test_question_id, lang} = params.query
    const {stage_order} = data

    //Find any existing records and remove them - should only store the current stage
    const stageStatusRecords = <any[]>await this.app
    .service('db/read/test-question-workflow-status')
    .find({query: {test_question_id, lang}, paginate: false});
    await Promise.all(stageStatusRecords.map(r => this.app.service('db/write/test-question-workflow-status').remove(r.id)));

    // Find the ID of the stage
    const stage_id = await this.getStageIdByOrder(stage_order);

    // Remove any previously existing assignment for this stage (if returning to this stage)
    const existingAssignmentRecords = <any[]>await this.app
    .service('db/read/test-question-workflow-assignments')
    .find({query: {test_question_id, lang, stage_id }, paginate: false});
    Promise.all(existingAssignmentRecords.map(r => {
      return this.app.service('db/write/test-question-workflow-assignments').remove(r.id)
    }))

    // Create the stage record
    const stageRecords = await this.app
    .service('db/write/test-question-workflow-status')
    .create({
      test_question_id,
      lang,
      stage_id, 
    })

    // Make a record in the log
    this.app.service('public/test-auth/question-change-log').create({
      test_question_id,
      lang,
      log_type: ChangeLogType.STAGE_TRANSITION,
      log_data: {
        stage_id,
      }
    }, params)

    return stageRecords;
  }


  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  /** Within an existing stage, update assignments or status settings */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (!params || !params.query) throw new Errors.BadRequest("MISSING_PARAMS_REQ");

    const test_question_id = id;
    const {lang, stage_order} = params.query
    const {assigned_uids, is_signed_off, is_done_editing, is_changes_required, is_edit_review} = data;
    const stage_id = await this.getStageIdByOrder(stage_order);
    const current_uid = await currentUid(this.app, params);

    // If called to update assignments
    if (assigned_uids && Array.isArray(assigned_uids)) {
      // Find which users are already assigned
      const existingAssignmentRecords = <any[]>await this.app
      .service('db/read/test-question-workflow-assignments')
      .find({query: {test_question_id, lang, stage_id }, paginate: false});
      const existingAssignedUids = existingAssignmentRecords.map(record => record.uid)
      // What users need to be newly assigned
      const uidsToAssign = assigned_uids.filter(uid => !existingAssignedUids.includes(uid))
      // What existing assignments need to be removed
      const uidsToUnassign = existingAssignedUids.filter(uid => !assigned_uids.includes(uid))
      const assignmentRecordsToRemove = existingAssignmentRecords.filter(record => uidsToUnassign.includes(record.uid))

      // Execute assignment creation and removal
      const promises: Promise<any>[] = [];
      assignmentRecordsToRemove.forEach(r => {
        promises.push(this.app.service('db/write/test-question-workflow-assignments').remove(r.id))
      })
      uidsToAssign.forEach(uid => {
        promises.push(
          this.app.service('db/write/test-question-workflow-assignments')
          .create({
            test_question_id, lang, stage_id,
            uid,
            created_by_uid: current_uid,
          })
        )
      })
      await Promise.all(promises);

      // Make a record in the log
      this.app.service('public/test-auth/question-change-log').create({
        test_question_id,
        lang,
        log_type: ChangeLogType.STAGE_ASSIGNMENT,
        log_data: {
          stage_id,
          assigned_uids,
          prev_assigned_uids: existingAssignedUids,
        }
      }, params)
      
    }
    // Otherwise update the stage status settings  
    else {
      //Find existing record and update with changes (only one will be passed at a time)
      await this.app.service('db/write/test-question-workflow-status')
      .db()
      .where({ test_question_id, lang, stage_id })
      .update({
        is_signed_off,
        is_done_editing,
        is_edit_review,
        is_changes_required
      });

      // Make a record in the log
      this.app.service('public/test-auth/question-change-log').create({
        test_question_id,
        lang,
        log_type: ChangeLogType.STAGE_STATUS,
        log_data: {
          stage_id,
          // Only one of these will be true
          is_signed_off: is_signed_off ? true : undefined,
          is_remove_signed_off: is_signed_off == 0 ? true : undefined,
          is_done_editing: is_done_editing ? true : undefined,
          is_remove_done_editing: is_done_editing == 0 ? true : undefined,
          is_edit_review: is_edit_review ? true : undefined,
          is_remove_edit_review: is_edit_review == 0 ? true : undefined,
          is_changes_required: is_changes_required ? true : undefined,
          is_remove_changes_required: is_changes_required == 0 ? true : undefined,
        }
      }, params)      
    }

    return {};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  /** From a list of question IDs, find their stage orders, assignees of the current stages, and details of assignees */
  async getStageInfoByQIds(questionIds:number[]){
    if (!questionIds.length) return {};
    const stageInfoMap:any = {};

    // Find any current stage records of the given question IDs
    const db:Knex = this.app.get('knexClientRead');
    const stageRecords = await db('test_question_workflow_status as status')
    .leftJoin('auth_workflow_stages as stage', 'stage.id', '=', 'status.stage_id')
    .whereIn('status.test_question_id', questionIds)
    .select('status.test_question_id', 'status.lang', 'status.stage_id', 'status.is_signed_off', 'status.is_done_editing', 'status.is_changes_required', 'stage.stage_order');

    // Rearrange into an object by question IDs and languages
    stageRecords.forEach(stageRecord => {
      const {lang, test_question_id, stage_order, is_signed_off, is_done_editing, is_changes_required} = stageRecord;
      if (!stageInfoMap[test_question_id]) stageInfoMap[test_question_id] = {}
      stageInfoMap[test_question_id][lang] = {
        order: stage_order, 
        // Mutually exclusive is_done_editing OR is_changes_required required for the Item in Edit stage, other stages use is_signed_off
        isStageWorkCompleted: !!is_signed_off || (!!is_done_editing || !!is_changes_required)
      }
    })

    let assignmentRecords:any[] = [];
    
    // If any questions have a current workflow stage, find if any users are assigned to them
    if (stageRecords.length) {
      assignmentRecords = await db('test_question_workflow_assignments as a')
        .where(function() {
          stageRecords.forEach(stageRecord => {
            this.orWhere(function() {
              this.where('a.test_question_id', stageRecord.test_question_id)
                .where('a.lang', stageRecord.lang)
                .where('a.stage_id', stageRecord.stage_id);
            });
          });
        })
      .select('a.test_question_id', 'a.lang', 'a.uid');
    }

    // Put list of assigned UIDs into the object
    assignmentRecords.forEach(assignmentRecord => {
      const {test_question_id, lang, uid} = assignmentRecord;
      if (!stageInfoMap[test_question_id][lang].assigneeUids) stageInfoMap[test_question_id][lang].assigneeUids = []
      stageInfoMap[test_question_id][lang].assigneeUids.push(uid)
    })

    // Find the details of any assigned users
    const assignmentUids: Set<number> = new Set();
    assignmentRecords.forEach(a => assignmentUids.add(a.uid))
    let stageUserDetails:any[] = []
    if (assignmentRecords.length) {
      stageUserDetails = await db('users as u')
      .whereIn('u.id', [...assignmentUids])
      .select('u.id as uid', 'u.first_name', 'u.last_name', 'u.contact_email');
    }
    // Rearrange user details into an object by UIDs
    const userDetailMap:any = {};
    stageUserDetails.forEach(u => {
      const {contact_email, first_name, last_name, uid} = u;
      userDetailMap[uid] = {uid, contact_email, last_name, first_name}
    })

    return {stageInfoMap, userDetailMap};
  }
}
