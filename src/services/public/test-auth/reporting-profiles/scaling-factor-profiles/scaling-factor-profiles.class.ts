import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';

interface ScalingFactorProfile {
  id?: number;
  authoring_group_id: number;
  source_export_id?: number;
  config: any;
}


interface ServiceOptions {}

export class ScalingFactorProfiles implements ServiceMethods<ScalingFactorProfile> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<ScalingFactorProfile[] | Paginated<ScalingFactorProfile>> {
    return this.app.service('db/read/rp-domain-score-scaling-factor-profile').find(params);
  }

  async get(id: Id, params?: Params): Promise<ScalingFactorProfile> {
    return this.app.service('db/read/rp-domain-score-scaling-factor-profile').get(id, params);
  }

  async create(data: ScalingFactorProfile, params?: Params): Promise<ScalingFactorProfile> {
    return this.app.service('db/write/rp-domain-score-scaling-factor-profile').create(data, params);
  }

  async update(id: NullableId, data: ScalingFactorProfile, params?: Params): Promise<ScalingFactorProfile> {
    throw new Errors.MethodNotAllowed('Use PATCH instead of UPDATE');
  }

  async patch(id: NullableId, data: Partial<ScalingFactorProfile>, params?: Params): Promise<ScalingFactorProfile> {
    if (!id) throw new Errors.BadRequest('ID_REQUIRED');
    return this.app.service('db/write/rp-domain-score-scaling-factor-profile').patch(id, data, params);
  }

  async remove(id: NullableId, params?: Params): Promise<ScalingFactorProfile> {
    throw new Errors.MethodNotAllowed('Removing scaling factor profiles is not allowed');
  }

}
