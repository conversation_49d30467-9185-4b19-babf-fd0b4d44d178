export const SQL_GET_ASMT_STRUCTURE_VIA_ITEM_SET = `
    SELECT sas.* FROM temp_question_set tqs 
    join se_assessment_structures sas 
        on sas.id = tqs.assessment_structure_id
    WHERE tqs.id = :item_set_id;
`

export const SQL_GET_ASMT_STRUCTURE_VIA_REP_PROF = `
    SELECT * 
    FROM se_assessment_structures sas
    WHERE reporting_profile_id = :reporting_profile_id
    ORDER BY id DESC;
`

export const SQL_GET_TWTT_TYPES_VIA_GRP_ID = `
    SELECT type_slug
    FROM test_window_td_types twtt
    WHERE authoring_group_id = :authoring_group_id 
    ORDER BY id DESC;
`

export const SQL_GET_ITEM_SETS = `
    SELECT * FROM se_item_set_structures
    WHERE id in (:item_set_ids);
`