// Initializes the `public/test-auth/reporting-profiles/text-node-refs` service on path `/public/test-auth/reporting-profiles/text-node-refs`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { LayoutConfigs } from './layout-configs.class';
import hooks from './layout-configs.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/reporting-profiles/layout-configs': LayoutConfigs & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/reporting-profiles/layout-configs', new LayoutConfigs(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/reporting-profiles/layout-configs');

  service.hooks(hooks);
}
