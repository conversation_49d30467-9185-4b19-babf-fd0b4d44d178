import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { dbRawRead, dbRawReadSingle } from '../../../../../util/db-raw';
import { IRepProfileDB, SQL_GET_LAYOUT_PROFILE } from '../layout-profiles/model/types';
import { dbDateNow } from '../../../../../util/db-dates';

interface Data {}

interface ServiceOptions {}

export class LayoutConfigs implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get(id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create(data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  /**
   * 
   * @param id the ID of the reporting profile to update
   * @param data the new layout config data in JSON form
   * @returns 
   */
  async patch(id: NullableId, data: any, params?: Params): Promise<Data> {
    if(!id) {
      throw new Errors.BadRequest('MISSING_ID');
    }
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const uid = await currentUid(this.app, params);
    // const config = this.app.service('public/test-auth/reporting-profiles/layout-profiles').getParsedConfig(data);
    const {config} = data;

    // Create new layout config
    const newLayoutConfig = await this.app.service('db/write/rp-layout-config').create({config, created_by_uid: uid})

    return await this.app.service('public/test-auth/reporting-profiles/layout-profiles').updateLayoutProfile(id, newLayoutConfig, 'layout_config_id', params);
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }


}
