// Initializes the `public/test-auth/reporting-profiles/reporting-profiles` service on path `/public/test-auth/reporting-profiles/reporting-profiles`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { ReportingProfiles } from './reporting-profiles.class';
import hooks from './reporting-profiles.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/reporting-profiles/reporting-profiles': ReportingProfiles & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/reporting-profiles/reporting-profiles', new ReportingProfiles(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/reporting-profiles/reporting-profiles');

  service.hooks(hooks);
}
