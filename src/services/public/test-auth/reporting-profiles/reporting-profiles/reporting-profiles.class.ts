import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { dbRawRead } from '../../../../../util/db-raw';
import { SQL_RP_REPORTING_PROFILES } from './model/sql';
import { ensureReqQueryAuthGroupIds } from '../../../../../util/access-control';
import { applyDbRecordJsonParse } from '../../../../../util/db-json';

interface ReportingProfileData {
  id?: number;
  authoring_group_id: number;
  slug: string;
  domain_schema_id: number;
  domain_score_scaling_factor_profile_id?: number | null;
  category_schema_id: number;
  cut_score_schema_id: number;
  default_cut_score_profile_id: number;
  config?: any;
  // plus: is_revoked, revoked_on, etc., if needed
}

interface ServiceOptions {}

export class ReportingProfiles implements ServiceMethods<ReportingProfileData> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }
  async find(params?: Params): Promise<ReportingProfileData[] | Paginated<ReportingProfileData>> {
    const authoring_group_ids = ensureReqQueryAuthGroupIds(this.app, params)
    const records = await dbRawRead(this.app, {authoring_group_ids}, SQL_RP_REPORTING_PROFILES)
    applyDbRecordJsonParse(records, ['config']);
    return records;
  }

  async get(id: Id, params?: Params): Promise<ReportingProfileData> {
    throw new Errors.MethodNotAllowed('INDIV_DISABLED'); // todo:would need to change the find query to pull up a list of ids, and then have a separate query for the joins, so that way the full query with all of the joins is always just taking a list of ids (which makes it easy to pass in a list of one)
  }

  async create(data: ReportingProfileData, params?: Params): Promise<ReportingProfileData> {
    // Validate or transform data if needed
    if (!params){
      throw new Errors.BadRequest()
    }
    if (!data.slug) {
      throw new Errors.BadRequest('SLUG_REQUIRED');
    }
    // gather variables
    const created_by_uid = await currentUid(this.app, params)
    const {
      authoring_group_id,
      slug,
    } = data;
    const config = '{}'; // start with a blank object
    // Insert into DB
    const newRecord = await this.app.service('db/write/rp-reporting-profiles').create({
      authoring_group_id,
      slug,
      created_by_uid,
      config
    });
    return newRecord;
  }

  async update(id: NullableId, data: ReportingProfileData, params?: Params): Promise<ReportingProfileData> {
    throw new Errors.MethodNotAllowed('REQ_PATCH_NOT_UPDATE');
  }

  async patch(id: NullableId, data: Partial<ReportingProfileData>, params?: Params): Promise<ReportingProfileData> {
    if (!id) {
      throw new Errors.BadRequest('ID_REQUIRED');
    }
    // Patch existing record
    const updated = await this.app.service('db/write/rp-reporting-profiles').patch(id, data, params);
    return updated;
  }

  async remove(id: NullableId, params?: Params): Promise<ReportingProfileData> {
    throw new Errors.MethodNotAllowed('DELETIONS_NOT_ALLOWED');
  }


}
