// export const SQL_RP_REPORTING_PROFILES = ` /*SQL_RP_REPORTING_PROFILES*/
// select rrp.id
//      , rrp.authoring_group_id 
//      , rrp.slug 
//      , rrp.created_on
//      , rrp.created_by_uid
//      , rrp.domain_schema_id
//      , rrp.domain_score_scaling_factor_profile_id
//      , rrp.category_schema_id
//      , rrp.cut_score_schema_id
//      , rrp.default_cut_score_profile_id
//      , rrp.config
//      , ag.type_slug ag_type_slug
//      , ag.name ag_name
// from rp_reporting_profiles rrp 
// join authoring_groups ag
// 	on ag.group_id = rrp.authoring_group_id
// where rrp.authoring_group_id in (:authoring_group_ids)
//   and rrp.is_revoked = 0
// `

export const SQL_RP_REPORTING_PROFILES = ` /*SQL_RP_REPORTING_PROFILES*/
select rrp.id
     , rrp.slug 
     , rrp.created_on
     , rrp.created_by_uid
     , ag.type_slug ag_type_slug
     , ag.name ag_name
from rp_reporting_profiles rrp 
join authoring_groups ag
	on ag.group_id = rrp.authoring_group_id
where rrp.authoring_group_id in (:authoring_group_ids)
  and rrp.is_revoked = 0
`