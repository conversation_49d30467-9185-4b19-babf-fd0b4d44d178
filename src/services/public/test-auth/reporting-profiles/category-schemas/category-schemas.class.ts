import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';

interface CategorySchema {
  id?: number;
  authoring_group_id: number;
  config: any;
}

interface ServiceOptions {}

export class CategorySchemas implements ServiceMethods<CategorySchema> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<CategorySchema[] | Paginated<CategorySchema>> {
    return this.app.service('db/read/rp-category-schema').find(params);
  }

  async get(id: Id, params?: Params): Promise<CategorySchema> {
    return this.app.service('db/read/rp-category-schema').get(id, params);
  }

  async create(data: CategorySchema, params?: Params): Promise<CategorySchema> {
    return this.app.service('db/write/rp-category-schema').create(data);
  }

  async update(id: NullableId, data: CategorySchema, params?: Params): Promise<CategorySchema> {
    throw new Errors.MethodNotAllowed('Use PATCH instead of UPDATE');
  }

  async patch(id: NullableId, data: Partial<CategorySchema>, params?: Params): Promise<CategorySchema> {
    if (!id) throw new Errors.BadRequest('ID_REQUIRED');
    return this.app.service('db/write/rp-category-schema').patch(id, data);
  }

  async remove(id: NullableId, params?: Params): Promise<CategorySchema> {
    throw new Errors.MethodNotAllowed('Removing category schemas is not allowed');
  }

}
