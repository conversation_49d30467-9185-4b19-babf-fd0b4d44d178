// Initializes the `public/test-auth/reporting-profiles/category-schemas` service on path `/public/test-auth/reporting-profiles/category-schemas`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { CategorySchemas } from './category-schemas.class';
import hooks from './category-schemas.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/reporting-profiles/category-schemas': CategorySchemas & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/reporting-profiles/category-schemas', new CategorySchemas(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/reporting-profiles/category-schemas');

  service.hooks(hooks);
}
