// Initializes the `public/test-auth/reporting-profiles/cut-score-profiles` service on path `/public/test-auth/reporting-profiles/cut-score-profiles`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { CutScoreProfiles } from './cut-score-profiles.class';
import hooks from './cut-score-profiles.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/reporting-profiles/cut-score-profiles': CutScoreProfiles & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/reporting-profiles/cut-score-profiles', new CutScoreProfiles(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/reporting-profiles/cut-score-profiles');

  service.hooks(hooks);
}
