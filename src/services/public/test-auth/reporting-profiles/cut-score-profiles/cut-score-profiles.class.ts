import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';

interface CutScoreProfile {
  id?: number;
  reporting_profile_id: number;
  rp_cut_score_schema_id?: number;
  source_export_id?: number;
  config: any;
}

interface ServiceOptions {}

export class CutScoreProfiles implements ServiceMethods<CutScoreProfile> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<CutScoreProfile[] | Paginated<CutScoreProfile>> {
    return this.app.service('db/read/rp-cut-score-profile').find(params);
  }

  async get(id: Id, params?: Params): Promise<CutScoreProfile> {
    return this.app.service('db/read/rp-cut-score-profile').get(id, params);
  }

  async create(data: CutScoreProfile, params?: Params): Promise<CutScoreProfile> {
    return this.app.service('db/write/rp-cut-score-profile').create(data);
  }

  async update(id: NullableId, data: CutScoreProfile, params?: Params): Promise<CutScoreProfile> {
    throw new Errors.MethodNotAllowed('Use PATCH instead of UPDATE');
  }

  async patch(id: NullableId, data: Partial<CutScoreProfile>, params?: Params): Promise<CutScoreProfile> {
    if (!id) throw new Errors.BadRequest('ID_REQUIRED');
    return this.app.service('db/write/rp-cut-score-profile').patch(id, data);
  }

  async remove(id: NullableId, params?: Params): Promise<CutScoreProfile> {
    throw new Errors.MethodNotAllowed('Removing cut score profiles is not allowed');
  }

}
