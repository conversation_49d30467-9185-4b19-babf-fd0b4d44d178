import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';

interface CutScoreSchema {
  id?: number;
  authoring_group_id: number;
  config: any;
}

interface ServiceOptions {}

export class CutScoreSchemas implements ServiceMethods<CutScoreSchema> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<CutScoreSchema[] | Paginated<CutScoreSchema>> {
    return this.app.service('db/read/rp-cut-score-schema').find(params);
  }

  async get(id: Id, params?: Params): Promise<CutScoreSchema> {
    return this.app.service('db/read/rp-cut-score-schema').get(id, params);
  }

  async create(data: CutScoreSchema, params?: Params): Promise<CutScoreSchema> {
    return this.app.service('db/write/rp-cut-score-schema').create(data);
  }

  async update(id: NullableId, data: CutScoreSchema, params?: Params): Promise<CutScoreSchema> {
    throw new Errors.MethodNotAllowed('Use PATCH instead of UPDATE');
  }

  async patch(id: NullableId, data: Partial<CutScoreSchema>, params?: Params): Promise<CutScoreSchema> {
    if (!id) throw new Errors.BadRequest('ID_REQUIRED');
    return this.app.service('db/write/rp-cut-score-schema').patch(id, data);
  }

  async remove(id: NullableId, params?: Params): Promise<CutScoreSchema> {
    throw new Errors.MethodNotAllowed('Removing cut score schemas is not allowed');
  }

}
