// Initializes the `public/test-auth/reporting-profiles/cut-score-schemas` service on path `/public/test-auth/reporting-profiles/cut-score-schemas`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { CutScoreSchemas } from './cut-score-schemas.class';
import hooks from './cut-score-schemas.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/reporting-profiles/cut-score-schemas': CutScoreSchemas & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/reporting-profiles/cut-score-schemas', new CutScoreSchemas(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/reporting-profiles/cut-score-schemas');

  service.hooks(hooks);
}
