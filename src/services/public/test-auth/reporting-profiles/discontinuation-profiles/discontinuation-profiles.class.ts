import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { dbRawRead, dbRawReadSingle } from '../../../../../util/db-raw';
import { dbDateNow } from '../../../../../util/db-dates';
import { SQL_GET_DISC_RULE_CONFIG } from './model/types';

interface Data {}

interface ServiceOptions {}

export class DiscontinuationProfiles implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get(id: Id, params?: Params): Promise<Data> {

    return await this.getDiscontinuationRuleConfig(+id);
  }

  async getDiscontinuationRuleConfig(tempQuestionSetID: number) {
    const profile = await dbRawReadSingle(this.app, {id: +tempQuestionSetID}, SQL_GET_DISC_RULE_CONFIG)

    return profile ?? null;
  }

  async create(data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch(id: NullableId, data: any, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }


}
