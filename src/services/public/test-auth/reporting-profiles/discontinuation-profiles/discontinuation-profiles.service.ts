// Initializes the `public/test-auth/reporting-profiles/text-node-refs` service on path `/public/test-auth/reporting-profiles/text-node-refs`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { DiscontinuationProfiles } from './discontinuation-profiles.class';
import hooks from './discontinuation-profiles.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/reporting-profiles/discontinuation-profiles': DiscontinuationProfiles & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/reporting-profiles/discontinuation-profiles', new DiscontinuationProfiles(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/reporting-profiles/discontinuation-profiles');

  service.hooks(hooks);
}
