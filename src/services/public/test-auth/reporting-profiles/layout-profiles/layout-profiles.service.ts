// Initializes the `public/test-auth/reporting-profiles/layout-profiles` service on path `/public/test-auth/reporting-profiles/layout-profiles`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { LayoutProfiles } from './layout-profiles.class';
import hooks from './layout-profiles.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/reporting-profiles/layout-profiles': LayoutProfiles & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/reporting-profiles/layout-profiles', new LayoutProfiles(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/reporting-profiles/layout-profiles');

  service.hooks(hooks);
}
