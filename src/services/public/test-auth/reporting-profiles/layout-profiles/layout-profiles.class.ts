import { Id, NullableId, Pa<PERSON>ated, Params, ServiceAddons, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { dbRawRead, dbRawReadSingle } from '../../../../../util/db-raw';
import { IRepProfileDB, SQL_GET_LAYOUT_PROFILE } from './model/types';
import { dbDateNow } from '../../../../../util/db-dates';

interface Data {}

interface ServiceOptions {}

export class LayoutProfiles implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get(id: Id, params?: Params): Promise<Data> {
    const profile = await dbRawReadSingle(this.app, {id: +id}, SQL_GET_LAYOUT_PROFILE)

    return profile;
  }

  async create(data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch(id: NullableId, data: Partial<Data>, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  /**
   * Generalized helper function for updating layout profiles with new IDs.
   * Revokes the old rp_layout_profiles record and creates a new version, then updates the layout_profile_id in rp_reporting_profiles
   * @param id the ID of the rp_reporting_profiles record to update
   * @param newEntity the newly created object with an id property
   * @param idColumnName name of the db column in rp_layout_profiles to update
   * @param params 
   * @returns the newly updated rp_reporting_profiles record.
   */
  async updateLayoutProfile(id: Id, newEntity: any, idColumnName: string, params: Params): Promise<Data> {
    const uid = await currentUid(this.app, params);

    const dbNow = dbDateNow(this.app);

    const currRepProfile: IRepProfileDB = await dbRawReadSingle(this.app, {id: +id}, SQL_GET_LAYOUT_PROFILE)
    const {layout_profile_id} = currRepProfile

    // Duplicate layout profile with new node ref
    const currLayoutProfile = await this.app.service('db/write/rp-layout-profiles').get(layout_profile_id)
    const newLayoutProfileTemplate = {...currLayoutProfile};
    // Delete columns that shouldn't be brought over
    delete newLayoutProfileTemplate.id;
    delete newLayoutProfileTemplate.revoked_on;
    delete newLayoutProfileTemplate.revoked_by_uid;
    delete newLayoutProfileTemplate.is_revoked;
    delete newLayoutProfileTemplate.created_on;

    // Assign new values
    newLayoutProfileTemplate.created_by_uid = uid;
    newLayoutProfileTemplate[idColumnName] = newEntity.id;

    const newLayoutProfile = await this.app.service('db/write/rp-layout-profiles').create(newLayoutProfileTemplate);
    
    // Revoke old profile
    await this.app.service('db/write/rp-layout-profiles').patch(layout_profile_id, {is_revoked: 1, revoked_on: dbNow, revoked_by_uid: uid})

    return await this.app.service('db/write/rp-reporting-profiles').patch(+id, {layout_profile_id: newLayoutProfile.id})
  }

  getParsedConfig(data: any) {
    try {
      return JSON.stringify(data);
    } catch (err) {
      throw new Errors.GeneralError('ERROR_PARSING_CONFIG', err);
    }
  }
  
}
