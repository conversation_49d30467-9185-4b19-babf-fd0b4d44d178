export const SQL_GET_LAYOUT_PROFILE_FROM_TWTDAR = `
  select  rrp.id
        , rlp.id layout_profile_id
        , rlc.id layout_config_id
        , rtnr.id text_node_refs_id
        , rrp.slug
        , rlc.config layout_config
        , rtnr.config text_node_ref_config 
  from test_window_td_alloc_rules twtar 
  join rp_reporting_profiles rrp 
    on rrp.id = twtar.reporting_profile_id 
  join rp_layout_profiles rlp 
    on rlp.id = rrp.layout_profile_id 
  join rp_layout_config rlc 
    on rlc.id = rlp.layout_config_id 
  join rp_text_node_refs rtnr 
    on rtnr.id = rlp.text_node_refs_id
  where twtar.id = :twtar_id;
`

export const SQL_GET_LAYOUT_PROFILE = `
  select  rrp.id
        , rlp.id layout_profile_id
        , rlc.id layout_config_id
        , rtnr.id text_node_refs_id
        , rrp.slug
        , rlc.config layout_config
        , rtnr.config text_node_ref_config 
  from rp_reporting_profiles rrp
  join rp_layout_profiles rlp 
    on rlp.id = rrp.layout_profile_id 
  join rp_layout_config rlc 
    on rlc.id = rlp.layout_config_id 
  join rp_text_node_refs rtnr 
    on rtnr.id = rlp.text_node_refs_id
  where rrp.id = :id;
`

export enum FONT_STYLES {
  BOLD = 'bold',
  REGULAR = 'regular'
}

export type TAlign = 'left' | 'right' | 'center'

export enum BLOCK_TYPES {
  TEXT = 'TEXT',
  OVERALL = 'OVERALL',
  HEADER = 'HEADER',
  GRAPHS = 'GRAPHS',
  DIVIDER = 'DIVIDER',
  FOOTER = 'FOOTER'
}

export interface ILayoutBlock {
  blockType: BLOCK_TYPES,
  columns?: ILayoutNode[][],
  data?: ILayoutNode
}

export interface ILayoutNode {
  marginTop?: number
}
export interface ILayoutText extends ILayoutNode{
  slug: string,
  align: TAlign
  fontStyle: FONT_STYLES,
  fontSize: number
}

export interface ILayoutGraphs extends ILayoutNode{
  showUnits?: boolean,
  usePercentage?: boolean
}

export interface ILayoutOverall extends ILayoutGraphs {
  titleSlug: string,
  contentSlug: string,
  titleFontSize: number,
  contentFontSize: number
}

export interface ILayoutHeader extends ILayoutNode {
  // slug: string,
  // align: TAlign
  // fontStyle: TFontStyle,
  // fontSize: number
}

export interface ILayoutConfig {
  normalFont: string,
  boldFont: string,
  layout: ILayoutBlock[],
}

export interface INodeRefConfig {
  [key: string]: {en: string, fr: string}
}

export interface IRepProfileDB {
  id: number,
  slug: string,
  layout_config: string,
  text_node_ref_config: string,
  layout_profile_id: number,
  layout_config_id: number,
  text_node_refs_id: number
}

export const OVERALL_HEADER = "OVERALL_HEADER";

export const OVERALL_CONTENT = "OVERALL_CONTENT";

export const FOOTER_HEADER = "FOOTER_HEADER";

export const FOOTER_CONTENT = "FOOTER_CONTENT";