import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';

interface DomainSchemaData {
  id?: number;
  authoring_group_id: number;
  config: any;
  // other fields like created_on, etc.
}


interface ServiceOptions {}

export class DomainSchemas implements ServiceMethods<DomainSchemaData> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<DomainSchemaData[] | Paginated<DomainSchemaData>> {
    return this.app.service('db/read/rp-domain-schema').find(params);
  }

  async get(id: Id, params?: Params): Promise<DomainSchemaData> {
    return this.app.service('db/read/rp-domain-schema').get(id, params);
  }

  async create(data: DomainSchemaData, params?: Params): Promise<DomainSchemaData> {
    return this.app.service('db/write/rp-domain-schema').create(data);
  }

  async update(id: NullableId, data: DomainSchemaData, params?: Params): Promise<DomainSchemaData> {
    throw new Errors.MethodNotAllowed();
  }

  async patch(id: NullableId, data: Partial<DomainSchemaData>, params?: Params): Promise<DomainSchemaData> {
    if (!id) throw new Errors.BadRequest('ID_REQUIRED');
    return this.app.service('db/write/rp-domain-schema').patch(id, data);
  }

  async remove(id: NullableId, params?: Params): Promise<DomainSchemaData> {
    throw new Errors.MethodNotAllowed();
  }
  
}
