type params = { [key: string | number]: any }
type caption = { en: string, fr: string }
export interface ISeItem {
    slug: string,
    item_params: params,
    val_max?: number,
    caption?: caption
}

export interface ISeItemSlugDefaults {
    [key: string]: { // item slug
        caption: caption,
        item_params: params,
        val_max?: number
    } 
}

export interface ISEItemSet {
    slug: string,
    caption: caption,
    item_params: params,
    items: ISeItem[],
}

export interface IItemSetStructuresDB {
    id: number,
    slug: string,
    config: {
        sets: ISEItemSet[],
        item_params: params,
        item_slug_defaults: ISeItemSlugDefaults
    }
    created_by_uid: number,
    created_on: string
}

export type TItemSetSlugMapping = { 
    [key: string]: { 
        item_slug_defaults: ISeItemSlugDefaults,
        item_params: params,
        sets: { 
            [key: string]: ISEItemSet 
        } 
    }
}