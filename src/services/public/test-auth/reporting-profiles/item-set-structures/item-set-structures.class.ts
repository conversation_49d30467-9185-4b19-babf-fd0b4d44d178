import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { dbRawRead, dbRawReadSingle } from '../../../../../util/db-raw';
import { IRepProfileDB, SQL_GET_LAYOUT_PROFILE } from '../layout-profiles/model/types';
import { dbDateNow } from '../../../../../util/db-dates';
import { SQL_GET_ITEM_SET_STRUCTURES } from './model/sql';
import { IItemSetStructuresDB } from './model/types';

interface Data {}

interface ServiceOptions {}

export class ItemSetStructures implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {item_set_structure_ids} = params.query;

    return await dbRawRead(this.app, {item_set_structure_ids}, SQL_GET_ITEM_SET_STRUCTURES)
  }

  async get(id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create(data: Partial<IItemSetStructuresDB>, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    delete data.id;
    delete data.created_on
    const uid = await currentUid(this.app, params)
    data.created_by_uid = uid;
    if(data.config) {
     (data.config as any) = JSON.stringify(data.config);
    }
    
    return await this.app.service('db/write/se-item-set-structures').create(data);
  }

  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch(id: NullableId, data: any, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }


}
