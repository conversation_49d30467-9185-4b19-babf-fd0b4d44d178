/**
 * Name: ViewScoringLeaderResponses
 * Description: <PERSON><PERSON><PERSON> for rendering the responses view for scoring leader.
 */
import { Component, OnInit} from '@angular/core';
import { SidepanelService } from '../../core/sidepanel.service';
import { UserSiteContextService } from '../../core/usersitecontext.service';
import { Router, ActivatedRoute } from '@angular/router';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { RoutesService } from '../../api/routes.service';
import { AccountType } from '../../constants/account-types';
import { DomSanitizer } from '@angular/platform-browser';
import { AuthService } from '../../api/auth.service';
import { IMenuTabConfig } from '../../ui-partial/menu-bar/menu-bar.component';
import { formatDate } from '@angular/common';
import { trace } from 'console';
import { renderOptionCaption } from '../../ui-scorer/panel-scoring/util/score-option-caption';
import { IScoreOption } from '../../ui-scorer/panel-scoring/types/score-option';
import moment from 'moment';
import { generateSanitizedRecords } from '../../scoring-leader-utils/sanitizeTableRows';
import { mtz } from '../../core/util/moment';
import * as _ from 'lodash';
import { ResponseBatch, ResponseBatchService } from '../../ui-scorer/response-batch.service';
import { IBatchHistory, IRead, IResponseBatch, IResponseMark, IResponseMarkState } from '../../ui-scorer/panel-scoring/types/response-batch';
import { IScoringFlagOption } from '../../ui-scorer/panel-scoring/types/scoring-flag-option';
import { GridApi } from 'ag-grid-community';
import { GroupBatch, IScale, IScoreProfileGroup, isResponseBatchCombinationInvalid, isResponseBatchRangeInvalid, isResponseMarked } from '../../ui-scorer/panel-scoring/util/util';
import { IAssignedItem } from '../../ui-scorer/view-scorer-dashboard/models/assigned-item';
import { IItemRules } from '../../ui-scorer/panel-scoring/types/item-rules';
import { DOMAIN_LOCK_MESSAGE } from 'src/app/api/constants/access-controls';
import { getScoringLeaderPanels } from './../../core/main-nav/panels/scoring-leader'
import { renderTrueFalse } from 'src/app/core/util/render';
import { AG_GRID_LOCALE_EN,  AG_GRID_LOCALE_FR } from '@ag-grid-community/locale';
import { renderYesNo } from '../../core/util/render';

export const TEMP_READ_RULES = [
  '',
  'THIRD_READ_RULE_TYPE',
  'FOURTH_READ_RULE_TYPE',
]

const coldef = (field:string, headerName:string, options?) => {
  return {
    field,
    headerName,
    sortable:true,
    filter:true,
    ... (options || {})
  }
}

enum ScorerMenuView {
  SCORINGS = 'scorings',
  S2S = 's2s',
  S2S_CIN = 's2s_cin',
  NOT_YET_CLAIMED = 'not_yet_claimed',
  NOT_YET_CLAIMED_CAEC = 'not_yet_claimed_caec',
}


enum SlugCode {
  INS = "INS",
  NO_RESPONSE = "NO_RESPONSE",
  UNFAMILIAR_LIT = "UNF"
}

enum RepoolTypes {
  POOL = 'POOL',
  MARKER = "MARKER"
}
@Component({
  selector: 'view-sl-response-scores',
  templateUrl: './view-sl-response-scores.component.html',
  styleUrls: ['./view-sl-response-scores.component.scss']
})
export class ViewSlResponseScoresComponent implements OnInit {
 scorerMenuViewName = {
  [ScorerMenuView.SCORINGS]: this.lang.tra("lbl_sl_scorings"),
  [ScorerMenuView.S2S]: this.lang.tra("lbl_sl_responses_tab_sent_to_supervisor"),
  [ScorerMenuView.S2S_CIN]: this.lang.tra('lbl_flag_child_in_need'),
  [ScorerMenuView.NOT_YET_CLAIMED]: this.lang.tra('lbl_sl_responses_tab_unclaimed'),
}

  inspectedCellRenderer = function(params) {
    if(!!params.data.inspected) {
      return `<span style="font-weight:700">Yes</span>`
    }
    else {
      return `<span style="color:#ccc">No</span>`;
    }
  }

  constructor(
    public whitelabelService: WhitelabelService,
    private route: ActivatedRoute,
    private router: Router,
    private sidePanel: SidepanelService,
    private lang: LangService,
    private auth: AuthService,
    private userSiteContext: UserSiteContextService,
    public  loginGuard: LoginGuardService,
    public  sanitizer: DomSanitizer,
    private breadcrumbsService: BreadcrumbsService,
    private routes: RoutesService,
    private responseBatchService: ResponseBatchService
  ) { }

  markingWindowId:number;
  breadcrumb:any[];
  ScorerMenuView = ScorerMenuView;
  
  batchGroups: any[] = [];
  batchGroupsFiltered: any[] = [];
  s2sFiltered: any[] =[];
  s2s: any[] = [];
  childInNeed: any[] = [];
  childInNeedFiltered: any[] = [];
  notYetClaimed = [];

  canLoadTable;
  domLayout;

  scoreOptions;
  selectedItemsStack = [];

  selectedView; // = ScorerMenuView.SCORINGS;
  views: IMenuTabConfig<ScorerMenuView>[];

  currentResponseScorerUid: number; // The uid of the original scorer. Needed to get response content.
  currentHistoryScorerUid: number = this.auth.myUID();; // The uid of the current scorer.
  currentUid: number = this.auth.myUID();
  currentMarkerReadId: number;
  assignmentItemCode: string;
  isSupervisorRestrictedView: boolean = false;
  scorerHistory: Map<number, IRead> | null = null;
  scorerValidHistory: Map<number, IRead> | null = null;
  multiScaleResponseBatchesMap: Map<number, ResponseBatch>;
  isSingleResponseLoaded = false;
  flagMessage: string = '';
  activeFlagId:number;
  flagOptions: IScoringFlagOption[];
  isShowFlagOptions:boolean = false;
  isHistoryShowing = false;
  currentGroupId: number;
  isShowHistory:boolean = false;
  scoreProfileGroups: Map<number, IScoreProfileGroup> = new Map();
  isHistoryLoading:boolean = false;
  isRepoolUnfDisabled:boolean = true;
  SlugCode = SlugCode;
  markingWindowGroupId:number;
  markingWindowName:string;
  isScanReassignAllowed: boolean = false;
  isScoringsInspectAccess: boolean = false;
  isGroupMarking: boolean = false;

  ngOnInit(): void {
    this.loginGuard.activate();
    this.route.params.subscribe(params => {
      this.markingWindowId = +params['markingWindow'];
      this.assignmentItemCode = params['assignmentItemCode'];
      this.initTabs()
      this.ensureSupervisorRestrictedOption();
      this.updateBreadcrumb();
    });
    if (this.markingWindowId){
      this.auth.apiGet('public/scor-lead/windows', this.markingWindowId).then(
        (scoringWindowMeta) => {
          this.markingWindowName = scoringWindowMeta.marking_window_name + (scoringWindowMeta.is_rescore?" (Rescore Mode: ON)": "");
          this.markingWindowGroupId = scoringWindowMeta.group_id;
          this.isScanReassignAllowed = !!scoringWindowMeta.is_scan_reassign_allowed
          this.isScoringsInspectAccess = !!scoringWindowMeta.is_scorings_inspect_access
          this.isGroupMarking = !!scoringWindowMeta.is_group_marking
        }
      );
    }
    this.initTabs();
    if (!this.isSupervisorRestrictedView){
      getScoringLeaderPanels(this.auth, this.routes).then(scoringLeaderPanels => {
        this.sidePanel.activate(scoringLeaderPanels, true);
        this.sidePanel.unexpand();
      })
    }
  }

  ensureSupervisorRestrictedOption(){
    this.isSupervisorRestrictedView = !!this.assignmentItemCode;
    this.selectView(ScorerMenuView.S2S);
  }
  
  async initTabs(){
    
    const casAccounts = await this.auth.apiGet('public/scor-lead/accounts', this.markingWindowId, {query: {roleTypes: 'mrkg_sensi', excludeRevoked: true}});
    const currUserUid = this.auth.getUid();
    const hasSensitiveAccess = casAccounts.filter(a => !a.is_revoked).map(a => a.uid).includes(currUserUid);

    // Initialize the views first
    this.views = [];
    if (!this.isSupervisorRestrictedView || this.isScoringsInspectAccess){
      this.views.push({id: ScorerMenuView.SCORINGS, caption: this.scorerMenuViewName[ScorerMenuView.SCORINGS] })
    }
    this.views.push({id: ScorerMenuView.S2S, caption: this.scorerMenuViewName[ScorerMenuView.S2S] })
    if (!this.isSupervisorRestrictedView){
      if (hasSensitiveAccess) {
        this.views.push({id: ScorerMenuView.S2S_CIN, caption: this.scorerMenuViewName[ScorerMenuView.S2S_CIN] })
      }
      this.views.push({id: ScorerMenuView.NOT_YET_CLAIMED, caption: this.scorerMenuViewName[ScorerMenuView.NOT_YET_CLAIMED] })
    }

    // Then load the counts and add them to the tabs
    this.loadTabCounts();
  }

  async loadTabCounts(){
    // Pull the counts, adjust the tab names to include counts
    const query = { 
      markingWindowId: this.markingWindowId,
      isGetTabCounts: 1,
      isSupervisorView: this.isSupervisorRestrictedView ? 1 : 0,
      assignmentItemCode: this.assignmentItemCode
    }
    let tabCounts = await this.auth
      .apiFind(this.routes.SCOR_SCOR_BATCH_GROUPS, { query })
      .catch(e => {
        const {message, data} = e;
        if (e.message == 'DOMAIN_LOCKED'){
          this.loginGuard.quickPopup(DOMAIN_LOCK_MESSAGE + data.lockedDomain);
        }
      })
    if (!tabCounts){ tabCounts = []; throw new Error()}
    this.views.forEach(view => {
      view.caption = this.scorerMenuViewName[view.id] + (tabCounts[view.id] !== undefined ? ` (${tabCounts[view.id]})` : '')
    })
  }

  async selectView(viewId:ScorerMenuView){
    if (this.selectedView !== viewId){
      this.selectedView = viewId;
      this.availableScorers = [];
      this.isFocusedOnStudentResponse = false;
      this.clearData();
      await this.loadResponses();
    }
  }
  getCurrentGridAPI(view = this.selectedView){
    switch(view) {
      case ScorerMenuView.S2S:
        return this.gridApiS2S;
      case ScorerMenuView.S2S_CIN:
        return this.gridApiCIN;
      case ScorerMenuView.SCORINGS:
        return this.gridApiSCORINGS;
      case ScorerMenuView.NOT_YET_CLAIMED:
        return this.gridApiNYC;
      case ScorerMenuView.NOT_YET_CLAIMED_CAEC:
        return this.gridApiNYC;
      default:
        return null
    }
  }

  async reloadValidationTool(){
    this.isLoadingFlaggedRR = true;
    this.auth.apiGet(this.routes.SCOR_LEAD_REPROCESS_SCORING_READS, this.markingWindowId)
      .then((res) => {
        this.flaggedRR = res;
        console.log(this.flaggedRR);
        this.filterByValidationTool();
      }).catch((err) => {
        alert(`Error loading flagged read routing ${err.message}`);
        this.isLoadingFlaggedRR = false;
      }).finally(()=>{
      this.isLoadingFlaggedRR = false;
      });
    return;
  }

  filterByValidationTool(){
    this.batchGroupsFiltered = [];
    const filteredBatchGroupFlags = new Map();
    const filteredBatchGroupIds = this.flaggedRR.map((responses)=> {
      const flags = [];
      responses.flags.forEach((flag)=>{
        if(!flags.includes(flag.type)){
          flags.push(flag.type);
        }
      })
      filteredBatchGroupFlags.set(responses.batchGroupId, flags.join(","))
      return responses.batchGroupId;
    })
    
    this.batchGroupsFiltered = this.batchGroups.filter((group) => filteredBatchGroupIds.includes(group.id));
    this.batchGroupsFiltered.forEach((group) => {
      group.flag = filteredBatchGroupFlags.get(group.id);
    })
  }

  clearCurrentGridApi(view = this.selectedView){
    switch(view) {
      case ScorerMenuView.S2S:
        this.gridApiS2S = null;
      case ScorerMenuView.S2S_CIN:
        this.gridApiCIN = null;
      case ScorerMenuView.SCORINGS:
        this.gridApiSCORINGS = null;
      case ScorerMenuView.NOT_YET_CLAIMED:
        this.gridApiNYC = null;
        case ScorerMenuView.NOT_YET_CLAIMED_CAEC:
          this.gridApiNYC = null;
      default:
        return null
    }
  }
  getBaseRoute(){
    return `/${this.lang.c()}/${AccountType.SCOR_LEAD}`;
  }
  updateBreadcrumb(){
    // getTrainingTypeCaption(this.trainingType);
    const currentTabName = this.isSupervisorRestrictedView ? `Responses - ${this.assignmentItemCode}` : 'Responses';
    if (this.isSupervisorRestrictedView){
      this.breadcrumb = [
        this.breadcrumbsService._CURRENT( 'Scorer View', `/${this.lang.c()}/${AccountType.SCOR_SCOR}/dashboard`),
        this.breadcrumbsService._CURRENT( currentTabName, this.router.url),
      ];
    }
    else{
      const baseRoute = this.getBaseRoute();
      this.breadcrumb = [
        this.breadcrumbsService._CURRENT( this.lang.tra('lbl_osslt_scorlead_dashboard'), `${baseRoute}/dashboard`),
        this.breadcrumbsService._CURRENT( currentTabName, this.router.url),
      ];
    }
  }

  // columnDefs = [
  //   { field: 'make'  , sortable: true, filter: true, checkboxSelection: true},
  //   { field: 'model' , sortable: true, filter: true},
  //   { field: 'price' , sortable: true, filter: true}
  // ];
  defaultColDef = {
    resizable: true,
  }

  

  COLDEF_SCORING_ID = coldef('mcbr_id', 'Scoring ID', { 
    checkboxSelection:true,
    headerCheckboxSelection:true,
    headerCheckboxSelectionFilteredOnly:true,
    width:130
  })
  COLDEF_RESPONSE_ID = coldef('taqr_id', 'Response ID', { width:130 });
  COLDEF_BATCH_ID = coldef('claimed_batch_id', 'Batch ID', { width:20 });
  COLDEF_SCORER = coldef('scorer_name', 'Scorer', { width:200 });
  COLDEF_ITEM = coldef('item_caption', 'Item', {  });
  COLDEF_INSPECTED = coldef('inspected', 'Inspected', {  
    cellRenderer:this.inspectedCellRenderer,
    width:140,
    filter: false,
  });
  COLDEF_IS_EXPERT_SCORE = coldef('is_expert_score', 'Expert Score?', {cellRenderer: params => renderTrueFalse(this.lang, params.value)});
  COLDEF_SCORE = coldef('score', 'Score', { width:100  });
  COLDEF_FLAG = coldef('flag', 'Flag', { width:100  });
  COLDEF_SCORED_ON = coldef('date', this.lang.tra('sl_responses_col_scored_on'));
  COLDEF_IS_INVALID = coldef('is_invalid', 'Previously Invalidated?', {  });
  COLDEF_FLAG_MESSAGE = coldef('flagMessage', 'Message?', { 
    tooltipField:'flagMessage',
    width:240,
    suppressSizeToFit:true,
  });
  COLDEF_VALIDITY_EXACT = coldef('is_validity_exact', 'Exact?', {  });
  COLDEF_VALIDITY_ADJ = coldef('is_validity_adj', 'Adjacent?', {  });
  COLDEF_BATCH_GROUP_ID = coldef('id', this.lang.tra('lbl_sl_scor_cols_group_id'), { 
    checkboxSelection:true,
    headerCheckboxSelection:true,
    headerCheckboxSelectionFilteredOnly:true,
    width:130
  });
  COLDEF_ASSESSMENT = coldef('caption', 'Assessment');
  COLDEF_UID = coldef('created_by_uid','Scorer UID');
  COLDEF_FLAGS = coldef('flags', this.lang.tra('lbl_sl_scor_cols_flags'))
  COLDEF_TA_ID = coldef('test_attempt_id', 'Test Attempt ID')
  COLDEF_PROFILE_ID = coldef('profile_id', 'Profile ID')
  COLDEF_MARKER_NUMBER = coldef('swum_marker_number', 'Marker Number', {
    valueGetter: params => {
      if (params.data.swum_marker_number) return params.data.swum_marker_number
      else if (params.data.max_valid_read_uid) return 'Leader'
    },
    hide: !!this.isScoringsInspectAccess
  })
  COLDEF_MAX_READ_LEVEL = coldef('swum_max_read_level', 'Max Read Level')
  // mcbg.id, mwi.caption, count(mcb.id) as 'batch_count', mcbg.created_by_uid, mcbg.created_on

  COLDEF_IS_AVAILABLE_TO_SCORERS = coldef('is_available_to_scorers', this.lang.tra('lbl_sl_scor_avail_to_scorers'), {cellRenderer: params => renderTrueFalse(this.lang, params.value)})
  COLDEF_IS_RESCORE_INDIC = coldef('is_rescore_indic', this.lang.tra('lbl_sl_scor_cols_needs_additional_read'), {cellRenderer: params => renderTrueFalse(this.lang, params.value)})
  COLDEF_INCLUDES_NR = coldef('includes_nr', this.lang.tra('lbl_sl_scor_cols_confirmed_flag'), {cellRenderer: params => renderTrueFalse(this.lang, params.value)})
  COLDEF_IS_READ_RULES_PROCESSED = coldef('is_read_rules_processed', this.lang.tra('lbl_sl_scor_cols_needs_read_check'), {cellRenderer: params => renderTrueFalse(this.lang, params.value)})
  COLDEF_IS_INSPECTED = coldef('is_inspected', this.lang.tra('lbl_sl_scor_cols_inspected'), {cellRenderer: params => {
    if (params.data.is_inspected) return this.lang.tra('lbl_yes')
    else if (!params.data.is_inspected && params.data.is_partially_inspected) return 'Partially'
    else  return this.lang.tra('lbl_no')
  }})
  COLDEF_ASSIGNMENT_ITEM_CODE = coldef('assignment_item_code', this.lang.tra('lbl_sl_cols_asmt_code'));
  COLDEF_TEST_ATTEMPT_ID = coldef('test_attempt_id', this.lang.tra('lbl_sl_cols_ta_id'));
  COLDEF_NUM_READS = coldef('num_reads', this.lang.tra('lbl_sl_cols_num_reads'));
  COLDEF_COMMENTS = coldef('comments', this.lang.tra('lbl_sl_scor_comments'));


  columnDefsBatchGroup = [
    this.COLDEF_BATCH_GROUP_ID,
    this.COLDEF_ASSIGNMENT_ITEM_CODE,
    this.COLDEF_TEST_ATTEMPT_ID,
    this.COLDEF_NUM_READS,
    this.COLDEF_IS_EXPERT_SCORE,
    this.COLDEF_IS_INSPECTED,
    this.COLDEF_IS_AVAILABLE_TO_SCORERS,
    this.COLDEF_IS_RESCORE_INDIC,
    this.COLDEF_INCLUDES_NR,
    this.COLDEF_IS_READ_RULES_PROCESSED,
    this.COLDEF_IS_INVALID,
    this.COLDEF_FLAGS,
    this.COLDEF_SCORED_ON,
  ]

  columnDefsBatchGroup__supervisor = [
    this.COLDEF_BATCH_GROUP_ID,
    this.COLDEF_ASSIGNMENT_ITEM_CODE,
    this.COLDEF_TEST_ATTEMPT_ID,
    this.COLDEF_NUM_READS,
    this.COLDEF_IS_EXPERT_SCORE,
    this.COLDEF_IS_INSPECTED,
    this.COLDEF_IS_AVAILABLE_TO_SCORERS,
    this.COLDEF_FLAGS,
    this.COLDEF_SCORED_ON,
  ]

  columnDefsBatchGroup__supervisor__inspect = [
    this.COLDEF_BATCH_GROUP_ID,
    this.COLDEF_ASSIGNMENT_ITEM_CODE,
    this.COLDEF_TEST_ATTEMPT_ID,
    this.COLDEF_MARKER_NUMBER,
    this.COLDEF_NUM_READS,
    this.COLDEF_IS_INSPECTED,
    this.COLDEF_IS_AVAILABLE_TO_SCORERS,
    this.COLDEF_FLAGS,
    this.COLDEF_SCORED_ON,
  ]

  columnDefsS2s__supervisor = [
    this.COLDEF_BATCH_GROUP_ID,
    this.COLDEF_ASSIGNMENT_ITEM_CODE,
    this.COLDEF_TEST_ATTEMPT_ID,
    this.COLDEF_FLAGS,
    this.COLDEF_COMMENTS,
    this.COLDEF_NUM_READS,
    this.COLDEF_IS_INSPECTED,
    this.COLDEF_IS_AVAILABLE_TO_SCORERS,
    this.COLDEF_SCORED_ON,
  ]

  columnDefsS2s__supervisor__inspect = [
    this.COLDEF_BATCH_GROUP_ID,
    this.COLDEF_ASSIGNMENT_ITEM_CODE,
    this.COLDEF_TEST_ATTEMPT_ID,
    this.COLDEF_MARKER_NUMBER,
    this.COLDEF_FLAGS,
    this.COLDEF_COMMENTS,
    this.COLDEF_NUM_READS,
    this.COLDEF_IS_INSPECTED,
    this.COLDEF_IS_AVAILABLE_TO_SCORERS,
    this.COLDEF_SCORED_ON,
  ]

  columnDefsS2s = [
    this.COLDEF_BATCH_GROUP_ID,
    this.COLDEF_ASSIGNMENT_ITEM_CODE,
    this.COLDEF_TEST_ATTEMPT_ID,
    this.COLDEF_FLAGS,
    this.COLDEF_COMMENTS,
    this.COLDEF_NUM_READS,
    this.COLDEF_IS_INSPECTED,
    this.COLDEF_IS_AVAILABLE_TO_SCORERS,
    this.COLDEF_IS_RESCORE_INDIC,
    this.COLDEF_IS_READ_RULES_PROCESSED,
    this.COLDEF_SCORED_ON,
  ]

  columnDefsS2sCIN = [
    this.COLDEF_BATCH_GROUP_ID,
    this.COLDEF_ASSIGNMENT_ITEM_CODE,
    this.COLDEF_TEST_ATTEMPT_ID,
    this.COLDEF_COMMENTS,
    this.COLDEF_NUM_READS,
    this.COLDEF_IS_INSPECTED,
    this.COLDEF_IS_AVAILABLE_TO_SCORERS,
    this.COLDEF_IS_RESCORE_INDIC,
    this.COLDEF_IS_READ_RULES_PROCESSED,
    this.COLDEF_SCORED_ON,
  ]

  columnDefsNYC = [
    coldef('test_attempt_id', this.lang.tra('lbl_sl_cols_ta_id'), { 
      checkboxSelection:true,
      headerCheckboxSelection:true,
      headerCheckboxSelectionFilteredOnly:true,
      width:130
    }),
    this.COLDEF_ASSIGNMENT_ITEM_CODE,
    coldef('student_uid', this.lang.tra('pc_lbl_student_uid')),
    coldef('student_name', this.lang.tra('lbl_student_name')),
    coldef('school_name', this.lang.tra('pc_lbl_schl_name')),
    coldef('class_name', this.lang.tra('class_section_name'))
  ]

  columnDefsNYCCAEC = [
    coldef('test_attempt_id', this.lang.tra('lbl_sl_cols_ta_id'), { 
      checkboxSelection:true,
      headerCheckboxSelection:true,
      headerCheckboxSelectionFilteredOnly:true,
      width:130
    }),
    this.COLDEF_ASSIGNMENT_ITEM_CODE,
    coldef('student_uid', this.lang.tra('pc_lbl_student_uid')),
    coldef('student_name', this.lang.tra('lbl_student_name')),
    coldef('institution_name', 'Institution Name'),
    coldef('twtar_name', 'Assignment Name'),
  ]

  isLoading:boolean;
  limit:number = 3000;
  endDate = new Date();
  isEndDateEnabled = false;
  async loadResponses() {
    this.clearData();
    this.isLoading = true;
   
    const viewId = this.selectedView

    this.batchGroups = [];
    this.s2s = [];
    this.childInNeed = [];

    const params = {
      query: {
        markingWindowId: this.markingWindowId,
        scorerMenuView: viewId,
        limit: this.getLimitParams(),
        isSupervisorView: this.isSupervisorRestrictedView ? 1 : 0,
        assignmentItemCode: this.assignmentItemCode
      }
    }
    if(viewId === ScorerMenuView.SCORINGS) {
      this.batchGroups = await this.auth
        .apiFind(this.routes.SCOR_SCOR_BATCH_GROUPS, params)
        .catch(e => {
          const {message, data} = e;
          if (e.message == 'DOMAIN_LOCKED'){
            this.loginGuard.quickPopup(DOMAIN_LOCK_MESSAGE + data.lockedDomain);
          }
          return
        })
      if (!this.batchGroups){ this.batchGroups= []; throw new Error()}
      this.batchGroups = this.batchGroups.map((record) => {
        return this.alignResponseRecord(record);
      })
      this.recalculateInspectedCounts();
    }  
    else if(viewId === ScorerMenuView.S2S) {
      this.s2s = await this.auth
        .apiFind(this.routes.SCOR_SCOR_BATCH_GROUPS, params)
        .catch(e => {
          const {message, data} = e;
          if (e.message == 'DOMAIN_LOCKED'){
            this.loginGuard.quickPopup(DOMAIN_LOCK_MESSAGE + data.lockedDomain);
          }
          return
        })
      if (!this.s2s){ this.s2s = [];throw new Error()}
      this.s2s = this.s2s.map((record) => {
        return this.alignS2SRecord(record);
      })
      this.s2sFiltered = this.s2s.filter((group) => !group.is_available_to_scorers);
    } 
    else if(viewId === ScorerMenuView.S2S_CIN) {
      this.childInNeed = await this.auth
        .apiFind(this.routes.SCOR_SCOR_BATCH_GROUPS, params)
        .catch(e => {
          const {message, data} = e;
          if (e.message == 'DOMAIN_LOCKED'){
            this.loginGuard.quickPopup(DOMAIN_LOCK_MESSAGE + data.lockedDomain);
          }
          return
        })
      if (!this.childInNeed){ this.childInNeed=[]; throw new Error()}
      
            this.childInNeed = this.childInNeed.map((record) => {
        return this.alignS2SRecord(record);
      })
      this.childInNeedFiltered = this.childInNeed.filter((group) => !group.is_inspected);
    } 
    else if(viewId === ScorerMenuView.NOT_YET_CLAIMED) {
      this.notYetClaimed = await this.auth
        .apiFind(this.routes.SCOR_SCOR_BATCH_GROUPS, params)
        .catch(e => {
          const {message, data} = e;
          if (e.message == 'DOMAIN_LOCKED'){
            this.loginGuard.quickPopup(DOMAIN_LOCK_MESSAGE + data.lockedDomain);
          }
          return
        })
      if (!this.notYetClaimed){ this.notYetClaimed = []; throw new Error()}
      const institionList = this.notYetClaimed.filter(response => response.institution_name);
      if(institionList && institionList.length > 0){
        this.selectedView = ScorerMenuView.NOT_YET_CLAIMED_CAEC;
      }
    }

    // Refresh the counts in all tabs
    this.loadTabCounts();
    this.filterOutUnf('loadResponses')
    // Don't think we need to clear Grid API, it is loaded every when swtiching view
    //this.clearCurrentGridApi(viewId); 
    this.isLoading = false;
  }

  hasFlagSlug(slug_code: string) {
    const filteredGroupIds = this.s2s.filter((row) => (<string>row.flags).includes(slug_code) && !row.is_available_to_scorers);
    return filteredGroupIds.length > 0;
  }


  async repoolFlagSlug(slug_code: string) {
    const filteredGroupIds = this.s2s.filter((row) => (<string>row.flags).includes(slug_code) && !row.is_available_to_scorers).map((row) => row.id);
    if(filteredGroupIds.length <= 0) {
      this.loginGuard.quickPopup(`No scores with ${slug_code} flag were found.`);
      return;
    }
    const TARGET = 'POOL';
    await this.repoolResponse(TARGET, filteredGroupIds);
  }

  alignCommonRecord(record) {
    record.is_rescore_indic = record.is_rescore_indic == 1 ? true : false
    record.is_read_rules_processed = record.is_read_rules_processed == 1 ? false : true
    record.is_available_to_scorers = record.is_available_to_scorers == 1 ? true : false
    record.includes_nr = record.includes_nr == 1 ? true : false;

    return record;
  }

  alignS2SRecord(record) {
    record = this.alignCommonRecord(record);
    record.is_inspected = record.is_inspected == 1 ? true : false
    try {
      let comments = JSON.parse(record.comments);
      comments = comments.map((comment) => {
        return comment.flagMessage;
      }).filter((comment) => comment.length >= 1);
      record.comments = comments.join(",");
    } catch {
      throw new Error('ERROR_PARSING_COMMENTS_JSON');
    }
    return record;
  }

  alignResponseRecord(record) {
    record = this.alignCommonRecord(record);
    record.is_invalid = record.is_invalid == 1 ? true : false;
    return record;
  }

  getActiveColumnDefs(view: string) {
    const columnDefMap = {
      [ScorerMenuView.S2S]:             this.isSupervisorRestrictedView ? (this.isScoringsInspectAccess ? this.columnDefsS2s__supervisor__inspect : this.columnDefsS2s__supervisor): this.columnDefsS2s,
      [ScorerMenuView.S2S_CIN]:         this.columnDefsS2sCIN,
      [ScorerMenuView.SCORINGS]:        this.isSupervisorRestrictedView ? (this.isScoringsInspectAccess ? this.columnDefsBatchGroup__supervisor__inspect : this.columnDefsBatchGroup__supervisor) : this.columnDefsBatchGroup,
      [ScorerMenuView.NOT_YET_CLAIMED]: this.columnDefsNYC,
      [ScorerMenuView.NOT_YET_CLAIMED_CAEC]: this.columnDefsNYCCAEC,
    };
  
    return columnDefMap[view] || this.columnDefsBatchGroup; // Use a default value if view is not found in the map
  }

  showInspectedResponses = true;
  showRepooledResponses = false;
  filteredByValidationTool = false;
  getActiveRecords(view: string) {
    const recordMap = {
      [ScorerMenuView.S2S]:             this.showRepooledResponses ? this.s2s : this.s2sFiltered,
      [ScorerMenuView.SCORINGS]:        this.filteredByValidationTool? this.batchGroupsFiltered : this.batchGroups,
      [ScorerMenuView.S2S_CIN]:        this.showInspectedResponses ? this.childInNeed : this.childInNeedFiltered,
      [ScorerMenuView.NOT_YET_CLAIMED]: this.notYetClaimed,
      [ScorerMenuView.NOT_YET_CLAIMED_CAEC]: this.notYetClaimed
    }

    return recordMap[view] || this.batchGroups;
  }

  flaggedRR: any[];
  isLoadingFlaggedRR:boolean = true;

  getExportFilename() {
    return `repsonses ` +  moment().format('YYYY-MM-DD[T]HH_mm_ss');
  }

  // TODO
  // - Show bottom flag options in the bottom
  // - Show chosen bottom flag above everything
  // - Uncomment function for clearing bottom flags
  gridApiS2S: GridApi;
  gridApiCIN: GridApi;
  gridApiSCORINGS: GridApi;
  gridApiNYC: GridApi;
  
  gridColumnApiS2S;
  gridColumnApiCIN;
  gridColumnApiSCORINGS;
  gridColumnApiNYC;

  async onGridReadyS2S(params) {
    //console.log("S2S GRID READY")
    this.gridApiS2S = params.api;
    this.gridColumnApiS2S = params.columnApi;
    params.columnApi.autoSizeColumns();
    // params.columnApi.autoSizeAllColumns();
  }

  async onGridReadyCIN(params) {
    //console.log("CIN GRID READY")
    this.gridApiCIN = params.api;
    this.gridColumnApiCIN = params.columnApi;
    params.columnApi.autoSizeColumns();
    // params.columnApi.autoSizeAllColumns();
  }

  async onGridReadySCORINGS(params) {
    //console.log("SCORINGS GRID READY")
    this.gridApiSCORINGS = params.api;
    this.gridColumnApiSCORINGS = params.columnApi;
    params.columnApi.autoSizeColumns();
    this.filterOutUnf('onGridReadySCORINGS') // I dont
    // params.columnApi.autoSizeAllColumns();
  }

  filterOutUnf(src?:string){
    setTimeout(() => {
      console.log('filterOutUnf::', src)
      const filterFlags = this.gridOptions.api.getFilterInstance('flags'); 
      if (filterFlags){
        filterFlags.setModel({
          filter: "UNF",
          filterType: "text",
          type: "notContains",
        });
        this.gridOptions.api.onFilterChanged();
      }
    }, 500)
  }

  async onGridReadyNYC(params) {
    this.gridApiNYC = params.api;
    this.gridColumnApiNYC = params.columnApi;
    params.columnApi.autoSizeColumns();
    // params.columnApi.autoSizeAllColumns();
  }

  /**
   * The first response batch is the primary key for each batch group. Thus, this function returns the
   * first response batch.
   * @returns the first response batch in multiScaleResponseBatchesMap
   */
  getCurrentResponseBatch() {
    const currentMwi = Array.from(this.multiScaleResponseBatchesMap.keys())[0];
    if(!currentMwi) {
      throw new Error('NO_RESPONSE_BATCHES');
    }
    return this.multiScaleResponseBatchesMap.get(currentMwi)!;
  }

  getCurrentResponseId() {
    if(this.isSingleResponseLoaded) {
      return this.getCurrentResponseBatch()?.myPresence.responseId;
    }
    let selectedNodes = this.getCurrentGridAPI().getSelectedNodes();
    let selectedData = selectedNodes.map(node => node.data);

    if(selectedData.length != 1) return null;

    return selectedData[0].taqr_id;
  }

  getCurrentResponse(isRestrictToSingle = true){
    let selectedNodes = this.getCurrentGridAPI()?.getSelectedNodes();
    let selectedData = selectedNodes?.map(node => node.data);
    if(selectedData?.length != 1) return null; 
    return selectedData[0];
  }
  getCurrentResponseScoreId() {
    const currentResponse = this.getCurrentResponse(true);
    return currentResponse.mrs_id;
  }

  getCurrentResponseScorer() {
    let selectedNodes = this.getCurrentGridAPI().getSelectedNodes();
    let selectedData = selectedNodes.map(node => node.data);

    if(selectedData.length != 1) return null;

    return {
      uid:selectedData[0].uid,
      first_name:selectedData[0].first_name,
      last_name:selectedData[0].last_name,
      contact_email:selectedData[0].contact_email,
    };
  }

  getCurrentWindowItemId() {
    let selectedNodes = this.getCurrentGridAPI().getSelectedNodes();
    let selectedData = selectedNodes.map(node => node.data);

    if(selectedData.length != 1) return null;
    return selectedData[0].item_id;
  }

  getCurrentTaqrId() {
    let selectedNodes = this.getCurrentGridAPI().getSelectedNodes();
    let selectedData = selectedNodes.map(node => node.data);

    if(selectedData.length != 1) return null;

    return selectedData[0].taqr_id;
  }

  /**
   * Used when the current responses window item ID is needed - which is stored as a key in multiScaleResponseBatchesMap.
   * @returns the first element in multiScaleResponseBatchesMap.keys()
   */
  getCurrentResponseItemId() {
    const FIRST_INDEX = 0;
    return Array.from(this.multiScaleResponseBatchesMap.keys())[FIRST_INDEX];
  }

  /**
  * Used when a specified response's window item ID is needed - which is stored as a key in multiScaleResponseBatchesMap.
  * @returns the first element in multiScaleResponseBatchesMap.keys()
  */
  getResponseItemId(multiScaleResponseBatchesMap: Map<number,ResponseBatch>) {
    const FIRST_INDEX = 0;
    return Array.from(multiScaleResponseBatchesMap.keys())[FIRST_INDEX];
  }

  /**
   * Used to get the student's response content from the current response batch.
   * @returns the student's response stored in responseContentStates
   */
  getCurrentResponseContent(){
    return this.getCurrentResponseBatch()?.currentResponsBatch.responseContentStates.get(this.getCurrentResponseId());
  }

  getCurrentResponseScorerUid() {
    return this.currentResponseScorerUid
  }

  isDirectEditingMode = false;

  getResponseScorerUid() {
    return 235151;
  }

  getCurrentResponses() {
    if(this.getCurrentResponseBatch() && this.getCurrentResponseBatch()?.currentResponsBatch?.responses) {
      return this.getCurrentResponseBatch()?.currentResponsBatch.responses;
    }
    return [];
  }

  getCurrentResponseMarkState(){
    return this.getCurrentMarkStates().get(this.getCurrentResponseId());
  }

  getCurrentMarkStates(){
    return this.getCurrentResponseBatch()?.currentResponsBatch.responseMarkStates;
  }

  /**
   * Update the current response ID to the previous index
   */
  navPrevResponse(): void {
    if(this.isPrevRespDisabled()) {
      return;
    }

    let currentResponseIndex = this.getCurrentResponseIndex();
    let newIndex = currentResponseIndex - 1;
    const responseId = this.getCurrentResponseBatch()?.currentResponsBatch.responses[newIndex];
    this.getCurrentResponseBatch().myPresence.responseId = responseId;
  }
  
  /**
   * Update the current response ID to the next index
   */
  navNextResponse(): void {
    if(this.isNextRespDisabled()) {
      return;
    }

    let currentResponseIndex = this.getCurrentResponseIndex();
    let newIndex = currentResponseIndex + 1;
    const responseId = this.getCurrentResponseBatch().currentResponsBatch.responses[newIndex];
    this.getCurrentResponseBatch().myPresence.responseId = responseId;
  }

  /**
   * Each response is stored in an array. When a user goes into the next or previous response, the current index increments or decrements by one respectively.
   * Finds the current response index based on the current response ID.
   * @returns The current response index of the marking state.
   */
  getCurrentResponseIndex() {
    let currentResponseIndex = 0;
    this.getCurrentResponses().forEach((responseId, responseIndex) => {
      if (responseId === this.getCurrentResponseId()){
        currentResponseIndex = responseIndex;
      }
    });
    return currentResponseIndex;
  }

  isPrevRespDisabled(){
    const responseIndex = this.getCurrentResponseIndex();
    return responseIndex <= 0;
  }

  isNextRespDisabled() {
    const responseIndex = this.getCurrentResponseIndex();
    return responseIndex >= this.getCurrentResponses().length - 1;
  }

  hasTopFlagOptions(groupId: number) {
    const scales = this.getScales(groupId);
    return this.scoreProfileGroups && this.multiScaleResponseBatchesMap.get(scales[0].id).scoreProfile.flagOptions && this.getTopFlagOptions(groupId).length > 0;
  }
  getTopFlagOptions(groupId: number) {
    const scales = this.getScales(groupId);
    const topFlags = this.multiScaleResponseBatchesMap.get(scales[0].id).scoreProfile.flagOptions;
    return topFlags.filter((flag) => flag.is_top_flag == 1)
  }
  getScalePrescoreFlags(mwiId: number) {
    const flags = this.multiScaleResponseBatchesMap.get(mwiId).scoreProfile.scoreOptions.filter((score) => score.is_pre_score_flag == 1);
    return flags;
  }

  getScoreGroupFlag(groupId: number) {
    const scales = this.getScales(groupId);
    const flagId = this.getFlagId(groupId);
    const flag = this.getFlag(scales[0].id, flagId);
    return flag;
  }

  hasTopFlag() {
    const groups = this.getScoreProfileGroups();
    let requireConfirmation = false;
    groups.map((group) => {
      const flag = this.getScoreGroupFlag(group.id)
      if(flag) {
        requireConfirmation = true;
      }
    });

    return requireConfirmation;
  }

  async confirmNrIns() {
    const groupFlagMap = new Map<number, IScoringFlagOption>();
    const batchResponseIndex = this.getCurrentResponseIndex();

    this.getScoreProfileGroups().forEach((group) => {
      const flag = this.getScoreGroupFlag(group.id);
      groupFlagMap.set(group.id, flag);
    })
    
    this.markInspected(true, true);

    await Promise.all(Array.from(groupFlagMap.entries()).map(async ([groupId, flag]) => {
      if(!flag) {
        return;
      }
      
      const scales = this.getScales(groupId)
      this.clearFlag([scales[0].id], batchResponseIndex);
      const scoreGroupScales = Array.from(this.scoreProfileGroups.get(groupId).scales.values());

      await Promise.all(scoreGroupScales.map(async (scale) => {
        const mwiId = scale.id
        const prescoreFlags = this.getScalePrescoreFlags(mwiId);
        const isNonScoreProfile = this.multiScaleResponseBatchesMap.get(mwiId).isNonScoredProfile;
        const currentGroupSlugCode = flag.slug_code;
        const scaleFlag = prescoreFlags.find((flag) => flag.slug_code == currentGroupSlugCode);
        
        if(!scaleFlag && !isNonScoreProfile) {
          throw new Error('NO_PRESCORE_FLAG_FOUND');
        }
        if(!isNonScoreProfile){
          await this.forceAssignScore(scaleFlag.id, scale);
        }
      }));
    }));

    this.duplicateInvalidateRead();
  }

  isFlagOptionSelected(flagId:number, groupId?: number){
    if(groupId) {
      return this.getFlagId(groupId) == flagId;
    }
    return this.getMyCurrentFlagOption() === flagId;
  }

  isResponseFlagged() {
    let isFlagged = false;
    const currentBottomFlag = this.getMyCurrentFlagOption();
    if(currentBottomFlag) {
      return true;
    }

    this.scoreProfileGroups.forEach((group) => {
      if(this.getFlagId(group.id) != null) {
        isFlagged = true;
      }
    })

    return isFlagged;
  }

  getFlagId(groupId: number){
    // Top flags should include the group ID.
    const scales = this.getScales(groupId)
    const batchResponseIndex = this.getCurrentResponseIndex();
    const responseId = this.multiScaleResponseBatchesMap.get(scales[0].id).currentResponsBatch.responses[batchResponseIndex];
    const response = this.multiScaleResponseBatchesMap?.get(scales[0].id)?.currentResponsBatch.responseMarkStates?.get(responseId);
    if(response && response.myAssignedMark?.flagId) {
      const currFlagId = response.myAssignedMark.flagId
      return currFlagId;
    }
    return null;
  }

  getMyCurrentFlagOption(){
    const {myAssignedMark} = this.getCurrentResponseMarkState() || {};
    if (myAssignedMark){
      return myAssignedMark.flagId
    }
  }

  getCurrentFlagMessage(scoreProfileGroup: IScoreProfileGroup){
    try {
      const {myAssignedMark} = this.getCurrentResponseMarkState() || {};
      if (myAssignedMark && myAssignedMark.meta && scoreProfileGroup.id == myAssignedMark.score_profile_group_id){
        const meta = JSON.parse(myAssignedMark.meta);
        return meta.flagMessage
      }
    }
    catch(e){}
    return;
  }

  getCurrentBottomFlag() {
    const currentFlag = this.getMyCurrentFlagOption();
    if(!currentFlag) {
      return null;
    }
    const bottomFlag = this.flagOptions.find((flag) => flag.is_top_flag == 0 && flag.id == currentFlag)
    return bottomFlag
  }

  selectFlag(flagId:number){
    this.activeFlagId = flagId;
    this.flagMessage = '';
  }
  deselectFlag(){
    this.activeFlagId = null;
    this.flagMessage = '';
  }

  getCurrentResponseFlag() {
    let selectedNodes = this.getCurrentGridAPI().getSelectedNodes();
    let selectedData = selectedNodes.map(node => node.data);

    if(selectedData.length != 1) return null;

    return selectedData[0].flag;
  }

  getCurrentResponseMessage() {
    let selectedNodes = this.getCurrentGridAPI().getSelectedNodes();
    let selectedData = selectedNodes.map(node => node.data);

    if(selectedData.length != 1) return null;

    return selectedData[0].flagMessage;
  }

  getIsCurrentResponseInspected() {
    return this.currResp().inspected;
  }

  currResp(){
    let selectedNodes = this.getCurrentGridAPI().getSelectedNodes();
    let selectedData = selectedNodes.map(node => node.data);
    if(selectedData.length != 1){
      return {}
    };
    return selectedData[0]
  }

    // Set default language by app langservice language, e.g. for text like "No rows to show"
  gridOptions:any = {
    localeText:  this.lang.c() == 'fr' ? AG_GRID_LOCALE_FR : AG_GRID_LOCALE_EN
  };
  exportCSV(){
    this.gridOptions.api.exportDataAsCsv();
  }

  async assignFlag(flagId:number, groupId?: number){
    const batchResponseIndex = this.getCurrentResponseIndex();
    const firstScales = [];
    this.scoreProfileGroups.forEach((group) => {
      const scales = this.getScales(group.id)
      firstScales.push(scales[0].id);
    })

    if(!groupId) {
      // Set bottom flag
      if (this.isFlagOptionSelected(flagId)){
        await this.clearFlag();
      }
      else{
        const meta = JSON.stringify({flagMessage: this.flagMessage})
        this.updateScoreOptions({flagId, meta},batchResponseIndex, firstScales);
        this.activeFlagId = flagId;
      }

      return;
    }

    // Top flag
    if (this.isFlagOptionSelected(flagId, groupId)){
      const scales = this.getScales(groupId)
      await this.clearFlag([scales[0].id], batchResponseIndex);
      return;
    }
    // If bottom flag exists, clear all assignments
    if(this.getCurrentBottomFlag()) {
      const withoutConfirmation = true;
      this.clearBottomFlags(withoutConfirmation);
    }

    const scales = this.getScales(groupId)
    const meta = JSON.stringify({flagMessage: this.flagMessage || ''})
    this.updateScoreOptions({flagId, meta},batchResponseIndex, [scales[0].id]);

    this.activeFlagId = flagId;
  }
  async clearBottomFlags(withoutConfirmation: boolean = false){
    if (withoutConfirmation || confirm('Are you sure you want to clear the bottom flag?')){
      const batchResponseIndex = this.getCurrentResponseIndex();
      const firstScales = [];
      this.scoreProfileGroups.forEach((group) => {
        const scales = this.getScales(group.id)
        firstScales.push(scales[0].id);
      })
      if(this.getCurrentBottomFlag()) {
        const is_bottom_flag = true;
        this.clearFlag([firstScales[0]], batchResponseIndex, is_bottom_flag)
      }
    }
  }
  async clearFlag(windowItemIds?: number[], batchResponseIndex?: number, is_bottom_flag?: boolean){
    if(windowItemIds && batchResponseIndex != undefined) {
      windowItemIds.forEach(async (item_id) => {
        if(this.multiScaleResponseBatchesMap.get(item_id)){
          const responseId = this.multiScaleResponseBatchesMap.get(item_id).currentResponsBatch.responses[batchResponseIndex];
        this.auth.apiRemove(this.routes.SCOR_SCOR_MARK, responseId, {query: {
          is_flag: true,
          scorer_uid: this.currentHistoryScorerUid,
          isLeader: 1,
          is_bottom_flag: is_bottom_flag
        }});
        const markState = this.multiScaleResponseBatchesMap.get(item_id).currentResponsBatch.responseMarkStates.get(responseId);
        if (markState){
          markState.myAssignedMark.flagId = null;
        }
      }})
    } else {
      const responseId = this.getCurrentResponseId();
      await this.auth.apiRemove(this.routes.SCOR_SCOR_MARK, responseId, {query:{
        is_flag: true,
        scorer_uid: this.currentHistoryScorerUid,
        isLeader: 1,
        is_bottom_flag: is_bottom_flag
      }});
      const markState = this.getCurrentResponseMarkState();
      if (markState){
        markState.myAssignedMark.flagId = null;
      }
    }
  }

  async clearScore(batchResponseIndex:number, windowItemId:number){
    if (this.multiScaleResponseBatchesMap.size > 1) {
      const targetResponseId = this.multiScaleResponseBatchesMap.get(windowItemId).currentResponsBatch.responses[batchResponseIndex];
      const markState = this.multiScaleResponseBatchesMap.get(windowItemId).currentResponsBatch.responseMarkStates.get(targetResponseId).myAssignedMark.scoreOptionId = null;
      if (markState){
        markState.myAssignedMark.scoreOptionId = null;
      }
      await this.auth.apiRemove(this.routes.SCOR_SCOR_MARK, targetResponseId, {query:{
        is_score_option: true,
        scorer_uid: this.currentHistoryScorerUid,
        isLeader: 1,
      }});
    } else {
      const responseId = this.getCurrentResponseId();
      const markState = this.getCurrentResponseMarkState();
      if (markState){
        markState.myAssignedMark.scoreOptionId = null;
      }
      await this.auth.apiRemove(this.routes.SCOR_SCOR_MARK, responseId, {query:{
        is_score_option: true,
        isLeader: 1,
      }});
    }
  }

  /**
   * Assigns a score to the scale based on the window item ID and the score option.
   * @param scoreOptionId the new score option ID
   * @param item the current question scale
   */
  assignScore(scoreOptionId:number, item?:IScale): void{
    const batchResponseIndex = this.getCurrentResponseIndex();
    if(!item) {
      return;
    }
    
    const windowItemId = item.id
    if(!this.isScoreOptionSelected(scoreOptionId, item)) {
      this.updateScoreOptions({scoreOptionId}, batchResponseIndex, [windowItemId]);
      return;
    }

    this.clearScore(batchResponseIndex, windowItemId);
  }

  async forceAssignScore(scoreOptionId:number, item?:IScale){
    const batchResponseIndex = this.getCurrentResponseIndex();
    if(!item) {
      return;
    }
    
    const windowItemId = item.id
    await this.updateScoreOptions({scoreOptionId}, batchResponseIndex, [windowItemId]);
  }

  /**
   * Update the score for multiple specified question scales, or all.
   * @param newMark the new mark to assign
   * @param batchResponseIndex the current response index
   * @param windowItemIds an array of window item IDs to apply the new score to
   */
  async updateScoreOptions(newMark:Partial<IResponseMark>, batchResponseIndex?:number, windowItemIds?:number[]): Promise<void> {
    if(!windowItemIds) {
      this.updateScoreOption(newMark, this.getCurrentResponseId());
      return;
    }

    await Promise.all(windowItemIds.map(async (item_id) => {
      const responseId = this.multiScaleResponseBatchesMap.get(item_id).currentResponsBatch.responses[batchResponseIndex];
      await this.updateScoreOption(newMark, responseId, item_id)
    }));
  }

  /**
   * Update the score for a specified response ID
   * @param newMark the new mark to assign
   * @param responseId the response ID to update
   * @param window_item_id the question scale the response belongs to
   */
  async updateScoreOption(newMark:Partial<IResponseMark>, responseId:number, window_item_id?:number){ 
    const payload = {
      score_option_id: newMark.scoreOptionId || undefined,
      score_flag_id: newMark.flagId || undefined,
      meta: newMark.meta || undefined,
    }

    await this.auth
    .apiPatch(
      this.routes.SCOR_SCOR_MARK, 
      responseId, 
      payload,
      {query: {
        task_id: this.getCurrentResponseBatch()?.currentResponsBatch.task_id,
        isLeader: 1,
      }}
    )
    .catch(e => {
      // if (e.message !== 'NO_CLAIMED_BATCHES_AVAIL'){ this.loginGuard.quickPopup(e.message) }
      console.log('scoring blocked', e)
      throw new Error(e.message)
    })

    let markState;
    if(!window_item_id) {
      markState = this.getCurrentResponseMarkState();
    }
    if(window_item_id) {
      markState = this.multiScaleResponseBatchesMap.get(window_item_id).currentResponsBatch.responseMarkStates.get(responseId);
    }

    if(!markState) {
      const newMarkState:IResponseMarkState = {
        responseId,
        myAssignedMark: newMark,
        tags:[],
      }
      if(window_item_id) {
        this.multiScaleResponseBatchesMap.get(window_item_id).currentResponsBatch.responseMarkStates.set(responseId, newMarkState);
      }

      return;
    }

    if (newMark.scoreOptionId){
      markState.myAssignedMark.scoreOptionId = newMark.scoreOptionId
    }
    if (newMark.flagId){
      markState.myAssignedMark.flagId = newMark.flagId
    }
    if (newMark.meta){
      markState.myAssignedMark.meta = newMark.meta
    }
  }

  async markInspected(status: boolean, isHelper = false) {
    const inspected = status ? 1 : 0;
    const params = {query: {
      marker_read_id: this.currentMarkerReadId
    }}
    let data: any;

    data = {
      inspected
    }

    this.auth.apiPatch(this.routes.SCOR_SCOR_SCORING_READS, this.currentGroupId, data, params).then(() => {
    }).catch((err) => {
      throw err;
    });

    if(!isHelper) {
      this.selectHistory(this.currentMarkerReadId);
    }
  }

  async invalidateRead(status: boolean, readId: number) {
    console.log('INVALIDATING ', readId);
    const invalid = status ? 1 : 0;
    const params = {query: {
      marker_read_id: readId
    }}
    this.auth.apiPatch(this.routes.SCOR_SCOR_SCORING_READS, this.currentGroupId, {
      is_invalid: invalid
    }, params).then(() => {
      const currentHistory = this.scorerHistory?.get(readId);
      currentHistory!.invalid = status;

      if(status && this.scorerValidHistory?.has(readId)) {
        this.scorerValidHistory.delete(readId);
      } else if(!status) {
        this.scorerValidHistory?.set(readId, currentHistory!);
      }
    }).catch((err) => {
      throw err;
    });
  }


  getMaxRead(){

  }
  isMaxRead(){

  }
  isConfirmableFlag(){

  }

  confirmFlag(){

  }

  repoolLoading: boolean = false;
  sendToTarget:string = null;
  markerUid: number = null;
  supervisorNote: string = null;
  async sendBackToPool(isStopInvalidation: boolean){
    if(!this.sendToTarget) {
      return;
    }
    const rows = this.getCurrentGridAPI().getSelectedRows();
    const batchGroupIds = rows.map((row)=>+row.id);

    this.repoolLoading = true;
    await this.repoolResponse(this.sendToTarget, batchGroupIds, this.markerUid, this.supervisorNote, isStopInvalidation).finally(
      () => {
        this.supervisorNote = null;
      }
    );
  }

  async repoolResponse(sendToTarget: string, batchGroupIds: number[], markerUid?: number, supervisorNote?: string, isStopInvalidation?: boolean) {
    this.repoolLoading = true;
    return await this.auth.apiCreate(this.routes.SCOR_LEAD_RESPONSE_REPOOLING, {
      sendToTarget: sendToTarget,
      batchGroupIds,
      markerUid: markerUid,
      supervisorNote: supervisorNote,
      isStopInvalidation
    }).then(() => {
      let message = ''
      if(sendToTarget == RepoolTypes.POOL) {
        message = 'repooled';
      } else if(sendToTarget == RepoolTypes.MARKER) {
        const scorer = this.availableScorers.find((scorer) => scorer.uid == markerUid);
        message = `sent back to ${scorer.email}`;
      }

      this.loginGuard.quickPopup(`${batchGroupIds.length} scores have been ${message}`);
      this.getCurrentGridAPI().deselectAll();
      const keepMarkerUid = true;
      this.clearData(keepMarkerUid);
      this.loadResponses();
    }).catch((err) => {
      this.loginGuard.quickPopup(`An error has occurred repooling the response`);
    }).finally(() => {
      this.repoolLoading = false;
    })
  }

  isReadInspected() {
    const currentRead = this.scorerHistory.get(this.currentMarkerReadId);
    if(!currentRead) {
      return false;
    }
    return currentRead.inspected;
  }

  currReadInspectedMarkerNumber() {
    const currentRead = this.scorerHistory.get(this.currentMarkerReadId);
    if(!currentRead) {
      return false;
    }
    return currentRead.inspected_by_swum_marker_number;
  }

  isCurrentReadExpert(){
    const currentRead = this.scorerHistory.get(this.currentMarkerReadId);
    if(!currentRead) {
      return false;
    }
    return currentRead.is_expert_score;
  }


  async markAsSensitive() {
    if (!confirm('Are you sure you want to mark this item as sensitive?')){
      return
    }
    const flagMessage = prompt('Please include a brief note as to why this response is being flagged as a sensitive response.') 
    if (flagMessage){
      const {mrs_id, taqr_id, window_item_id} = this.getCurrentResponse(true);
      await this.auth.apiCreate('public/scor-lead/responses/sensitive', {
        taqr_id,
        window_item_id,
        mrs_id,
        flagMessage: `[Assigned as Sensitive by ${this.auth.getDisplayName()} (${this.auth.getUid()})]\n` + flagMessage,
      });
      alert('The response has been marked as a sensitive response')
    }
  }

  async markAsSII() {
    if (!confirm('Are you sure you want to mark this item as scan issue?')){
      return
    }
    const flagMessage = prompt('Please include a brief note as to why this response is being flagged as a scan issue response.') 
    if (flagMessage){
      const {mrs_id, taqr_id, window_item_id} = this.getCurrentResponse(true);
      await this.auth.apiCreate('public/scor-lead/responses/sensitive', {
        taqr_id,
        window_item_id,
        mrs_id,
        flagMessage: `[Assigned as Scan Issue by ${this.auth.getDisplayName()} (${this.auth.getUid()})]\n` + flagMessage,
        targetFlag: 'lbl_scan_issue_abr'
      });
      alert('The response has been marked as a scan issue')
    }
  }

  isBatchGroupSelected = false;
  /**
   * Called when a batch group is selected. Initializes group information, and loads responses.
   * @param event an ag-grid row click event
   */
  async onBatchGroupSelect(event): Promise<void>{
    const keepMarkerUid = true
    this.clearData(keepMarkerUid);
    const rows: GroupBatch[] = this.getCurrentGridAPI().getSelectedRows();

    this.isBatchGroupSelected = true; 

    if(rows.length <= 0) {
      this.isBatchGroupSelected = false; 
      return;
    }

    if(rows.length==1) {
      const data = rows[0]
      this.currentGroupId = data.id;
      this.currentResponseScorerUid = data.created_by_uid;
      await this.loadResponseBatch(this.currentGroupId, this.currentResponseScorerUid)
      this.isSingleResponseLoaded = true;
      return;
    }
  }

  isNYCSelected = false;
  mwiIdsCache: number[] = [];
  /**
   * Handles row selection for Not Yet Claimed. Initializes availableScorers if it is empty.
   * @param event 
   * @returns 
   */
  async onNYCSelect(event) {
    const rows = this.getCurrentGridAPI().getSelectedRows();
    
    this.isNYCSelected = true;    
    
    if(rows.length <= 0) {
      const keepMarkerUid = true
      this.clearData(keepMarkerUid);
      return;
    }
    const mwiIds: number[] = rows.map((row) => row.group_to_mwi_id)
    const distinctMwiIds: number[] = [...new Set(mwiIds)].sort();

    if(!this.isSameMwiList(distinctMwiIds, this.mwiIdsCache)){
      this.mwiIdsCache = distinctMwiIds;
      const emptyRow = {
        marker_id: null,
        uid: null,
        email: null,
        group_to_mwi_id: null,
        slug: null
      }
      
      this.availableScorers = [];
      if(distinctMwiIds.length > 0) {
        this.availableScorers.push(emptyRow);
        const availableScorers = await this.auth.apiCreate(this.routes.SCOR_LEAD_GET_SCORER_ACCESS, { mwiIds: distinctMwiIds});
        if(availableScorers && availableScorers.length) {
          this.availableScorers.push(...availableScorers);
        }
      }
    }
  }

  isSameMwiList(mwi_ids: number[], mwi_ids_2: number[]){
    if(mwi_ids.length !== mwi_ids_2.length){
      return false;
    }
    for(const mwi_id of mwi_ids){
      if(!mwi_ids_2.includes(mwi_id)){
        return false;
      }
    }
    return true;
  }

  /**
   * Assigns a response in Not Yet Claimed to a marker
   */
  async assignNYCToMarker() {
    const attempts = this.getCurrentGridAPI().getSelectedRows();
    if(attempts.length <= 0) {
      throw new Error('NO_ROWS_SELECTED')
    }
    const data = {attempts, uid: this.markerUid}
    this.repoolLoading = true;
    await this.auth.apiCreate(this.routes.SCOR_LEAD_FORCE_MARKER_CLAIM, data).then(() => {
      const scorer = this.availableScorers.find((scorer) => scorer.uid == this.markerUid);
      this.loginGuard.quickPopup(`${attempts.length} responses in ${attempts[0].assignment_item_code} have been assigned to ${scorer.email}`);
      this.getCurrentGridAPI().deselectAll();
      const keepMarkerUid = true
      this.clearData(keepMarkerUid);
    });
    this.repoolLoading = false;
  }

  responseScoreIds: Map<number,number[]>;
  availableScorers: {marker_id: number, uid: number, email: string}[] = [];
  previousAssessmentSelection: string;
  batchBeforeRescore: boolean = false;
  hasCurrentResponseScan: boolean = false;
  /**
   * Makes an API call to SCOR_SCOR_BATCH_GROUPS and loads the required response batch information.
   * @param groupId the scoring batch group ID to load
   * @param uid the uid to filter through responses. Optional.
   */
  async loadResponseBatch(groupId: number = 0, uid: number, markerReadId?: number) {
    this.isHistoryLoading = true;
    const params = {
      query: {
        markerReadId,
        markingWindowId: this.markingWindowId
      }
    }
    const batchConfigs = await this.auth.apiGet(this.routes.SCOR_SCOR_BATCH_GROUPS, groupId, params)
    const responseBatchesMap = this.initResponseBatch(batchConfigs);
    this.multiScaleResponseBatchesMap = responseBatchesMap;
    this.scoreProfileGroups = this.initScoreProfileGroup(batchConfigs);
    const {historyMarkMap, historyValidMarkMap} = this.initScorerHistory(batchConfigs);
    this.scorerHistory = historyMarkMap;
    this.scorerValidHistory = historyValidMarkMap;
    this.flagOptions = this.initFlagOptions(batchConfigs);
    this.responseScoreIds = this.initResponseScoreIds(batchConfigs);
    const currentBatchAssessment = await this.updatePrevAssessment(batchConfigs);
    if(this.previousAssessmentSelection !== currentBatchAssessment || this.availableScorers.length <= 0){
      this.markerUid = null
      this.previousAssessmentSelection = currentBatchAssessment;
      this.availableScorers = await this.initAvailableScorers(batchConfigs);
    }
    this.currentHistoryScorerUid = uid;
    this.currentMarkerReadId = markerReadId || Array.from(this.scorerHistory.keys()).pop()!;
    const history = this.scorerHistory.get(this.currentMarkerReadId);
    this.batchBeforeRescore = history.is_before_rescore;
    this.hasCurrentResponseScan = batchConfigs.some(c => c.hasResponseScan);
    // console.log(this.currentMarkerReadId, 'currentMarkerReadId');
    this.isHistoryLoading = false;
    // console.log(this.multiScaleResponseBatchesMap, 'multiScaleResponseBatchesMap')
    // console.log(this.responseScoreIds, 'responseScoreIds')
  }

  getScorerHistory() {
    if(this.showInvalidReads) {
      return this.scorerHistory
    }

    return this.scorerValidHistory;
  }
  showInvalidReads = false;
  hasValidRead() {
    if(this.showInvalidReads) {
      return true;
    }
    if(!this.scorerHistory) {
      return false;
    }

    const validReads = Array.from(this.scorerHistory.values()).filter((read) => read.invalid == false);
    return validReads && validReads.length > 0;
  }

  initResponseBatch(batchConfigs: any[]): Map<number, ResponseBatch> {
    const responseBatchesMap: Map<number, ResponseBatch> = new Map();
    batchConfigs.forEach((batch) => {
      const itemResponseBatch = this.responseBatchService.initNewResponseBatch(batch)
      responseBatchesMap.set(batch.windowItemId, itemResponseBatch)
    })

    return responseBatchesMap;
  }

  clearData(keepMarkerUid : boolean = false) {
    this.scoreProfileGroups = new Map();
    this.multiScaleResponseBatchesMap = new Map();
    this.responseScoreIds = new Map();
    this.scorerHistory = null;
    this.currentHistoryScorerUid = null;
    this.currentMarkerReadId = null;
    this.isSingleResponseLoaded = false;
    this.isHistoryShowing = true;
    this.flagOptions = [];
    this.isBatchGroupSelected = false;
    this.isNYCSelected = false;
    if(!keepMarkerUid){
      this.markerUid = null;
    }
  }

  getReadRuleCategories(){
    return TEMP_READ_RULES
  }
  onSelectedFromReadRuleChange(scale:IResponseBatch){
    //console.log(scale._new_from_read_rule)
  }
  confirmFromReadRuleChange(scale:IResponseBatch){
    if (scale && this.multiScaleResponseBatchesMap.size >= 1) {
      const itemResponseBatch = this.multiScaleResponseBatchesMap.get(scale.id);
      let new_from_read_rule = scale._new_from_read_rule;
      if(!new_from_read_rule){
        new_from_read_rule = ""
      }
      const mcbrId = itemResponseBatch.currentResponsBatch.responses[0]
      if(!mcbrId){
        return
      }
      this.auth.apiPatch(this.routes.SCOR_SCOR_MARKING_CLAIMED_BATCH_RESPONSES, mcbrId, 
        {
          from_read_rule: new_from_read_rule
        }
      ).then(
        (r) => {this.loginGuard.quickPopup(`Edit read type to ${new_from_read_rule} successfully.`)}
      )
      .catch((e) => {
        this.loginGuard.quickPopup("Failed to edit read type.");
      })
    }
  }
  checkScaleSuppression(scale:IResponseBatch){

    if (scale && this.multiScaleResponseBatchesMap.size >= 1) {
      const itemResponseBatch = this.multiScaleResponseBatchesMap.get(scale.id);
      return itemResponseBatch.currentResponsBatch.isSuppressed[0]
    }
  }

  toggleScaleSuppression(scale:IResponseBatch){
    if (scale && this.multiScaleResponseBatchesMap.size >= 1) {
      const itemResponseBatch = this.multiScaleResponseBatchesMap.get(scale.id);
      const toggledSuppressionValue = itemResponseBatch.currentResponsBatch.isSuppressed[0] == 0? 1 : 0;
      const mcbrId = itemResponseBatch.currentResponsBatch.responses[0]
      if(!mcbrId){
        return
      }
      this.auth.apiPatch(this.routes.SCOR_SCOR_MARKING_CLAIMED_BATCH_RESPONSES, mcbrId, 
        {
          is_scale_supressed: toggledSuppressionValue
        }
      ).then(
        (r) => {itemResponseBatch.currentResponsBatch.isSuppressed[0] = toggledSuppressionValue}
      )
      .catch((e) => {

      })
    }
  }
  
  getMcbrId(scale:IResponseBatch){
    if (scale && this.multiScaleResponseBatchesMap.size >= 1) {
      const itemResponseBatch = this.multiScaleResponseBatchesMap.get(scale.id);
      return itemResponseBatch.currentResponsBatch.responses[0]
    }
  }

  getReadType(scale:IResponseBatch){
    if (scale && this.multiScaleResponseBatchesMap.size >= 1) {
      const itemResponseBatch = this.multiScaleResponseBatchesMap.get(scale.id);
      if(!itemResponseBatch.currentResponsBatch.readRule[0]){
        return "None"
      }
      return itemResponseBatch.currentResponsBatch.readRule[0]
    }
  }

  isNYCSelectedValid() {
    const attempts = this.getCurrentGridAPI().getSelectedRows();
    if (attempts.length === 0) {
      // If the array is empty, return false
      return false;
    }
  
    // Extract the slug from the first object in the array
    const group_to_mwi_id = attempts[0].group_to_mwi_id;
  
    // Check if all objects in the array have the same slug
    return attempts.every(obj => obj.group_to_mwi_id === group_to_mwi_id);
  }

  initAvailableScorers(batchConfigs: any[]) {
    if(!batchConfigs[0]) {
      throw new Error('NO_BATCHES_FOUND');
    }
    return batchConfigs[0].availableMarkers;
  }
  
  updatePrevAssessment(batchConfigs: any[]){
    if(!batchConfigs[0]) {
      throw new Error('NO_BATCHES_FOUND');
      this.clearData();
    }
    return batchConfigs[0].item_slug;
  }

  initResponseScoreIds(batchConfigs: any[]): Map<number,number[]> {
    const responseScoreIds = new Map<number,number[]>();
    batchConfigs.forEach((batch) => {
      if(!batch.scores || !Array.isArray(batch.scores) || batch.scores.length <= 0) {
        return;
      }

      batch.scores?.forEach((score) => {
        if(!responseScoreIds.has(score.uid)) {
          responseScoreIds.set(score.uid, []);
        }

        const ids = responseScoreIds.get(score.uid);
        ids.push(score.id);
      })
    })
    return responseScoreIds
  }
  /**
   * Initializes the scorer history and storing it in scorerHistory
   * @param batchConfigs an array of batch information from SCOR_SCOR_BATCH_GROUPS
   */
  initScorerHistory(batchConfigs: any[]): {historyMarkMap: Map<number, IRead>, historyValidMarkMap:Map<number, IRead>} {
    const history: any[] = [];
    batchConfigs.forEach((batch) => {
      history.push(...batch.scorerHistory);
    });
    console.log(history, 'history');
    return this.getLatestHistory(history)
  }

  /**
   * Takes in an array of batches, and returns the latest scorer history information in map form.
   * @param batchHistory an array of batches
   * @returns a map of the distinct UIDs with the latest marked dates.
   */
  getLatestHistory(batchHistory: IBatchHistory[]): {historyMarkMap: Map<number, IRead>, historyValidMarkMap:Map<number, IRead>} {
    const historyMarkMap =  new Map<number, IRead>();
    const historyValidMarkMap =  new Map<number, IRead>();
    for (const batchMark of batchHistory) {
      const { marker_read_id, read_num, uid, created_on, inspected, swum_marker_number, marking_group_number_when_scored, marking_group_number_when_scored_revoked_on, inspected_by_swum_marker_number, is_invalid, is_sent_back, is_marked, is_rescore, is_before_rescore, is_valid_before_rescore, is_expert_score} = batchMark;
      const read: IRead = {
          read_num
        , uid
        , inspected: inspected == 0 ? false : true
        , invalid: is_invalid ? true : false
        , date: created_on
        , swum_marker_number
        , marking_group_number_when_scored
        , marking_group_number_when_scored_revoked_on
        , inspected_by_swum_marker_number
        , is_sent_back: is_sent_back == 0? false: true
        , is_rescore: is_rescore == 0? false: true
        , is_valid_before_rescore: is_valid_before_rescore == 0? false: true
        , is_before_rescore: is_before_rescore == 0? false: true
        , is_marked
        , is_expert_score
      };
      if (!historyMarkMap.has(marker_read_id)) {
        historyMarkMap.set(marker_read_id, read);
      }
      if (!historyValidMarkMap.has(marker_read_id) && !is_invalid) {
        historyValidMarkMap.set(marker_read_id, read);
      }
    }

    console.log(historyMarkMap, 'historyMarkMap');
    return {historyMarkMap, historyValidMarkMap};
  }

  isCurrentReadInvalid() {
    if(this.scorerHistory?.get(this.currentMarkerReadId)?.invalid) {
      return true;
    }

    return false;
  }

  hasScorerHistory() {
    return this.scorerHistory.size > 0;
  }
  
  async selectHistory(marker_read_id: number) {
    const history = this.scorerHistory.get(marker_read_id);
    const readUid = history?.uid || this.currentUid;
    await this.loadResponseBatch(this.currentGroupId, readUid, marker_read_id)
  }

  isCreatingRead: boolean = false;
  async createRead() {
    this.isCreatingRead = true;
    let isError = false;
    const claimedBatchgroupId = this.currentGroupId;

    const newMCBRs = await this.auth.apiCreate(this.routes.SCOR_SCOR_SCORING_READS, {claimedBatchgroupId, mcbrInfo: {is_marked: 1}}).catch((err) => {
      isError = true;
      this.isCreatingRead = false;
      throw err;
    });

    if(isError) {
      return;
    }
    this.isCreatingRead = false;
    const readId = newMCBRs[0].claimedBatchResponse.marker_read_id

    await this.selectHistory(readId);
  }

  async duplicateInvalidateRead() {
    let isError = false;
    const batch_group_id = this.currentGroupId;
    const marker_read_id = this.currentMarkerReadId;

    const newMCBRs = await this.auth.apiCreate(this.routes.SCOR_LEAD_DUPLICATE_READ, {batch_group_id, marker_read_id}).catch((err) => {
      isError = true;
      throw err;
    });

    if(isError) {
      return;
    }

    const readId = newMCBRs[0].claimedBatchResponse.marker_read_id

    await this.selectHistory(readId);
  }

  clearMarks() {
    this.multiScaleResponseBatchesMap.forEach((scale) => {
      scale.currentResponsBatch.responseMarkStates.clear();
    })
  }

  getScore(windowItemId: number, scoreOptionId: number) {
    return this.multiScaleResponseBatchesMap?.get(windowItemId)?.scoreProfile?.scoreOptions?.find((score) => {
      return score.id == scoreOptionId;
    });
  }

  getFlag(windowItemId: number, flagId: number) {
    return this.multiScaleResponseBatchesMap?.get(windowItemId)?.scoreProfile?.flagOptions?.find((flag) => {
      return flag.id == flagId;
    }) || null;
  }

  initFlagOptions(batchConfigs: any[]) {
    const flagOptions: IScoringFlagOption[] = [];
    batchConfigs.forEach((batch) => {
      flagOptions.push(...batch.flagOptions)
    })

    return flagOptions.filter((flag) => flag.is_top_flag == 0);
  }

  questionScales: any[];
  initScoreProfileGroup(batchConfigs: any[]) {
    const scoreProfileGroups = new Map<number, IScoreProfileGroup>()
    const questionScales: any[] = [];
    batchConfigs.forEach((batch) => {
      const currentBatch = batch.currentBatch;
      // Create new group if new score profile ID found
      if(!scoreProfileGroups.has(currentBatch.score_profile_group_id)) {
        const name = currentBatch.group_name;
        const scales = new  Map<number, IScale>();
        scoreProfileGroups.set(currentBatch.score_profile_group_id, {id: currentBatch.score_profile_group_id, name, scales});
      }

      // Set the score profile scale depending on window item ID
      const scales = scoreProfileGroups?.get(currentBatch.score_profile_group_id)?.scales;
      const scaleName = currentBatch?.scale_name ? JSON.parse(currentBatch.scale_name).en :'NA'
      scales?.set(batch.windowItemId, {
        id: batch.windowItemId,
        scaleName, 
        scoreProfile: batch.scoreOptions
      });
      questionScales.push({
          id: batch.windowItemId,
          scaleName, 
          scoreProfile: batch.scoreOptions,
          score_profile_group_id: currentBatch.score_profile_group_id
      });
    })

    this.questionScales = questionScales;

    return scoreProfileGroups;
  }

  isFocusedOnStudentResponse = false
  toggleFocusView(){
    this.isFocusedOnStudentResponse = ! this.isFocusedOnStudentResponse;
  }

  isExpandView = false;
  toggleExpandView(){
    this.isExpandView = !this.isExpandView;
  }

  getScoreProfileGroups() {
    return Array.from(this.scoreProfileGroups.values());
  }

  getScales(groupId: number) {
    if(this.scoreProfileGroups == undefined || !this.scoreProfileGroups) {
      return [];
    }
    return Array.from(this.scoreProfileGroups.get(groupId)?.scales?.values());;
  }

  hasQuestionScales() {
    const hasScales = this.getScoreProfileGroups().map((group) => {
      const scales = this.getScales(group.id);
      return scales && scales.length > 0;
    });

    return !hasScales.includes(false);
  }
  
  getMyCurrentScoreCaption(){
    let selectedNodes = this.getCurrentGridAPI().getSelectedNodes();
    let selectedData = selectedNodes.map(node => node.data);
    let caption;
    if(selectedData.length != 1){
      caption = "";
    }else{
      caption = selectedData[0].score;
    }
    return this.lang.tra('txt_colon') + this.lang.tra(caption)
  }
  
  hasScoreOptions(){

    let selectedNodes = this.getCurrentGridAPI().getSelectedNodes();
    let selectedData = selectedNodes.map(node => node.data);
    if(selectedData.length != 1) return null;
    
    return this.scoreOptions && this.scoreOptions.options.length > 0;
  }

  getScoreOptions(){

    let selectedNodes = this.getCurrentGridAPI().getSelectedNodes();
    let selectedData = selectedNodes.map(node => node.data);
    if(selectedData.length != 1) return null;

    if(this.scoreOptions==null){
      return [];
    }else{
      return this.scoreOptions.options;
    }
  }

  isScoreOptionSelected(scoreOptionId:number, item?:IScale){
    if (item && this.multiScaleResponseBatchesMap.size >= 1) {
      const itemResponseBatch = this.multiScaleResponseBatchesMap.get(item.id);
      return itemResponseBatch.currentResponsBatch.responseMarkStates.get(itemResponseBatch.currentResponsBatch.responses[this.getCurrentResponseIndex()])?.myAssignedMark.scoreOptionId === scoreOptionId;
    }

    const selected = this.currResp().score_option_id;
    return selected == scoreOptionId;
  }
  
  renderOptionCaption(scoreOption:IScoreOption){
    return renderOptionCaption(scoreOption);
  }


  formatTimestamp(t) {
    return formatDate(new Date(t), 'medium', 'en-US');
  }

  showMessageCentreModal = false;
  
  messageScorer() {
    this.showMessageCentreModal = true;
  }

  isCombinationInvalid() {
    const batchResponseIndex = 0;
    const questionScales: any[] = [];
    this.scoreProfileGroups.forEach((group, groupId) => {
      questionScales.push({...group, score_profile_group_id: groupId})
    })
    return this.isResponseBatchCombinationInvalid() && isResponseMarked(batchResponseIndex, this.getScoreProfileGroups(), this.questionScales, this.multiScaleResponseBatchesMap);
  }

  isResponseBatchCombinationInvalid() {
    const currResponseIndex = this.getCurrentResponseIndex();
    const isRangeInvalid = isResponseBatchRangeInvalid(currResponseIndex, this.getItemRules(), this.multiScaleResponseBatchesMap);
    const isCombinationInvalid = isResponseBatchCombinationInvalid(currResponseIndex, this.getItemRules(), this.multiScaleResponseBatchesMap);
    
    return isCombinationInvalid || isRangeInvalid;
  }

  getItemRules() {
    const rulesMap: Map<number, IItemRules[]> = new Map()
    this.multiScaleResponseBatchesMap.forEach(batch => {
      batch.scoreProfile.itemRules.forEach((rule) => {
        if(!rulesMap.has(rule.group_to_rule_id)) {
          rulesMap.set(rule.group_to_rule_id, []);
        }
  
        rulesMap.get(rule.group_to_rule_id).push(rule);
      })
    });
    
    const debugRulesMap = Array.from(rulesMap.values());

    return rulesMap
  }

  resetSupervisorNote(){
    this.supervisorNote = null;
  }

  getEnableNYCLimit(){
    return this.whitelabelService.getSiteFlag("IS_ENABLE_NYC_LIMIT")
  }

  getLimitParams(){
    let limit = null;
    if (this.getEnableNYCLimit()){
      limit = this.limit
    }
  }

  getLocaleText(){
    this.lang.c() == 'fr' ? AG_GRID_LOCALE_FR : AG_GRID_LOCALE_EN
  }

  isValidationToolLoaded:boolean = false;
  async toggleValidationToolFilter(){
    if(!this.isValidationToolLoaded && this.filteredByValidationTool){
      this.isValidationToolLoaded = true;
      await this.reloadValidationTool();
    }
    this.recalculateInspectedCounts();
  }

  inspectedCounts: {
    assignment_item_code: string,
    inspected_num: number,
    total_num: number,
  }[] = [];
  isInspectionComplete: boolean = false;
  recalculateInspectedCounts(){
    const batchGroups = this.filteredByValidationTool? this.batchGroupsFiltered : this.batchGroups;
    const inspectedCountsByItem = {}
    for (const batchGroup of batchGroups){
      const {assignment_item_code, is_inspected, is_partially_inspected} = batchGroup
      if (!inspectedCountsByItem[assignment_item_code]){
        inspectedCountsByItem[assignment_item_code] = {
          assignment_item_code,
          inspected_num: 0,
          partially_inspected_num: 0,
          total_num: 1,
        }
      } else {
        inspectedCountsByItem[assignment_item_code].total_num += 1;
      }
      if (is_inspected){
        inspectedCountsByItem[assignment_item_code].inspected_num += 1;
      } else if (!is_inspected && is_partially_inspected){
        inspectedCountsByItem[assignment_item_code].partially_inspected_num += 1;
      }
    }
    this.inspectedCounts = Object.values(inspectedCountsByItem);
    this.isInspectionComplete = this.inspectedCounts.every(c => c.inspected_num == c.total_num)
  }

  renderIsReadInspected(inspected){
    return renderYesNo(this.lang, inspected)
  }

}





