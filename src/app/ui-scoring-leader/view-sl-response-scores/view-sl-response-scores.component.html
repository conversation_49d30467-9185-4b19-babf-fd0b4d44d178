<panel-sl-new-message *ngIf="showMessageCentreModal" [markingWindowId]="markingWindowId"
  [prefillRecipients]="[getCurrentResponseScorer()]" (close)="showMessageCentreModal = false;"></panel-sl-new-message>



<div class="page-body">
  <div>

    <header [breadcrumbPath]="breadcrumb" [hasSidebar]="true" techSupportDataKey="SCORING_SUPPORT"></header>

    <div class="page-content is-fullpage">
      <div class="page-container" style="max-width: 100%;">
        <div class="content-container">
          <strong>{{markingWindowName}}</strong>
          <hr>
          <div style="margin-bottom:2em;">

            <ng-container *ngIf="getEnableNYCLimit()">
              <tra slug="lbl_sl_responses_include"></tra> <input type="number" style="width: 6em; text-align: center;"
                [(ngModel)]="limit">
              <tra slug="lbl_sl_responses_most_recent"></tra>
            </ng-container>
            <!-- from before 
              <input type="checkbox" [(ngModel)]="isEndDateEnabled"> 
              <input [disabled]="!isEndDateEnabled" type="datetime" [(ngModel)]="endDate"> -->

            <button [class.enable-nyc-limit]="getEnableNYCLimit()" [disabled]="!selectedView || isLoading"
              (click)="loadResponses()">
              <tra slug="abed_mrkg_refresh"></tra>
            </button>
            <button *ngIf="selectedView == ScorerMenuView.SCORINGS && !isSupervisorRestrictedView" style="margin-left:2em;"
              [disabled]="!selectedView || isLoadingFlaggedRR || isLoading" (click)="reloadValidationTool()">Reload
              Validation</button>
          </div>

          <div *ngIf="!selectedView" class="notification is-warning">
            <tra slug="abed_mrkg_view_selection"></tra>
          </div>

          <ng-container>
            <menu-bar [menuTabs]="views" [tabIdInit]="selectedView" (change)="selectView($event)"></menu-bar>
          </ng-container>

          <div *ngIf="isLoading" class="notification is-info">
            <tra slug="abed_sr_loading"></tra>
          </div>

              <ng-container *ngIf="!isFocusedOnStudentResponse">
                <div style="margin-bottom:1em; display:flex; flex-direction:row; justify-content: space-between;">
                  <div>
                    <tra slug="lbl_sl_responses_export_btn_description"></tra>
                  </div>
                  <div>
                    <button class="button" (click)="exportCSV()"><tra slug="abed_invig_export_csv"></tra></button>
                      <!-- <export-table-contents [tableContents]="sanitizedResponses" [filename]=getExportFilename()></export-table-contents> -->
                  </div>
                </div>
                <div
                  *ngIf="isScoringsInspectAccess && selectedView == ScorerMenuView.SCORINGS && inspectedCounts.length"
                  class="notification is-light"
                  [class.is-warning]="!isInspectionComplete"
                  [class.is-primary]="isInspectionComplete"
                  style="max-width: 500px"
                >
                  <p *ngFor="let count of inspectedCounts" [class.has-text-success]="count.inspected_num == count.total_num">
                    <b>{{count.assignment_item_code}}:</b> 
                    {{ (count.inspected_num / count.total_num * 100) | number:'1.1-1' }}% ({{count.inspected_num}}/{{count.total_num}}) <tra slug="lbl_sl_inspected"></tra>,
                    {{ (count.partially_inspected_num / count.total_num * 100) | number:'1.1-1' }}% ({{count.partially_inspected_num}}/{{count.total_num}}) <tra slug="lbl_sl_part_inspected"></tra>
                  </p>
                </div>
                <div class="dashboard-buttons">
                  <div>
                    <div *ngIf="selectedView == ScorerMenuView.S2S">
                      <mat-slide-toggle [(ngModel)]="showRepooledResponses"> <tra slug="abed_mrkg_include_reassigned"></tra> </mat-slide-toggle>
                    </div>
                    <div *ngIf="selectedView == ScorerMenuView.SCORINGS">
                      <mat-slide-toggle [(ngModel)]="filteredByValidationTool" (change)="toggleValidationToolFilter()"> Filter by Validation Tool? </mat-slide-toggle>
                    </div>
                    <div *ngIf="selectedView == ScorerMenuView.S2S_CIN">
                      <mat-slide-toggle [(ngModel)]="showInspectedResponses"> <tra slug="abed_mrkg_include_inspected"></tra> </mat-slide-toggle>
                    </div>
                    <div class="repool-container" *ngIf="(selectedView == ScorerMenuView.S2S_CIN || selectedView == ScorerMenuView.S2S) && (isBatchGroupSelected)">
                      <div style="display:flex; flex-direction:row; justify-content: stretch; align-items: center;">
                        <span style="margin-right:0.5em;">
                          <tra slug="abed_mrkg_send_to"></tra>
                        </span>
                        <div class="select is-small">
                          <select [(ngModel)]="sendToTarget" (change)="resetSupervisorNote()">
                            <option value="POOL"><tra slug="abed_mrkg_pool"></tra></option>
                            <option value="MARKER"><tra slug="abed_mrkg_marker_by_number"></tra></option>
                          </select>
                        </div>
                        <div style="padding:0.25em" *ngIf="sendToTarget==='MARKER'">
                          <select [(ngModel)]="markerUid" [disabled]="repoolLoading">
                            <!-- keep an empty option -->
                            <option></option> 
                            <option *ngFor="let scorer of availableScorers" [value]="scorer.uid">
                                {{ scorer.marker_id }} - {{scorer.email}}
                            </option>
                          </select>
                        </div>
                        <input 
                          *ngIf="sendToTarget === 'MARKER'"
                          type="text"
                          class="input is-small super-note"
                          [disabled]="repoolLoading"
                          [(ngModel)]="supervisorNote"
                          placeholder="Write a note..."
                        >
                        <button
                          *ngIf="selectedView == ScorerMenuView.S2S_CIN"
                          class="button is-small is-success" 
                          [disabled]="!sendToTarget || repoolLoading || (sendToTarget==='MARKER' && !markerUid)" 
                          (click)="sendBackToPool(true)"
                        >
                          <tra slug="abed_send"></tra>
                        </button>
                        <button 
                          *ngIf="selectedView == ScorerMenuView.S2S"
                          class="button is-small is-success" 
                          [disabled]="!sendToTarget || repoolLoading || (sendToTarget==='MARKER' && !markerUid)" 
                          (click)="sendBackToPool(false)"
                        >
                          <tra slug="abed_send"></tra>
                        </button>
                      </div>
                    </div>
                    <div class="repool-container" *ngIf="(selectedView == ScorerMenuView.NOT_YET_CLAIMED || selectedView == ScorerMenuView.NOT_YET_CLAIMED_CAEC) && isNYCSelected && availableScorers.length">
                      <div style="display:flex; flex-direction:row; justify-content: stretch; align-items: center;">
                        <span style="margin-right:0.5em;">
                          <tra slu="ie_assign_to"></tra>
                        </span>
                        <div style="padding:0.25em">
                          <select [(ngModel)]="markerUid" [disabled]="repoolLoading">
                            <option *ngFor="let scorer of availableScorers" [value]="scorer.uid">
                                {{ scorer.marker_id }} - {{scorer.email}}
                            </option>
                          </select>
                        </div>
                        <button class="button is-small is-success" [disabled]="!markerUid || !isNYCSelectedValid() || repoolLoading" (click)="assignNYCToMarker()">
                          <tra slug="mrkg_assign"></tra>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </ng-container>

          <!-- [ngSwitch]="selectedView" -->
          <div class="flex-container" style="overflow: auto; max-width: 100vw;" *ngIf="!isLoading">
            <div class="panel-content" [class.is-compressed-to-nil]="isFocusedOnStudentResponse || isExpandView">
              <div class="table-container" *ngIf="markingWindowId">
                <ng-container [ngSwitch]="true">
                  <ng-container *ngSwitchCase="selectedView == ScorerMenuView.S2S">
                    <ag-grid-angular style="height: 100%;" class="ag-theme-alpine"
                      [rowData]="getActiveRecords(ScorerMenuView.S2S)" [gridOptions]="gridOptions"
                      [columnDefs]="getActiveColumnDefs(ScorerMenuView.S2S)" [defaultColDef]="defaultColDef"
                      (gridReady)="onGridReadyS2S($event)" (rowSelected)="onBatchGroupSelect($event)"
                      [rowSelection]="'multiple'" [enableCellTextSelection]="true">
                    </ag-grid-angular>
                  </ng-container>
                  <ng-container *ngSwitchCase="selectedView == ScorerMenuView.S2S_CIN">
                    <ag-grid-angular style="height: 100%;" class="ag-theme-alpine"
                      [rowData]="getActiveRecords(ScorerMenuView.S2S_CIN)" [gridOptions]="gridOptions"
                      [columnDefs]="getActiveColumnDefs(ScorerMenuView.S2S_CIN)" [defaultColDef]="defaultColDef"
                      (gridReady)="onGridReadyCIN($event)" (rowSelected)="onBatchGroupSelect($event)"
                      [rowSelection]="'multiple'">
                    </ag-grid-angular>
                  </ng-container>
                  <ng-container *ngSwitchCase="selectedView == ScorerMenuView.SCORINGS">
                    <ag-grid-angular style="height: 100%;" class="ag-theme-alpine"
                      [rowData]="getActiveRecords(ScorerMenuView.SCORINGS)" [gridOptions]="gridOptions"
                      [columnDefs]="getActiveColumnDefs(ScorerMenuView.SCORINGS)" [defaultColDef]="defaultColDef"
                      (gridReady)="onGridReadySCORINGS($event)" (rowSelected)="onBatchGroupSelect($event)"
                      [enableCellTextSelection]="true">
                    </ag-grid-angular>
                  </ng-container>
                  <ng-container
                    *ngSwitchCase="selectedView == ScorerMenuView.NOT_YET_CLAIMED || selectedView == ScorerMenuView.NOT_YET_CLAIMED_CAEC">
                    <ag-grid-angular style="height: 100%;" class="ag-theme-alpine"
                      [rowData]="getActiveRecords(ScorerMenuView.NOT_YET_CLAIMED)" [gridOptions]="gridOptions"
                      [columnDefs]="getActiveColumnDefs(selectedView)" [defaultColDef]="defaultColDef"
                      (gridReady)="onGridReadyNYC($event)" (rowSelected)="onNYCSelect($event)"
                      [rowSelection]="'multiple'" [enableCellTextSelection]="true">
                    </ag-grid-angular>
                  </ng-container>
                </ng-container>
              </div>
            </div>
            <div class="panel-content"
              *ngIf="getCurrentResponse() && hasCurrentResponseScan && isScanReassignAllowed && isExpandView">
              <widget-swap-scans [test_attempt_id]="getCurrentResponse().test_attempt_id"
                [markingWindowId]="markingWindowId" [markingWindowGroupId]="markingWindowGroupId"
                [scoreProfileGroups]="getScoreProfileGroups()"
                (reassignActioned)="onBatchGroupSelect(null)"></widget-swap-scans>
            </div>
            <div class="panel-response" [class.is-response-focus]="isFocusedOnStudentResponse || isExpandView"
              *ngIf="(selectedView == ScorerMenuView.SCORINGS || selectedView == ScorerMenuView.S2S || selectedView == ScorerMenuView.S2S_CIN) && isSingleResponseLoaded && getCurrentGridAPI() && multiScaleResponseBatchesMap && scoreProfileGroups">
              <div [class.is-full-width]="isFocusedOnStudentResponse || isExpandView">
                <panel-score-content [scorerUid]="getCurrentResponseScorerUid()" [responseId]="getCurrentResponseId()"
                  [windowItemId]="getCurrentResponseItemId()" responseType="TEXT"
                  [responseContent]="getCurrentResponseContent()" [isLeaderView]="true"
                  [multiScaleResponseBatchesMap]="multiScaleResponseBatchesMap"
                  [isFocusedOnStudentResponse]="isFocusedOnStudentResponse" [isExpandView]="isExpandView"
                  (toggleFocusView)="toggleFocusView()" [showExpandViewBtn]="true"
                  (toggleExpandView)="toggleExpandView()"></panel-score-content>
              </div>
              <div class="panel-score-right panel-score-action"
                [class.is-compressed-to-nil]="isFocusedOnStudentResponse">
                <div class="score-nav-container" *ngIf="false">
                  <div class="nav-progress">
                    {{getCurrentResponseIndex()+1}} <tra slug="of_enum"></tra> {{getCurrentResponses().length}}
                  </div>
                  <div class="nav-buttons">

                    <!-- Prev. Response button -->
                    <button class="button is-small" (click)="navPrevResponse()" [disabled]="isPrevRespDisabled()">
                      <tra slug="btn_prev_resp"></tra>
                    </button>

                    <!-- Next Response / Validate button -->
                    <button class="button is-small" (click)="navNextResponse()" [disabled]="isNextRespDisabled()">
                      <span>
                        <tra slug="btn_next_resp"></tra>
                      </span>
                    </button>
                  </div>
                </div>
                <div class="assigned-flag-container space-between" style="align-items: flex-start;">
                  <a (click)="isHistoryShowing=!isHistoryShowing">
                    <tra slug="sa_se_score_history"></tra>
                  </a>
                  <div style="display: flex;flex-direction: column; gap: 0.5em;">
                    <mat-slide-toggle *ngIf="!isCurrentReadExpert()" [(ngModel)]="isDirectEditingMode">
                      <tra slug="lbl_sl_responses_direct_editing"></tra>
                    </mat-slide-toggle>
                    <mat-slide-toggle [(ngModel)]="showInvalidReads">
                      <tra slug="lbl_sl_responses_show_invalid_reads"></tra>
                    </mat-slide-toggle>
                  </div>
                </div>
                <div *ngIf="!hasValidRead()" class="empty-score-panel">
                  <tra slug="lbl_sl_responses_no_valid_reads"></tra>
                </div>
                <div *ngIf="hasValidRead()">
                  <div *ngIf="isHistoryShowing && hasScorerHistory">
                    <table class="table" style="font-size: 0.85em">
                      <tr>
                        <th>
                          <tra slug="lbl_sl_responses_read_number"></tra>
                        </th>
                        <th>
                          <tra slug="lbl_sl_responses_marker_number"></tra>
                        </th>
                        <th *ngIf="isGroupMarking">
                          <tra slug="lbl_sl_responses_marking_group_number_when_scored"></tra>
                        </th>
                        <th>
                          <tra slug="date"></tra>
                        </th>
                        <th style="text-wrap: nowrap;">
                          <tra slug="lbl_status"></tra>
                        </th>
                        <th>
                          <tra slug="lbl_sl_invalidated_column"></tra>
                        </th>
                        <th>
                          <tra slug="lbl_sl_scor_cols_inspected"></tra>
                        </th>
                      </tr>
                      <tr *ngFor="let entry of getScorerHistory() | keyvalue">
                        <td>
                          <div style="display:flex; flex-direction: row; align-items:center;">
                            <span class="tag" style="margin-right:1em">
                              {{ entry.value.read_num }}
                            </span>
                            <button class="button is-small" [class.is-info]="entry.key == currentMarkerReadId"
                              (click)="selectHistory(entry.key)" [disabled]="isHistoryLoading">
                              <tra slug="mrkg_item_asgn_btn_select"></tra>
                            </button>
                          </div>
                        </td>
                        <td>{{ entry.value.swum_marker_number || 'Leader' }}</td>
                        <td *ngIf="isGroupMarking">
                          <span *ngIf="entry.value.marking_group_number_when_scored">
                             # {{ entry.value.marking_group_number_when_scored }}
                             <span *ngIf="entry.value.marking_group_number_when_scored_revoked_on">
                              (Archived On {{entry.value.marking_group_number_when_scored_revoked_on}})
                             </span>
                          </span>
                          <span *ngIf="!entry.value.marking_group_number_when_scored">N/A</span>
                        </td>
                        <!-- <td>{{ entry.value.uid }}</td> -->
                        <td>{{ entry.value.date | date:'MMM d HH:mm' }}</td> <!-- Format date as desired -->
                        <!-- <td>{{ entry.value.inspected }}</td> -->
                        <td>
                          <div class="tags">
                            <span *ngIf="entry.value.is_marked == 1" class="tag is-success">
                              <tra slug="lbl_completed"></tra>
                            </span>
                            <span *ngIf="!entry.value.is_marked || entry.value.is_marked == 0" class="tag is-info">
                              <tra slug="lbl_active"></tra>
                            </span>
                            <span *ngIf="entry.value.is_expert_score" class="tag is-primary">
                              <tra slug="lbl_expert_score"></tra>
                            </span>
                          </div>
                          <div class="tags" *ngIf="isDirectEditingMode && !isCurrentReadExpert()">
                            <span *ngIf="entry.value.is_sent_back" class="tag">Sent Back</span>
                            <span *ngIf="entry.value.is_rescore" class="tag">Rescored?</span>
                            <span *ngIf="entry.value.is_valid_before_rescore" class="tag">Valid Before Rescore?</span>
                          </div>
                        </td>
                        <td>
                          <div *ngIf="entry.value.invalid">
                            <span *ngIf="entry.value.is_before_rescore" class="tag is-info">Rescore</span>
                            <span *ngIf="!entry.value.is_before_rescore">
                              <tra slug="lbl_sl_invalidated"></tra>
                            </span>
                          </div>
                          <button *ngIf="!entry.value.invalid" class="button is-small"
                            (click)="invalidateRead(true, entry.key)"
                            [disabled]="!isDirectEditingMode || entry.value.is_before_rescore || isCurrentReadExpert()">
                            <tra slug="lbl_sl_invalidate"></tra>
                          </button>
                          <button *ngIf="entry.value.invalid" class="button is-small"
                            (click)="invalidateRead(false, entry.key)"
                            [disabled]="!isDirectEditingMode || entry.value.is_before_rescore || isCurrentReadExpert()">
                            <tra slug="lbl_sl_undo_inv"></tra>
                          </button>
                        </td>
                        <td>{{renderIsReadInspected(entry.value.inspected)}}</td>
                      </tr>
                      <tr>
                        <td colspan="8">
                          <button class="button" style="margin-top: 0.5em;" (click)="createRead()"
                            [disabled]="!isDirectEditingMode || isCreatingRead || getCurrentResponse(true)?.is_expert_score">
                            <tra slug="lbl_sl_create_read"></tra>
                          </button>
                        </td>
                      </tr>
                    </table>

                  </div>
                  <div [ngSwitch]="showInvalidReads || (!showInvalidReads && !isCurrentReadInvalid())">
                    <div *ngSwitchCase="false" class="empty-score-panel">
                      <tra slug="lbl_sl_select_read"></tra>
                    </div>
                    <div *ngSwitchCase="true">
                      <div style="margin-top: 1em; margin-bottom: 1em;"
                        *ngIf="selectedView == ScorerMenuView.S2S && (isBatchGroupSelected)">
                        <button class="button is-fullwidth is-yellow" (click)="confirmNrIns()"
                          [disabled]="!hasTopFlag() || scorerHistory?.get(currentMarkerReadId)?.invalid">
                          <tra slug="lbl_sl_confirm_flag"></tra>
                        </button>
                      </div>

                      <div class="assigned-flag-container" *ngIf="getCurrentBottomFlag()">
                        <div>
                          <button class="button is-small is-flag">
                            {{getCurrentBottomFlag().slug}}
                          </button>
                          <button class="button is-small" (click)="clearBottomFlags()">
                            Clear
                          </button>
                        </div>
                      </div>
                      <div class="score-assign-container">
                        <div *ngIf="hasQuestionScales()" class="score-option-container">
                          <div *ngFor="let group of getScoreProfileGroups()">
                            <div class="space-between">
                              <div class="name">{{group.name}}</div>
                              <div *ngIf="hasTopFlagOptions(group.id)" class="general-score-options">
                                <button *ngFor="let flagOption of getTopFlagOptions(group.id)" class="button is-small"
                                  [class.is-flag]="isFlagOptionSelected(flagOption.id, group.id)"
                                  [disabled]="!isDirectEditingMode || batchBeforeRescore || isCurrentReadExpert()"
                                  (click)="assignFlag(flagOption.id, group.id)">
                                  <tra [slug]="renderOptionCaption(flagOption)"></tra>
                                </button>
                              </div>
                            </div>
                            <div *ngIf="getCurrentFlagMessage(group)" style="margin-top:0.5em;"
                              class="notification is-small is-danger">
                              {{getCurrentFlagMessage(group)}}
                            </div>
                            <div class="contents">
                              <div *ngFor="let scale of getScales(group.id)">
                                <div style="margin: 0.5em; display: flex; flex-direction: column;">
                                  <div class="space-between" style="min-width: 6rem;">
                                    <span>
                                      {{scale.scaleName}}
                                    </span>
                                    <span *ngIf="isDirectEditingMode && !isCurrentReadExpert()">
                                      id: {{getMcbrId(scale)}}
                                    </span>
                                    <div style="margin-bottom:0.5em" *ngIf="isDirectEditingMode && !isCurrentReadExpert()">
                                      Read Type:
                                      <ng-container *ngIf="!scale._isEditingReadRule">
                                        {{getReadType(scale)}}
                                        <button (click)="scale._isEditingReadRule = true"
                                          [disabled]="batchBeforeRescore">Edit</button>
                                      </ng-container>
                                      <ng-container *ngIf="scale._isEditingReadRule">
                                        <select [(ngModel)]="scale._new_from_read_rule"
                                          (change)="onSelectedFromReadRuleChange(scale)" style="display:inline-block">
                                          <option *ngFor="let category of getReadRuleCategories()">{{category}}</option>
                                        </select>
                                        <button (click)="confirmFromReadRuleChange(scale)">Confirm</button>
                                        <button (click)="scale._isEditingReadRule = false">Cancel</button>
                                      </ng-container>
                                    </div>
                                    <div *ngIf="isDirectEditingMode && !isCurrentReadExpert()">
                                      <button (click)="toggleScaleSuppression(scale)"
                                        [disabled]="batchBeforeRescore">Toggle Scale Supression
                                        ({{checkScaleSuppression(scale) ? 'On' : 'Off'}})</button>
                                    </div>
                                  </div>
                                  <div class="score-row-container">
                                    <div class="score-options">
                                      <div *ngFor="let scoreOption of scale.scoreProfile"
                                        [class.is-offset]="scoreOption.is_offset" class="score-option">
                                        <button class="button is-fullwidth"
                                          [class.is-info]="isScoreOptionSelected(scoreOption.id, scale)"
                                          [disabled]="!isDirectEditingMode || checkScaleSuppression(scale) || batchBeforeRescore || isCurrentReadExpert()"
                                          (click)="assignScore(scoreOption.id, scale)">
                                          <tra [slug]="scoreOption.slug"></tra>
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <hr />
                          </div>
                        </div>
                        <div *ngIf="isCombinationInvalid()" class="notification is-warning">
                          <tra slug="Invalid score combination"></tra>
                        </div>
                        <div class="score-flag-container"
                          *ngIf="(selectedView == ScorerMenuView.S2S_CIN && hasTopFlag()) || isScoringsInspectAccess">
                          <button class="button is-success is-outlined is-small is-fullwidth"
                            (click)="markInspected(true)" *ngIf="!isReadInspected() && (isResponseFlagged() || isScoringsInspectAccess)">
                            <tra slug="Mark as Inspected"></tra>
                          </button>
                          <ng-container *ngIf="isReadInspected() && (isResponseFlagged() || isScoringsInspectAccess)">
                            <button class="button is-success is-outlined is-small is-fullwidth"
                              (click)="markInspected(false)">
                              <tra slug="Undo Inspection"></tra>
                            </button>
                            <p *ngIf="currReadInspectedMarkerNumber()" class=has-text-centered>
                              <b><tra slug="lbl_sl_inspected_by_swum_marker_number"></tra></b> {{currReadInspectedMarkerNumber()}}
                            </p>
                          </ng-container>
                        </div>
                        <div class="score-flag-container">
                          <ng-container>
                            <!-- hiddne because the options are not -->
                            <button (click)="isShowFlagOptions=!isShowFlagOptions"
                              class="button is-small is-danger is-outlined has-icon is-fullwidth">
                              <span>
                                <tra slug="btn_report_issue_scor_resp"></tra>
                              </span>
                              <span class="icon" [ngSwitch]="!!isShowFlagOptions">
                                <i *ngSwitchCase="true" class="fas fa-caret-down"></i>
                                <i *ngSwitchCase="false" class="fas fa-caret-right"></i>
                              </span>
                            </button>
                            <div *ngIf="isShowFlagOptions"
                              style="border:1px solid #f14668; padding:0.5em; background-color:white; width: max-content;">
                              <div *ngFor="let flagOption of flagOptions" class="flag-option"
                                [ngSwitch]="activeFlagId == flagOption.id">
                                <button class="button is-small is-white" *ngSwitchCase="false"
                                  (click)="selectFlag(flagOption.id)">
                                  <tra [slug]="flagOption.caption"></tra>
                                </button>

                                <div *ngSwitchCase="true" class="message-insertion">
                                  <strong>
                                    <tra [slug]="flagOption.caption"></tra>
                                  </strong>
                                  <tra [slug]="flagOption.report_msg"></tra>
                                  <textarea *ngIf="flagOption.is_comment_req == 1" [(ngModel)]="flagMessage"
                                    [disabled]="isFlagOptionSelected(flagOption.id)" rows="2"
                                    class="textarea is-small is-fullwidth">
                                      </textarea>
                                  <div [ngSwitch]="isFlagOptionSelected(flagOption.id)">
                                    <div *ngSwitchCase="true" style="margin-top:0.5em; line-height:1.1em;">
                                      <tra slug="txt_notif_sent_scor"></tra>
                                      <!-- <button (click)="undoReport(flagOption.id)" class="button is-small">Undo Report</button> -->
                                    </div>

                                    <ng-container *ngSwitchCase="false">
                                      <button (click)="deselectFlag()" class="button is-small">
                                        <tra slug="btn_cancel"></tra>
                                      </button>
                                      <button (click)="assignFlag(flagOption.id)"
                                        [disabled]="flagOption.is_comment_req == 1 ? !flagMessage : false"
                                        class="button is-small">
                                        <tra slug="lbl_send"></tra>
                                      </button>
                                    </ng-container>

                                  </div>
                                </div>
                              </div>
                            </div>
                          </ng-container>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <footer [hasLinks]="false" techSupportDataKey="SCORING_SUPPORT"></footer>
</div>

<!-- <chat-box [accountType]="'leader'"></chat-box> -->