import { Component, OnInit, NO_ERRORS_SCHEMA } from '@angular/core';

import {
  observePresenceRecords, 
  FBC_SRF_Presence, 
  FBC_SRF_PresenceRecord, 
  FIREBASE_COLLECTION_ID_SCOR_RAFI_PRESENCE, 
  updatePresenceRecord, 
  RAFI_PRESENCE_HEARTBEAT_MS, 
  removePresenceRecord,
  isWithinHeartbeat,
} from './model/firebase-score-rafi';

import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { AccountType } from '../../constants/account-types';
import { LangService } from '../../core/lang.service';
import { Router, ActivatedRoute } from '@angular/router';
import { LoginGuardService } from '../../api/login-guard.service';
import { SidepanelService } from '../../core/sidepanel.service';
import { AuthService } from '../../api/auth.service';
import {CdkDragDrop, moveItemInArray} from '@angular/cdk/drag-drop';
import { getScoringLeaderPanels } from './../../core/main-nav/panels/scoring-leader'
import * as moment from 'moment-timezone';
import { IItemRules } from './../../ui-scorer/panel-scoring/types/item-rules';
import { ResponseBatch, ResponseBatchService } from '../../ui-scorer/response-batch.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { RoutesService } from 'src/app/api/routes.service';
import { isResponseCombinationInvalid } from './../widget-score-options/util'
const _ = require('lodash');
import { saveAs } from 'file-saver';
import { Document, Packer, Paragraph, TextRun } from 'docx';
import { getPrintDocument, arrangeResponseDataForPrint } from './print-docx-utils';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { ISRAFIResponseRecord } from './model/types';
import { Subscription } from 'rxjs';

const MAX_RESPONSE_RAW_PULL_LIMIT = 500;

const MAX_PRINT_CAPACITY = 100;

enum FilterSpecialValues {
  ALL = "ALL",
  NO_VALUE = "NO_VALUE"
}

interface FilterPart {
  val: any[],  
  caption: string, 
  options?: any[], 
  isUserInput?: boolean,
  getProp?: string, 
  isActive?: boolean,
  filterProp?: string[], // Use if the prop is a property of the response, or
  isCustomMatchCheck?: (response, targetValue) => boolean; // Do a custom check for response matching filter,
  isCustomNullMatchCheck? : (response) => boolean
}

export enum RangeFindingModalType {
  NEW_COMMENT = "NEW_COMMENT",
  COMMENT_EDIT = "COMMENT_EDIT",
  COMMENT_REPLY = "COMMENT_REPLY",
  MULTI_EXPERT_SCORE = "MULTI_EXPERT_SCORE",
}

enum ViewType {
  STAGE = "STAGE", // A stage in range finding
  SET = "SET" // Read-only view of only responses in a certain set (after all stages are done)
}

export enum RANGE_CATEGORIES {
  REJECTED = "REJECTED",
  PERFORMANCE = "PERFORMANCE",
  NON_PERFORMANCE = "NON_PERFORMANCE",
  OTHER = "OTHER"
}

export const rangeCategorySettings = [
  {id: RANGE_CATEGORIES.REJECTED, bgColor: '#FFD2D2'},
  {id: RANGE_CATEGORIES.PERFORMANCE, caption: 'lbl_abed_tag_category_perf', bgColor: '#D2E9FF'},
  {id: RANGE_CATEGORIES.NON_PERFORMANCE, caption: 'lbl_abed_tag_category_nonperf', bgColor: '#FFEAD2'},
  {id: RANGE_CATEGORIES.OTHER, bgColor: '#D2D5FF'}
]

@Component({
  selector: 'view-sl-validity',
  templateUrl: './view-sl-validity.component.html',
  styleUrls: ['./view-sl-validity.component.scss']
})
export class ViewSlValidityComponent implements OnInit {
  
  constructor(
    private route: ActivatedRoute,
    private routes: RoutesService,
    private breadcrumbsService: BreadcrumbsService,
    private auth: AuthService,
    public lang: LangService,
    private router: Router,
    public  loginGuard: LoginGuardService,
    private sidePanel: SidepanelService,
    private pageModalService: PageModalService,
    private whitelabel: WhitelabelService
  ) { }

  pageModal: PageModalController;
  RangeFindingModalType = RangeFindingModalType;
  viewType: ViewType;
  ViewType = ViewType;
  breadcrumb:any[];
  isLoaded:boolean = true;
  isNetworkBusy:boolean;
  isResponsesLoading: boolean = false;
  markingWindowId:number;
  syncId:number;
  itemName:string;
  stageNum:number;
  setId:number;
  setSlug:string;
  loadedSyncId:number;
  loadedStageNum:number;
  loadedResponsesId:number;
  currentResponseId:number;
  stageNumSelection:number;
  loadedResponses:Partial<ISRAFIResponseRecord>[] = [];
  responses:ISRAFIResponseRecord[] = [];
  responseIds:number[] = []; // not used
  scoreProfile = {}; // to be initialized
  currentResponseIndex:number;
  currentResponse:ISRAFIResponseRecord = {id: undefined};
  isEditingRationale:boolean;
  ranges = []
  rangeRef;
  scoreOptions = []
  scoreOptionRef;
  responseSets = [];
  responseSetTypes = [];
  responseSetRef;
  targetResponseSet:number;
  filterParts:{[key:string]: FilterPart};
  isFilterEditing:boolean;
  currentResponseSet:ISRAFIResponseRecord[] = [];
  itemCaption:string;
  itemGroupName:string;
  syncItemsInSameGroup: {id: number, group_to_mwi_id: number, caption: string}[];
  isEnteringHistorical:boolean;
  newResponse: Partial<ISRAFIResponseRecord> = {};
  isRangeFinder:boolean;
  isCodeColumnCollapsed: boolean = false;
  isPrintDocProcessing: boolean = false;
  FilterSpecialValues = FilterSpecialValues;
  isCountsExpanded = false;
  filterRejectedResponse: boolean = true;


  ngOnInit(): void {
    this.loginGuard.activate();
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.route.params.subscribe(params => {
      this.markingWindowId = +params['markingWindowId'];
      this.syncId = params['syncId'];
      this.stageNum = params['stageNum'] ? +params['stageNum'] : undefined;
      this.setId = params['setId'] ? +params['setId'] : undefined;
      this.setSlug = params['setSlug'];
      this.itemName = params['itemName'];

      // Two uses of this page based on the URL - a stage in set selection, or the completed set
      if (this.stageNum) {
        this.viewType = ViewType.STAGE
      } else if (this.setId) {
        this.viewType = ViewType.SET
      }

      this.updateBreadcrumb();
      this.initRoute()
      this.initCurrentPresence();
    });
    getScoringLeaderPanels(this.auth, this.routes, this.isRangeFinder).then(scoringLeaderPanels => {
      this.sidePanel.activate(scoringLeaderPanels, true);
      this.sidePanel.unexpand();
    })
  }
  async initRoute(){
    if (this.syncId !== this.loadedSyncId){
      await this.checkLockStatus();
      await this.initScoreOptions();
      await this.initRangeOptions();
      await this.initResponseSetOptions();
      await this.initFilters();
    }
    if (this.stageNum !== this.loadedStageNum){
      this.clearFilters()
    }
    await this.updateFilter();
    this.loadedSyncId = this.syncId;
    this.loadedStageNum = this.stageNum;
    this.stageNumSelection = this.stageNum;
    this.onTargetResponseSetChange();
  }

  isLocked:boolean;
  isExpertScoreEdit: boolean;
  async checkLockStatus(){
    this.isLocked = true;
    if (this.markingWindowId){
      const scoringWindowStatus = await this.auth.apiGet('public/scor-lead/windows', this.markingWindowId);
      this.isLocked = scoringWindowStatus.is_locked == 1;
      this.isExpertScoreEdit = scoringWindowStatus.is_expert_score_edit == 1;
    }
  }

  getUid(){
    return this.auth.getUid()
  }


  rangeCategories: {
    id: RANGE_CATEGORIES,
    caption?: string,
    bgColor: string,
    ranges
   }[];

  async initRangeOptions(){

    const allowedRangeRecords = await this.auth.apiGet(this.routes.SCOR_LEAD_MARKING_STANDARDS_RANGE_TAGS, this.syncId);

    this.rangeCategories = rangeCategorySettings.map(category => {
      const categoryRanges = allowedRangeRecords.filter(r => r.category == category.id)
      .map(r => {
        return {props:{tag1: r.id}, slug: this.lang.tra(r.slug), caption: this.lang.tra(r.caption_slug)} 
      })
      return {...category, ranges: categoryRanges}
    })
    .filter(category => category.ranges.length);

    this.ranges = [];
    this.rangeCategories.forEach(r => {
      this.ranges = [...this.ranges, ...r.ranges]
    })
    this.rangeRef = new Map();
    this.ranges.forEach(range => {
      this.rangeRef.set(range.props.tag1, range);
    })
  }

  async initResponseSetOptions(){
    const {responseSets, responseSetTypes} = await this.auth.apiFind('public/scor-lead/validity/set', {
      query: {
        sync_id:this.syncId
      }
    });

    this.responseSets = responseSets.map(r => {
      let slug = r.slug;
      let caption = r.custom_name || this.lang.tra(r.caption_slug)
      if (r.set_type_variant_num > 1) {
        if (!r.custom_name){
          caption += ` ${r.set_type_variant_num}`
        }
        slug += `${r.set_type_variant_num}`
      }
      return {...r, caption, slug}
    });

    this.responseSetTypes = responseSetTypes.map(r => {
      return {...r, caption: this.lang.tra(r.caption_slug)}
    });


    this.responseSetRef = new Map();
    this.responseSets.forEach(responseSet => {
      this.responseSetRef.set(responseSet.props.set_id, responseSet);
    })
  }

  isSetReleasing:boolean;
  async makeSetAvailable(){
    if (this.targetResponseSet){
      this.isSetReleasing = true;
      await this.auth.apiCreate('/public/scor-lead/validity/set', {
        sync_id: this.syncId,
        set_id: this.targetResponseSet
      })
      .then( ()=> {
        alert('The updated response set has been published.')
        this.isSetReleasing = false;
      })
      .catch(()=>{
        alert('An error occurred while making the response set available.')
      })
    }
  }

  /** Where we need to get the caption etc. */
  markingWindowItems: any[] = [];
  async initScoreOptions(){
    if (this.loadedSyncId !== this.syncId){
      this.scoreOptionRef = new Map();
      this.scoreOptions = [];
      const {sync_caption, sync_score_profile_group_name, items, sync_items_in_same_group} =  await this.auth.apiGet('public/scor-lead/validity/item', this.syncId);
      this.itemCaption = sync_caption; // not the best place to put this...
      this.itemGroupName = sync_score_profile_group_name; // not the best place to put this...
      this.syncItemsInSameGroup = sync_items_in_same_group;
      this.markingWindowItems = items.map(item => {
        const { id, scale_description, scale_skill_code, scoreOptions, flagOptions, itemRules} = item;
        const scaleName = JSON.parse(scale_description)[this.lang.c()]
        return {id, scaleName, scale_skill_code, scoreOptions, flagOptions, itemRules}
      });
      this.markingWindowItems.forEach(mwi => {
        mwi.scoreOptions = mwi.scoreOptions.map(record => {
          const score_option_id = record.id;
          const scoreOption = {
            props:{score_option_id}, 
            slug: this.lang.tra(record.slug || record.value), 
            caption: this.lang.tra(record.caption || record.slug || record.value),
          };
          if (!this.scoreOptions.some(option => option.slug == scoreOption.slug)){
            this.scoreOptions.push(scoreOption)
          }
          this.scoreOptionRef.set(score_option_id, scoreOption);
          return scoreOption;
        })
      })
    }
  }

  renderRefProp <T,Y>(ref:Map<T, Y>, key:T, outputProp:string='slug'){
    const entry = ref.get(key);
    if (entry){
      return entry[outputProp];
    }
  }
  renderRangeSlug = (rangeTag:number) => this.renderRefProp(this.rangeRef, rangeTag);
  renderResponseSetSlug = (set_id:number) => {
    for(let set of this.responseSets) {
      if(set.props.set_id == set_id) return set.slug;
    }

    return null;
  };
  renderScoreOptionCaption = (scoreOptionId:number) => this.renderRefProp(this.scoreOptionRef, scoreOptionId, 'caption');
  renderScoreOptionSlug = (scoreOptionId:number) => this.renderRefProp(this.scoreOptionRef, scoreOptionId);

  // to do: this is defined in the db, no need for it to be hard-coded here
  renderResponseSetTypeSlug = (responseSetNum:number) => {
    if(responseSetNum == 1) {
      return 'T'
    }
    if(responseSetNum == 2) {
      return 'V'
    }
    if(responseSetNum == 3) {
      return 'PT'
    }
    if(responseSetNum == 4) {
      return 'QT'
    }
    if(responseSetNum == 5) {
      return 'A'
    }
  }


  checkStageSelection(stages:number[]){
    return (stages.indexOf(+this.stageNumSelection) !== -1);
  }

  selectSetById(set_id:number){
    this.targetResponseSet = set_id;
    this.onTargetResponseSetChange();
  }
  selectSetByType(setNum:number){
    for(let set of this.responseSets) {
      if(set.props.response_set_num == setNum) {
        this.targetResponseSet = set.props.set_id
      }
    }
    this.onTargetResponseSetChange();
  }

  checkSetSelection(setId:number){
    return this.targetResponseSet && (this.targetResponseSet == setId)
  }

  onTargetResponseSetChange(){
    this.currentResponseSet = [];
    if (this.targetResponseSet){
      const responseSet = [];

      for(let response of this.loadedResponses) {
        let inserted = false;

        let inclusionRecord = response.inclusions[this.targetResponseSet];
        if(inclusionRecord) {
          //if it belongs in the set, insert it in order
          for(let r = 0; r < this.currentResponseSet.length; r++) {
            let currentInclusionRecord = this.currentResponseSet[r].inclusions[this.targetResponseSet];

            //the response at counter also belongs, check order
            if(currentInclusionRecord) {
              if(inclusionRecord.response_set_order < currentInclusionRecord.response_set_order) {
                this.currentResponseSet.splice(r, 0, <ISRAFIResponseRecord> {...response, inFocusedSet:true});
                inserted = true;
                break;
              }
            }
            else {
              this.currentResponseSet.splice(r, 0, <ISRAFIResponseRecord> {...response, inFocusedSet:true});
              inserted = true;
              break;
            }
          }

          if(!inserted) this.currentResponseSet.push(<ISRAFIResponseRecord> {...response, inFocusedSet:true});


        }
        else {
          //otherwise, add it to the end of the list.
          this.currentResponseSet.push(<ISRAFIResponseRecord> {...response, inFocusedSet:false});
        }

      }

      // In case the order numbers in data are not valid to start with, take this opportunity to refresh it to the order of the items on the display
      this.refreshCurrResponseSetOrder();
    }
  }


  updateView(update: any){
    const config = {
      ... {
        markingWindowId: this.markingWindowId,
        syncId: this.syncId,
        stageNum: this.stageNum,
      },
      ... update
    }
    this.router.navigateByUrl(
      this.getBaseRoute()+`/validity/${config.markingWindowId}/${config.syncId}/${config.stageNum}`
    )
  }

  incrStageNum(incr:number){
    let num = (+this.stageNumSelection) + incr;
    if (num < 1){
      num = 1;
    }
    if (num > 4){
      num = 4;
    }
    this.stageNumSelection = num;
    this.updateView({stageNum: this.stageNumSelection})
  }
  
  getBaseRoute(){
      const lang = this.lang.c()
      const accountType = this.isRangeFinder ? AccountType.SCOR_RAFI : AccountType.SCOR_LEAD
      return `/${lang}/${accountType}`;
  }

  /**
   * Update the breadcrumbs.
   * Account for two possible uses of this page - viewing a seleciton stage linked from Range Finding, or viewing a completed set linked from Response Sets.
   */
  updateBreadcrumb(){

    // Use URL instead of this.auth.accountTypeSub.subscribe(currAccount... in case both roles are opened and the page is refreshed (it should match the URL, not the latest account they entered)
    this.isRangeFinder = this.router.url.includes("scor-rafi")

    const baseRoute = this.getBaseRoute();
    const dashboardSlug = this.isRangeFinder ? (this.isABED() ? "lbl_abed_standards_confirmer_dashboard" : "lbl_osslt_range_find_dashboard") : "lbl_osslt_scorlead_dashboard"
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT( this.lang.tra(dashboardSlug), `${baseRoute}/dashboard`),
      this.breadcrumbsService._CURRENT( 'Training Test Window '+this.markingWindowId, `${baseRoute}/training/${this.markingWindowId}`),
    ];

    if (this.viewType == ViewType.SET) {
      const responseSets = this.breadcrumbsService._CURRENT( "Response Sets", `${baseRoute}/training/${this.markingWindowId}/response-sets`);
      this.breadcrumb.push(responseSets)
    } else if (this.viewType == ViewType.STAGE) {
      const rangeFinder = this.breadcrumbsService._CURRENT( this.lang.tra('lbl_abed_standard_confirming'), `${baseRoute}/training/${this.markingWindowId}/range-finding`);
      this.breadcrumb.push(rangeFinder)
    }

    let currentTabName = this.lang.tra('lbl-scor_resp_for_item') + ' ('+this.syncId+')';
    if (this.viewType == ViewType.SET) currentTabName += ` - ${this.setSlug}` 
    this.breadcrumb.push(this.breadcrumbsService._CURRENT( currentTabName, this.router.url),)

  }

  gotoPrevResponse(){
    const i = this.getCurrentResponseIndex();
    if (i > 0){
      return this.selectResponseByIndex(i - 1);
    }
  }
  gotoNextResponse(){
    const i = this.getCurrentResponseIndex();
    if ( i < this.responses.length -1 ){
      return this.selectResponseByIndex(i + 1);
    }
  }

  isFocusedOnStudentResponse = false
  toggleFocusView(){
    this.isFocusedOnStudentResponse = ! this.isFocusedOnStudentResponse;
  }

  wsPresenceSub:Subscription
  wsPresenceHeartbeatInterval:any
  responseIdPresence:Map<number, FBC_SRF_Presence> = new Map()
  async initCurrentPresence(){
    const ctx = this.whitelabel.context
    const mwi_id = +this.syncId;
    const presenceObservable = observePresenceRecords(ctx, mwi_id);
    this.wsPresenceHeartbeatInterval = setInterval(() => this.updateCurrentPresence(), RAFI_PRESENCE_HEARTBEAT_MS)
    this.wsPresenceSub = presenceObservable.subscribe({
        next: (records) => {
          this.cachePresenceMappings(records);
            console.log('Updated presence records:', records);
        },
        error: (error) => {
            console.error('Error observing presence records:', error);
        }
    });
  }
  cachePresenceMappings(records:FBC_SRF_Presence[]){
    this.responseIdPresence = new Map()
    for (let record of records){
      this.responseIdPresence.set(+record.response_id, record);
    }
  }
  destroyWsPresence(){
    if (this.wsPresenceSub){
      this.wsPresenceSub.unsubscribe();
      this.wsPresenceSub = null;
    }
    if (this.wsPresenceHeartbeatInterval) {
      console.log('clearInterval(this.wsPresenceHeartbeatInterval')
      clearInterval(this.wsPresenceHeartbeatInterval);
      this.wsPresenceHeartbeatInterval = null;
    }
  }

  isResponsePresence(response:ISRAFIResponseRecord){
    // todo: filter by date
    const usersOnline = this.getResponsePresence(response);
    return !! usersOnline.length
  }
  getResponsePresence(response:ISRAFIResponseRecord){
    const users = this.responseIdPresence.get(+response.id)?.users || []
    const usersOnline = users.filter(user => (user.last_touch_on && isWithinHeartbeat(user.last_touch_on)) )
    return usersOnline
  }

  updateCurrentPresence(){
    console.log('updateCurrentPresence')
    if (this.currentResponse){
      this.trackPresence(this.currentResponse)
    }
  }

  async trackPresence(response:ISRAFIResponseRecord){
    console.log('trackPresence', response.id);
    const ctx = this.whitelabel.context
    const mwi_id = +this.syncId;
    const uid = this.auth.getUid();
    const tabSessionHash = this.auth.getTabSessionHash();
    const display_name = this.auth.getDisplayName();
    updatePresenceRecord(
      ctx,
      mwi_id,
      response.id,
      uid,
      display_name,
      tabSessionHash,
    )
  }
  removePresence(response:ISRAFIResponseRecord){
    const ctx = this.whitelabel.context
    const mwi_id = +this.syncId;
    const uid = this.auth.getUid();
    removePresenceRecord(
      ctx,
      mwi_id,
      response.id,
      uid,
    )
  }
  selectResponseByIndex(index:number){
    this.selectResponse(this.responses[index]);
  }
  isCurrentResponse(){
    return !! this.currentResponse?.id
  }
  ensureSoftNoOtherEditors(response:ISRAFIResponseRecord){

    const users = this.getResponsePresence(response);
    for (let user of users){
      isWithinHeartbeat(user.last_touch_on, true)
    }

    if (this.isResponsePresence(response)){
      const isAcceptConflict = confirm('You are entering a response that is being accessed by another standards confirming. Would you like to continue?')
      if (!isAcceptConflict){
        throw new Error();
      }
    }
  }
  async ensureCurrentRationaleSave(){
    if (this.isEditingRationale){
      try {
        await this.saveRationaleEdit(this.currentResponse.rationale)
      }
      catch(e){
        if (!confirm('Would you like to continue to another response?')){
          throw new Error();
        }
      }
    }
  }
  async selectResponse(response:ISRAFIResponseRecord){

    this.ensureCurrentRationaleSave() // can exit (if save fails)
    this.ensureSoftNoOtherEditors(response); // can exit (if user does not confirm)

    if (this.currentResponse && (this.currentResponse !== response) ){
      this.removePresence(this.currentResponse)
    }
    this.currentResponse = response;
    this.currentResponseIndex = this.responses.indexOf(response);
    this.currentResponseId = response.id;
    this.trackPresence(response)
    this.scrollToResponseProgress();
    await this.loadLatestStats(response);
    await this.loadComments();
  }
  /** When selecting a response re-pull its details again (so that the user sees any latest changes) */
  async loadLatestStats(response){
    this.isNetworkBusy = true;
    const latestResponseDetail = await this.auth.apiGet(this.routes.SCOR_LEAD_VALIDITY_RESPONSES, this.currentResponseId, {query:{sync_id: this.syncId}})
    this.sanitizeResponseStats(latestResponseDetail);
    this.currentResponse = latestResponseDetail
    this.currentResponse.isCombinationInvalid = isResponseCombinationInvalid(this.currentResponse.id, this.currentResponse.mwi_id_to_score_option_id, this.markingWindowItems)
    this.responses[this.currentResponseIndex] = this.currentResponse
    const targetLoadedResponse = this.loadedResponses.find(r => r.id == response.id)
    this.loadedResponses[this.loadedResponses.indexOf(targetLoadedResponse)] = this.currentResponse
    this.isNetworkBusy = false;
  }
  async loadComments(){
    if (!this.currentResponse) return;
    this.currentResponse.commentTree = [];
    if (!this.currentResponse.mrsel_id){
      return;
    }
    const {comments, users} = await this.auth.apiGet(this.routes.SCOR_LEAD_VALIDITY_COMMENTS, this.currentResponse.mrsel_id)
    const userRef = new Map();
    users.forEach(user => {
      userRef.set(user.id, user)
    })
    const parentCommentRef = new Map();
    comments.forEach(record => {
      const timestamp = record.created_on ? moment(record.created_on).format('MMM D (H:mma)') : '';
      const timestamp_updated = record.updated_on ? moment(record.updated_on).format('MMM D (H:mma)') : '';
      const commentNode = this.newCommentNode(record.id, record.comment, JSON.parse(record.mwi_id_to_score_option_id), userRef.get(record.uid), record.is_removed, timestamp, timestamp_updated)
      if (record.parent_id){
        const parentNode = parentCommentRef.get(record.parent_id);
        if (parentNode){
          parentNode.sub.push(commentNode);
        }
      }
      else{
        this.currentResponse.commentTree.push(commentNode)
      }
      parentCommentRef.set(record.id, commentNode)
    })
  }
  private newCommentNode(id:number, comment:string, mwi_id_to_score_option_id, user:any, is_removed, timestamp:string='', timestamp_updated:string=''){
    return {
      id,
      comment,
      mwi_id_to_score_option_id,
      user,
      timestamp,
      timestamp_updated,
      is_removed,
      sub: [],
    }
  }
  async newThread(comment: string, mwi_id_to_score_option_id, response:any){
    if (!comment){
      return;
    }
    const commentNode = await this.newComment(response, comment, mwi_id_to_score_option_id)
    response.commentTree.push(commentNode);
  }
  newComment = async  (response:any, comment:string, mwi_id_to_score_option_id, parent_id?:number) => {
    const newRecord = await this.auth.apiCreate(this.routes.SCOR_LEAD_VALIDITY_COMMENTS, {
      marking_response_selections_id: response.mrsel_id,
      taqr_id: response.id, 
      sync_id: this.syncId,
      parent_id, 
      comment,
      mwi_id_to_score_option_id: JSON.stringify(mwi_id_to_score_option_id)
    })
    const currentTime = moment().format('MMM D (H:mma)')
    return this.newCommentNode(newRecord.id, comment, mwi_id_to_score_option_id, {id: this.auth.getUid(), first_name:'(Me)', last_name:''}, 0, currentTime, currentTime)
  }
  patchComment = (commentId:number, comment:string, mwi_id_to_score_option_id) => {
    return this.auth
    .apiPatch(this.routes.SCOR_LEAD_VALIDITY_COMMENTS, commentId, {
      comment,
      mwi_id_to_score_option_id: JSON.stringify(mwi_id_to_score_option_id)
    })
  }

  scrollToResponseProgress(){
    const el = document.getElementsByClassName('panel-score-left')[0];
    const unit = document.getElementsByClassName('response-row')[0];
    if (el && unit){
      const unitHeight = unit.clientHeight;
      el.scrollTo({behavior: 'smooth', top: unitHeight*this.getCurrentResponseIndex()});
    }
  }

  getMwiId(){
    return this.syncId
  }

  getCurrentResponseId(){
    return this.currentResponseId;
  }
  getCurrentResponseIndex(){
    if (!this.currentResponseIndex || isNaN(this.currentResponseIndex)){
      return 0;
    }
    return this.currentResponseIndex;
  //   let currentResponseIndex = -1;
  //   this.getCurrentResponses().forEach((responseId, responseIndex) => {
  //     if (responseId === this.getCurrentResponseId()){
  //       currentResponseIndex = responseIndex;
  //     }
  //   });
  //   return currentResponseIndex;
  }

  initFilters(){

    const scoreFilters = {}
    this.markingWindowItems.forEach(mwi => {
      scoreFilters[`score_${mwi.id}`] = {
          val: [FilterSpecialValues.ALL],
          caption: `Score ${mwi.scale_skill_code}`,
          options: mwi.scoreOptions,
          getProp:'score_option_id',
          filterProp:['mwi_id_to_score_option_id', mwi.id]
      }
    })

    this.filterParts = {
      testAttempt: {val: null,  caption:'Attempt ID', isUserInput: true, filterProp: ['test_attempt_id']},
      schlGroupId: {val: null,  caption:'School Code', isUserInput: true, filterProp: ['schl_foreign_id']},
      tag1: {val:[FilterSpecialValues.ALL],  caption:'Range', options: this.ranges, getProp:'tag1', filterProp: ['tag1']},
      ...scoreFilters,
      tag2: {val:[FilterSpecialValues.ALL],  caption:'Prop.', options: this.responseSetTypes, getProp:'id', filterProp: ['tag2']},
      setIncl: {val: [FilterSpecialValues.ALL],  caption:'Incl.', options: this.responseSets, getProp:'set_id', filterProp:null, isCustomMatchCheck: this.isResponseInSet, isCustomNullMatchCheck: this.responseInNoSets},
    }
  }

  /** Check if a response is included in some set */
  isResponseInSet(response, setId:number):boolean{
    const setInclusion = response?.inclusions?.[setId]
    return (setInclusion && !setInclusion.is_revoked)
  }

  responseInNoSets(response){
    return Object.values(response.inclusions).filter((i: any) => !i.is_revoked).length == 0
  }

  clearFilters(){
    if (this.filterParts){
      Object.keys(this.filterParts).forEach(filterId => this.filterParts[filterId].val = this.filterParts[filterId].isUserInput ? null : [FilterSpecialValues.ALL]);
    }
  }

  // temp: should be defined in array
  getFilterOptions() : FilterPart[] {
    if (!this.filterParts){
      return [];
    }
    return Object.values(this.filterParts)
  }

  loadPercent: string = "";

  async loadResponses(isForced:boolean = false){
    if ( isForced || (this.loadedSyncId !== this.syncId) || (this.loadedStageNum !== this.stageNum)){
      this.isNetworkBusy = true;
      this.isResponsesLoading = true;
      this.responses = [];
      const allResponses = [];
      let latestResponses = [];
      const records  = await this.auth.apiGet(this.routes.SCOR_LEAD_ITEM_STATS, this.markingWindowId);
      const totalResponseNum = records.find(record => record.sync_id == this.syncId)["num_stage_" + this.stageNum + "_pooled"]
      let finishedNum = 0;
      let filterTagIds = [];
      console.log(this.filterRejectedResponse)
      if(this.filterRejectedResponse) {
        for(let rangeCategory of this.rangeCategories){
          if(rangeCategory.id == RANGE_CATEGORIES.REJECTED) {
            for(let range of rangeCategory.ranges){
              filterTagIds.push(range.props.tag1)
            }
          }
        }
      }
      
      // Pull the raw records in intervals (to avoid timeout on pages with many responses)
      // (interval corresponds to the number of raw records pulled in the API but the number returned is likely lower since data is rearranged
      let offset = 0;
      do {
        latestResponses = await this.auth.apiFind(this.routes.SCOR_LEAD_VALIDITY_RESPONSES, {query:{
          sync_id: this.syncId,
          stage_num: this.stageNum,
          set_id: this.setId,
          filter_tag_ids: filterTagIds,
          offset,
          limit: MAX_RESPONSE_RAW_PULL_LIMIT
        }});
        // Either add record or merge with existing record (if info about the same response got split between different requests)
        latestResponses.forEach((latestResponse) => {
          const existingRecord = allResponses.find(r => r.id == latestResponse.id)
          if (!existingRecord){
            allResponses.push(latestResponse)
          } else {
            Object.keys(latestResponse.inclusions).forEach(setId => {
              existingRecord.inclusions[setId] = latestResponse.inclusions[setId]
            })
          }
        })
        offset += MAX_RESPONSE_RAW_PULL_LIMIT;
        finishedNum += latestResponses.length;
        this.loadPercent = (finishedNum / totalResponseNum * 100).toFixed(2)
      } while (latestResponses.length > 0)
      this.loadPercent = ""
      this.responses = _.shuffle(allResponses);

      this.responses
      .forEach(response => {
        response.isCombinationInvalid = isResponseCombinationInvalid(response.id, response.mwi_id_to_score_option_id, this.markingWindowItems)
        this.sanitizeResponseStats(response)
      });
      this.loadedResponses = this.responses;
      this.isNetworkBusy = false;
      this.isResponsesLoading = false;
    }
  }

  private sanitizeResponseStats = (response:any) => {
    response.inclusions = response.inclusions || {}
    if (response.tag2){
      response.tag2 = +response.tag2;
    }
  }

  validateSelectedResponseToFilter(){
    this.currentResponse = null;
    this.currentResponseIndex = -1;
    if (this.currentResponseId){
      this.responses.forEach((response, i) => {
        if (this.currentResponseId === response.id){
          this.currentResponse = response;
          this.currentResponseIndex = i;
        }
      })
    }
    if (!this.currentResponse){
      this.currentResponseId = undefined;
    }
  }

  isQuickFilterSelected(tag1Value: string){
    const allowedVals = this.filterParts?.tag1?.val
    return allowedVals && allowedVals.includes(tag1Value);
  }

  /** Count how many responses have the provided range value */
  getCountForRangefilter(tag1Value) : number {
    if (!this.filterParts) return;
    const tag1Filter = _.cloneDeep(this.filterParts.tag1)
    tag1Filter.val = [tag1Value]
    const responsesWithRange = this.loadedResponses.filter(response => {
      return this.isResponseMatchFilterOption(tag1Filter, response)
    })
    return responsesWithRange.length
  }

  /** Apply filter to a range tags and clear all other filters */
  applyRangeQuickFilter(tag1Value: string){
    Object.keys(this.filterParts).forEach(filterKey => {
      if (filterKey == "tag1"){
        this.filterParts[filterKey].val = [tag1Value]
      }
      else {
        this.filterParts[filterKey].val = this.filterParts[filterKey].isUserInput ? null : [FilterSpecialValues.ALL];
      }
    })
    this.updateSubFilter();
  }

  updateSubFilter(){
    const filterOptions = this.getFilterOptions();
    filterOptions.forEach(filterOption => {
      filterOption.isActive = false
      if (filterOption.val && filterOption.val.length){
        const allowedVals = filterOption.val;
        if (allowedVals.includes(FilterSpecialValues.ALL)){
          filterOption.isActive = false;
        }
        else{
          filterOption.isActive = true;
        }
      }
    });
    const responses = [];
    this.loadedResponses.forEach(response => {
      let isBadMatch = false;
      filterOptions.forEach(filterOption => {
        if (!filterOption.isActive) return;
        isBadMatch = !this.isResponseMatchFilterOption(filterOption, response)
      })
      if (!isBadMatch){
        responses.push(response);
      }
    });
    if (this.filterParts.schlGroupId.val && this.filterParts.schlGroupId.val.length) {
      // If attempt id provided, search for attempt, otherwise search for school group id
      if(!(this.filterParts.testAttempt.val && this.filterParts.testAttempt.val.length)){

        const searchBySchoolGroupId = this.loadedResponses.filter(response => this.isResponseMatchFilterOption(this.filterParts.schlGroupId, response))
        
        this.auth.apiFind(this.routes.SCOR_LEAD_VALIDITY_RESPONSES, {query:{
          sync_id: this.syncId,
          stage_num: this.stageNum,
          set_id: this.setId,
          schl_foreign_id: this.filterParts.schlGroupId.val || null,
          is_search: true
          }}).then((res)=>{
            if(res && res.length > searchBySchoolGroupId.length){
              this.loginGuard.confirmationReqActivate({
                caption: this.lang.tra('lbl_abed_standard_confirming_school_group_id_lookup'),
                confirm: () => {
                  this.poolAttempt(+this.filterParts.schlGroupId.val)
                }
              })
            }
          })
      }
    }
    if(responses.length == 0) { 
      if((this.filterParts.testAttempt.val && this.filterParts.testAttempt.val.length)){
        // search attempt on list again without other filters
        const searchByAttemptId = this.loadedResponses.find(response => this.isResponseMatchFilterOption(this.filterParts.testAttempt, response))
        if(!searchByAttemptId){
          this.auth.apiFind(this.routes.SCOR_LEAD_VALIDITY_RESPONSES, {query:{
          sync_id: this.syncId,
          stage_num: this.stageNum,
          set_id: this.setId,
          test_attempt_id: this.filterParts.testAttempt.val || null,
          is_search: true
          }}).then((res)=>{
            if(res && res.length > 0){
              this.loginGuard.confirmationReqActivate({
                caption: this.lang.tra('lbl_abed_standard_confirming_attempt_pool'),
                confirm: () => {
                  this.poolAttempt()
                }
              })
            }
          })
        }
      }
    }
    this.responses = responses;
    this.validateSelectedResponseToFilter();
    this.isFilterEditing = false;
  }

  poolAttempt(schl_foreign_id?: number) {
    this.auth.apiPatch(this.routes.SCOR_LEAD_ITEM_STATS, this.markingWindowId, {
        sync_ids: [this.syncId],
        test_attempt_id: schl_foreign_id? null: this.filterParts.testAttempt.val,
        schl_foreign_id: schl_foreign_id? schl_foreign_id: null,
      }).then((res)=>{
        const isForced = true;
        this.loadResponses(isForced);
      }).catch((err)=>{
        this.loginGuard.quickPopup(err)
      })
  }

  isResponseMatchFilterOption(filterOption, response){
    // Either use the custom function, or compare the property in the response directly depending on filter configurations
    if (filterOption.isCustomMatchCheck){
      for (let filterVal of filterOption.val){
        if (filterVal == FilterSpecialValues.NO_VALUE && filterOption.isCustomNullMatchCheck(response)){
          return true
        }
        else if (filterVal !== FilterSpecialValues.NO_VALUE && filterOption.isCustomMatchCheck(response, filterVal)){
          return true;
        }
      }
      return false;
    }
    else if (filterOption.filterProp) {
      const targetValue = _.get(response, filterOption.filterProp);
      if (targetValue === null || targetValue === undefined){
        if (filterOption.val.includes(FilterSpecialValues.NO_VALUE)) {
          return true
        } else {
          return false;
        }
      }
      else {
        if (filterOption.isUserInput && filterOption.val != targetValue) {
          return false;
        }
        if (!filterOption.isUserInput && filterOption.val.indexOf(targetValue) === -1){
          return false;
        }
      }
    }
    return true;
  }

  scrollIntoView(){
    setTimeout(()=>{
      const el = document.getElementsByClassName('is-response-selected')[0];
      if (el){
        el.scrollIntoView({behavior: 'smooth'});
      }
    }, 300)
  }

  async updateFilter(){
    await this.loadResponses();
    this.validateSelectedResponseToFilter();
    await this.loadComments();
    this.updateSubFilter();
    this.scrollIntoView()
  }

  async assignSelectionData(data:any, response=this.currentResponse, syncId = this.syncId){
    const payload = {
      ... data,
      sync_batches_to_wmi_id: syncId,
    }
    try {
      const {new_rationale_hash} = await this.auth.apiPatch(this.routes.SCOR_LEAD_VALIDITY_RESPONSES, response.id, payload);
      response.rationale_hash = new_rationale_hash;
    }
    catch(e){
      if (e.message === 'POSSIBLE_OVERRIDE'){
        if (confirm('It seems that you are about to override a recently made update on this response by another user. Are you sure that you wish to override the other change? To compare the values, you can open this response again in another tab. Press "OK" to overwrite the recent change.') ){
          const {new_rationale_hash} = await this.auth.apiPatch(this.routes.SCOR_LEAD_VALIDITY_RESPONSES, response.id, {...payload, isForceOverride:true});
          response.rationale_hash = new_rationale_hash;
        }
        else{
          throw new Error()
        }
      }
      else{
        alert('Unknown error');
        throw new Error()
      }
    }
  }

  async assignTag(tag1:string){
    if (this.currentResponse.tag1 === tag1){
      tag1 = null;
    }
    await this.assignSelectionData({tag1})
    this.currentResponse.tag1 = tag1;
  }


  async assignScore(score_option_id:number, mwi_id: number, response = this.currentResponse, syncIdOverride?: number){

    const isRemovingScore = response.mwi_id_to_score_option_id[mwi_id] == score_option_id
    
    if (isRemovingScore){
      score_option_id = undefined;
    }

    const newMwiIdToScoreOptionId = {...response.mwi_id_to_score_option_id, [mwi_id]: score_option_id}
    const patchData = {
      mwi_id_to_score_option_id: JSON.stringify(newMwiIdToScoreOptionId)
    }
    this.assignSelectionData(patchData, response, syncIdOverride)
    .then(() => {
      response.mwi_id_to_score_option_id = newMwiIdToScoreOptionId
      response.isCombinationInvalid = isResponseCombinationInvalid(response, newMwiIdToScoreOptionId, this.markingWindowItems);
    }).catch(err => {
      this.loginGuard.quickPopup(`Error: ${err.message}`)
    })
  }

  // /**
  //  * Mark on unmark the score as being an expert score for some response
  //  * @param is_expert_score - 1 if expert score, 0 if not
  //  * @param reqConfirm - whether confirmation click is required (true/false)
  //  * @param response - pass the response to change this for, otherwise uses currentResponse
  //  */
  // async changeIsExpertScore(is_expert_score:number, response=this.currentResponse, reqConfirm:boolean=true){

  //   if (is_expert_score == 0 && this.hasInclusions(response)){
  //     const inclBlockMsg = 'lbl_block_unmark_expert_msg'
  //     return this.loginGuard.quickPopup(inclBlockMsg)
  //   }
  //   const doChange = () => {
  //     this.assignSelectionData({is_expert_score}, response)
  //     .then(() => {
  //       this.currentResponse.is_expert_score = !!is_expert_score
  //     })
  //   }

  //   // Change after confirmation if required, or just proceed to change
  //   if (reqConfirm) {
  //     const confirmMsg = is_expert_score ? 'lbl_confirm_mark_expert_scor_msg' : 'lbl_confirm_unmark_expert_scor_msg'
  //     this.loginGuard.confirmationReqActivate({
  //       caption: confirmMsg,
  //       confirm: () => {
  //         doChange()
  //       }
  //     })
  //   } else {
  //     doChange();
  //   }
  // }

  async assignProposed(tag2:string){
    if (this.currentResponse.tag2 === tag2){
      tag2 = null;
    }
    await this.assignSelectionData({tag2})
    this.currentResponse.tag2 = tag2;
  }
  async assignInclusion(is_included:number, responseToChange=this.currentResponse, set_id=+this.targetResponseSet){

    let response_set_order = 0;
    if (is_included==1){
      this.currentResponseSet.forEach(response => {
        //added check to see if it is in the set, since currentResponseSet now includes all possible inclusions.
        if (!!response.inclusions[set_id] && response_set_order < response.inclusions[set_id].response_set_order ){
          response_set_order = response.inclusions[set_id].response_set_order
        }
      })
      response_set_order = +response_set_order + 1


      responseToChange = await this.auth.apiCreate(this.routes.SCOR_LEAD_VALIDITY_RESPONSES, {
        sync_batches_to_wmi_id: this.syncId,
        set_id,
        selection_id:responseToChange.mrsel_id,
        taqr_id:responseToChange.id,
        response_set_num:this.setIDToSetType(set_id),
        response_set_order,
      }, {
        query: {
          taqr_id:responseToChange.id,
          set_id,
          sync_id:this.syncId
        }
      });

      // If the response is now included in a set, the score should be set to expert score if it wasn't already
      // if (!responseToChange.is_expert_score) {
      //   await this.changeIsExpertScore(1, responseToChange, false)
      // }

    }
    else {
      responseToChange = await this.auth.apiRemove(this.routes.SCOR_LEAD_VALIDITY_RESPONSES, null, {
        query: {
          taqr_id:responseToChange.id,
          set_id,
          sync_id:this.syncId
        }
      });
    }

    for(let i = 0; i < this.loadedResponses.length; i++) {
      if(this.loadedResponses[i].id == responseToChange.id) {
        this.loadedResponses[i] = {...responseToChange}
      }
    }

    if(responseToChange.id == this.currentResponse.id) {
      this.currentResponse = responseToChange;
    }

    // Also update the response in the full list to the latest info (to modify inclusion column)
    let responseInFullListIndex = this.responses.findIndex(r => r.id == responseToChange.id)
    if (responseInFullListIndex !== -1){
      this.responses[responseInFullListIndex] = responseToChange
    }

    // await this.assignSelectionData({is_included, set_id, response_set_order})


    this.onTargetResponseSetChange();

    // If an item was taken out of the set, refresh the order of the remaining items
    if (!is_included) this.refreshCurrResponseSetOrder();
  }
  async saveRationaleEdit(rationale:string){
    const rationale_hash = this.currentResponse.rationale_hash
    await this.assignSelectionData({rationale, rationale_hash})
    this.currentResponse.rationale = rationale;
    this.isEditingRationale = false;
  }

  excludeItem(){
    alert('not available')
  }

  recentlyCreated:{taqr_id: string, scoreCaption:string|number}[]
  newResponseTargetSet;
  startHistorical(){
    this.isEnteringHistorical = true;
    this.recentlyCreated = [];
    this.newResponse = {};
    this.loginGuard.quickPopup('You are entering a mode to enter historical exemplars. You can either upload files or respond to the item the way the student would.')
  }
  endHistorical(){
    this.loginGuard.confirmationReqActivate({
      caption: 'Are you done creating all of your historical exemplars? Anything that you have not saved yet will be lost.',
      confirm: () => {
        this.loadResponses(true);
        this.isEnteringHistorical = false;
      }
    })
  }
  async saveHitorical(){
    this.newResponse.isSaving = true;
    
    try{ 
      const {responseRaw, rationale, score_option_id, scan_url} = this.newResponse;
      const record = await this.auth.apiCreate('public/scor-lead/validity/historical-response', {
        marking_window_id: this.markingWindowId, 
        sync_id: this.syncId,
        response_raw: JSON.stringify(responseRaw), 
        scan_url: scan_url,
        rationale: rationale,
        score_option_id: score_option_id,
      })
      const {taqr_id, selection_id} = record;
      this.recentlyCreated.push({
        taqr_id,
        scoreCaption: this.getScoreCaption(score_option_id)
      })
      if (this.newResponseTargetSet){
        const set_id = +this.newResponseTargetSet;
        await this.auth.apiCreate(this.routes.SCOR_LEAD_VALIDITY_RESPONSES, {
          sync_id: this.syncId,
          set_id,
          selection_id,
          taqr_id,
          response_set_num:this.setIDToSetType(set_id),
          response_set_order: this.recentlyCreated.length,
        }, {
          query: {
            taqr_id,
            set_id,
            sync_id:this.syncId
          }
        });
      }

      this.newResponse = {};

      this.newResponse.isSaving = false;
    }
    catch (e){
      this.loginGuard.quickPopup('Could not save exemplar. Please make sure you are logged in and try again, and then contact support.')
      this.newResponse.isSaving = false;
    }
    
  }

  getScoreCaption(score_option_id:number){
    for (let scoreOption of this.scoreOptions){
      if (score_option_id === scoreOption.props.score_option_id){
        return scoreOption.caption;
      }
    }
  }

  isSavingSetOrder:boolean;
  /**
   * After drag & drop of items in a set, update the list and refresh the order
   * @param arr target array in which to move the item
   * @param event event emitted when the draggable is dropped
   */
  setOrderDrop(arr:any, event: CdkDragDrop<string[]>) {
    moveItemInArray(arr, event.previousIndex, event.currentIndex);
    this.refreshCurrResponseSetOrder()
  }

  /** In the set currently in focus, renumber the set order data to match the current order of the displayed responses where necessary, save to db */
  async refreshCurrResponseSetOrder(){
    this.isSavingSetOrder = true;
    let counter = 1;
    let payload = [];
    this.currentResponseSet.forEach(response => {
      if(!!response.inclusions[this.targetResponseSet]) {
        // If the response already has the expected order, don't need to update
        if (response.inclusions[this.targetResponseSet].response_set_order == counter) return counter++
        response.inclusions[this.targetResponseSet].response_set_order = counter;
        payload.push({
          id: response.inclusions[this.targetResponseSet].mrset_id,
          response_set_order: response.inclusions[this.targetResponseSet].response_set_order,
        });
        counter ++;
      } 
    })
    if (payload.length){
      await this.auth
      .apiUpdate(this.routes.SCOR_LEAD_VALIDITY_RESPONSES, this.syncId, payload)
      .catch(e => {
        alert('Could not save change to sort order. '+e.message)
      })
    }
    this.isSavingSetOrder = false;
  }

  hasInclusions(response) {
    if (response && response.inclusions){
      for(let inclusion of <any[]>Object.values(response.inclusions)) {
        if(!inclusion.is_revoked) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * @param response The response object
   * @returns All the sets in which the response is included and not revoked from
   */
  getResponseInclusions(response){
    if (!response || !response.inclusions) return;
    const validInclusions = Object.values(response.inclusions).filter((i:any) => !i.is_revoked)
    return validInclusions
  }

  /**
   * @param response The response object
   * @returns String of comma-separated list of short names of all the sets the response is in
   */
  renderResponseInclusionSlugList(response):string{
    const inclSetIds = this.getResponseInclusions(response).map((i:any) => i.set_id).filter(setId => setId)
    const  inclSetString = inclSetIds.map(setId => this.renderResponseSetSlug(setId)).join(", ")
    return inclSetString;
  }

  firstInclusion(response):any {
    if (response && response.inclusions){
      for(let inclusion of <any[]>Object.values(response.inclusions)) {
        if(!inclusion.is_revoked) {
          return inclusion
        }
      }
    }
    
    return null;
  }

  setIDToSetType(set_id) {
    return this.responseSetRef.get(set_id).props.response_set_num;
  }

  setTypeToSetID(type) {
    for(let set of this.responseSets) {
      if(set.props.response_set_num == type) return set.props.set_id
    }
  }

  clickedCheckbox(event, response, set) {
    this.assignInclusion(+event, response, set.props.set_id);
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }

  toggleCommentIncludeSuggestedScore($event){
    const includeSuggestedScore = $event.checked;
    if (!includeSuggestedScore){
      this.cmc().mwi_id_to_score_option_id = {}
    }
  }

  launchNewCommentModal(){
    const config = {
      comment: null,
      mwi_id_to_score_option_id: {},
      includeSuggestedScore: false
    };
    this.pageModal.newModal({
      type: RangeFindingModalType.NEW_COMMENT,
      config,
      confirmationCaption: 'btn_save',
      cancel: () => {
      },
      finish: (config) => {
        this.newThread(config.comment, config.mwi_id_to_score_option_id, this.currentResponse)
      }
    })
  }

  async launchCommentReplyModal(parentComment){
    const config = {
      parentComment: parentComment.comment,
      parent_mwi_id_to_score_option_id: parentComment.mwi_id_to_score_option_id,
      comment: null,
      mwi_id_to_score_option_id: {},
      includeSuggestedScore: false
    };
    this.pageModal.newModal({
      type: RangeFindingModalType.COMMENT_REPLY,
      config,
      confirmationCaption: 'btn_save',
      cancel: () => {
      },
      finish: (config) => {
        this.newComment(this.currentResponse, config.comment, config.mwi_id_to_score_option_id, parentComment.id)
        .then((newComment) => {
          parentComment.sub.push(newComment)
        })
      }
    })
  }

  isSomeScoreGiven(mwi_id_to_score_option_id){
    return mwi_id_to_score_option_id && Object.values(mwi_id_to_score_option_id).length
  }

  async launchCommentEditModal(targetComment){
    const config = {
      comment: targetComment.comment,
      prevComment: targetComment.comment,
      mwi_id_to_score_option_id: {...targetComment.mwi_id_to_score_option_id},
      prev_mwi_id_to_score_option_id: {...targetComment.mwi_id_to_score_option_id},
      includeSuggestedScore: this.isSomeScoreGiven(targetComment.mwi_id_to_score_option_id)
    };
    this.pageModal.newModal({
      type: RangeFindingModalType.COMMENT_EDIT,
      config,
      confirmationCaption: 'btn_save',
      cancel: () => {
      },
      finish: (config) => {
        this.patchComment(targetComment.id, config.comment, config.mwi_id_to_score_option_id)
        .then(() => {
          targetComment.comment = config.comment;
          targetComment.mwi_id_to_score_option_id = config.mwi_id_to_score_option_id,
          targetComment.timestamp_updated = moment().format('MMM D (H:mma)')
        })
      }
    })
  }

  assignScoreInComment(score_option_id: number, mwi_id: number){
    const {mwi_id_to_score_option_id} = this.cmc();
    const isRemovingScore = mwi_id_to_score_option_id[mwi_id] == score_option_id
    if (isRemovingScore){
      score_option_id = undefined;
    }
    mwi_id_to_score_option_id[mwi_id] = score_option_id
  }

  deleteComment(targetComment){
    this.loginGuard.confirmationReqActivate({
      caption: `Are you sure that you want to delete this comment?`,
      btnCancelCaption: 'lbl_no',
      btnProceedCaption: 'lbl_yes',
      confirm: () => {
        this.auth.apiRemove(this.routes.SCOR_LEAD_VALIDITY_COMMENTS, targetComment.id)
        .then(() => {
          targetComment.is_removed = 1;
        })
      }
    })
  }

  isCommentSaveDisabled(){
    const {comment, mwi_id_to_score_option_id} = this.cmc();
    if (!comment) {
      return true;
    }
    const isScoreInvalid = isResponseCombinationInvalid(this.currentResponse, mwi_id_to_score_option_id, this.markingWindowItems)
    if (isScoreInvalid){
      return true;
    }
    if (this.cModal().type == RangeFindingModalType.COMMENT_EDIT){
      const {prevComment, prev_mwi_id_to_score_option_id} = this.cmc();
      const isCommentSame = comment == prevComment;
      const isSuggestedScoreSame = JSON.stringify(mwi_id_to_score_option_id) == JSON.stringify(prev_mwi_id_to_score_option_id)
      if (isCommentSame && isSuggestedScoreSame) {
        return true;
      }
    }
    return false;
  }

  isSomeScaleScored(response){
    return this.markingWindowItems.some(mwi => response.mwi_id_to_score_option_id[mwi.id])
  }

  areAllScalesScored(response){
    return this.markingWindowItems.every(mwi => response.mwi_id_to_score_option_id[mwi.id])
  }


  /** Download responses currently filtered in view as a docx document */
  async loadResponsesDocx(isIncludeRationale: boolean = false){
    this.isPrintDocProcessing = true;
    try {
      if (!this.responses.length){
        throw new Error('No responses in view')
      }
      if (this.responses.length > MAX_PRINT_CAPACITY){
        throw new Error(`Cannot print more then ${MAX_PRINT_CAPACITY} responses, use filters to narrow down to a smaller sample.`)
      }
  
      // Fetch test question item config
      const {itemDisplays} = await this.auth.apiGet(this.routes.SCOR_SCOR_BATCH_ITEM_DISPLAY,
        this.getMwiId(), { query: { 
          responseId: this.responses[0].id, 
          isSingleItem: 1
        }
      });
      // (There's always only one question in the standard Confirming view)
      const targetItemDisplay: any = Object.values(itemDisplays)[0];
      const questionConfig = JSON.parse(targetItemDisplay.config);

      // Get the student response data
      const studentResponses = [];
      for (let r of this.responses) {
        const taqr_id = r.id;
        const query = { include_scan_base64: 1 }
        const responseData = await this.auth.apiGet(this.routes.SCOR_LEAD_VALIDITY_RESPONSES, taqr_id, {query})
        const response_raw = JSON.parse(responseData.response_raw)
        const scan_base64 = responseData.scan_base64
        studentResponses.push({...r, response_raw, scan_base64})
      }

      // Rearrange data for document
      const responseDataForPrint = await arrangeResponseDataForPrint(questionConfig, targetItemDisplay.lang, studentResponses, isIncludeRationale)

      // Create a docx document to be downloaded
      const document = getPrintDocument(responseDataForPrint, this.itemCaption, this.itemGroupName)

      // Download document
      Packer.toBlob(document).then((blob) => {
        saveAs(blob, "studentResponses.docx");
      });

    } catch (e){
      console.error(e);
      this.loginGuard.quickPopup(`Error: ${e.message}`)
    }
    this.isPrintDocProcessing = false;
  }

  onLoadResponsesDocxClick(){
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('lbl_abed_download_for_print_annotation_confirm'),
      btnProceedConfig: {
        caption: this.lang.tra('lbl_yes'),
      },
      btnCancelConfig: {
        caption: this.lang.tra('lbl_no'),
      },
      confirm: () => {
        this.loadResponsesDocx(true)
      },
      close: () => {
        this.loadResponsesDocx(false)
      }
    })
  }

  isABED() {
    return this.whitelabel.getSiteFlag('IS_ABED');
  }

  onClickAssignExpertScore(){
    if (this.syncItemsInSameGroup.length > 1){
      this.launchMultiSyncExpertScoreModal();
    } else {
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('lbl_rf_expert_score_confirm_msg'),
        btnProceedConfig: {caption: 'lbl_yes'},
        btnCancelConfig: {caption: 'lbl_no'},
        confirm: () => {
          const syncItem = this.syncItemsInSameGroup[0]
          const data = [{
            taqr_id: this.currentResponse.id,
            group_to_mwi_id: syncItem.group_to_mwi_id,
            sync_id: syncItem.id
          }]
          this.auth.apiCreate(this.routes.SCOR_LEAD_VALIDITY_EXPERT_SCORE, data, {})
          .then(() => {
            this.currentResponse.is_expert_score = true
          })
        }
      })
    }
  }

  onClickUnassignExpertScore(){
    const confirmaitionMsg = this.syncItemsInSameGroup.length > 1 ? 'lbl_rf_remove_expert_score_mutli_confirm_msg' : 'lbl_rf_remove_expert_score_confirm_msg'
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra(confirmaitionMsg),
      btnProceedConfig: {caption: 'lbl_yes'},
      btnCancelConfig: {caption: 'lbl_no'},
      confirm: () => {
        this.unassignExpertScore();
      }
    })
  }

  async launchMultiSyncExpertScoreModal(){
    const {test_attempt_id} = this.currentResponse;
    const responseTabs = [];
    for (const syncItem of this.syncItemsInSameGroup){
      if (syncItem.id == this.syncId){
        responseTabs.push({
          ...syncItem,
          response: this.currentResponse,
          markingWindowItems: this.markingWindowItems
        })
      } else {
        const responses = await this.auth.apiFind(this.routes.SCOR_LEAD_VALIDITY_RESPONSES, {query:{
          sync_id: syncItem.id,
          test_attempt_id
        }});
        const {items} =  await this.auth.apiGet('public/scor-lead/validity/item', syncItem.id);
        const markingWindowItems = items.map(item => {
          const { id, scale_description, scale_skill_code, scoreOptions, flagOptions, itemRules} = item;
          const scaleName = JSON.parse(scale_description)[this.lang.c()]
          return {id, scaleName, scale_skill_code, scoreOptions, flagOptions, itemRules}
        });
        markingWindowItems.forEach(mwi => {
          mwi.scoreOptions = mwi.scoreOptions.map(record => {
            const score_option_id = record.id;
            const scoreOption = {
              props:{score_option_id}, 
              slug: this.lang.tra(record.slug || record.value), 
              caption: this.lang.tra(record.caption || record.slug || record.value),
            };
            return scoreOption;
          })
        })
        if (responses.length){
          const response = responses[0]
          response.isCombinationInvalid = isResponseCombinationInvalid(response.id, response.mwi_id_to_score_option_id, markingWindowItems)
          responseTabs.push({
            ...syncItem,
            markingWindowItems,
            response,
          })
        }
      }
    }
    const config = {
      test_attempt_id,
      responseTabs,
      selectedResponseTab: responseTabs[0]
    };
    this.pageModal.newModal({
      type: RangeFindingModalType.MULTI_EXPERT_SCORE,
      config,
      confirmationCaption: 'lbl_rf_expert_score_multi_save_btn',
      cancel: () => {
      },
      finish: (config) => {
        const data = responseTabs.map(t => {
          return {
            taqr_id: t.response.id,
            group_to_mwi_id: t.group_to_mwi_id,
            sync_id: t.id
          }
        })
        this.auth.apiCreate(this.routes.SCOR_LEAD_VALIDITY_EXPERT_SCORE, data, {})
        .then(() => {
          this.currentResponse.is_expert_score = true;
          this.pageModal.closeModal();
        })
      }
    })
  }

  async unassignExpertScore(){
    const {test_attempt_id} = this.currentResponse;
    const data = [];
    for (const syncItem of this.syncItemsInSameGroup){
      if (syncItem.id == this.syncId){
        data.push({
          sync_id: syncItem.id,
          taqr_id: this.currentResponse.id
        })
      } else {
        const responses = await this.auth.apiFind(this.routes.SCOR_LEAD_VALIDITY_RESPONSES, {query:{
          sync_id: syncItem.id,
          test_attempt_id
        }});
        if (responses.length){
          const response = responses[0]
          data.push({
            sync_id: syncItem.id,
            taqr_id: response.id
          })
        }
      }
    }
    this.auth.apiPatch(this.routes.SCOR_LEAD_VALIDITY_EXPERT_SCORE, 1, data, {})
    .then(() => {
      this.currentResponse.is_expert_score = false;
    })
  }

  isEditDisabledExpertScore(){
    for (const responseTab of this.cmc().responseTabs){
      const {markingWindowItems} = responseTab;
      const {isResponseBatchCombinationInvalid, mwi_id_to_score_option_id} = responseTab.response
      const isEveryScaleMarked = markingWindowItems.every(mwi => !!mwi_id_to_score_option_id[mwi.id])
      if (!isEveryScaleMarked || isResponseBatchCombinationInvalid) return true;
    }
  }
  toggleFilterRejectResponse(){
    const isForced = true;
    this.loadResponses(isForced);
  }
}
