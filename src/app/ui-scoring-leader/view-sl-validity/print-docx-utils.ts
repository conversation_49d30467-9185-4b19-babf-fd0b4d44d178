import { Document, Paragraph, TextRun, PageBreak, HeadingLevel, AlignmentType, Table, TableRow, TableCell, BorderStyle, WidthType, FileChild, Footer, ImageRun } from 'docx';
import { deepFind } from '../../core/util/obj';
import { SafeResourceUrl, DomSanitizer } from '@angular/platform-browser';

interface IResponseDataForPrint {
  taqr_id: number,
  test_attempt_id: number,
  entries: {
    caption: string,
    str: string,
  }[],
  scan_base64?: string,
  rationale?: string
}

const defaultParagraphSpacing = {
  after: 150,
  before: 150,
  line: 400,  
}

const cellMarginSettings = {
  top: 100,  
  bottom: 100,
  left: 100,
  right: 100
}

const domToDocxAlignment = {
  "center": AlignmentType.CENTER,
  "right": AlignmentType.RIGHT,
  "justify": AlignmentType.JUSTIFIED,
}

const hTagToHeaderLevel = {
  "H1": HeadingLevel.HEADING_1,
  "H2": HeadingLevel.HEADING_2,
  "H3": HeadingLevel.HEADING_3,
  "H4": HeadingLevel.HEADING_4,
}

/** Render possible HTML elements within the student's response */
const getSectionChildrenByNode = (node: HTMLElement): FileChild[]=> {
  // Paragraph
  if (["P", "H1", "H2", "H3", "H4"].includes(node.nodeName)) {
    const paragraph =  new Paragraph({
      children: getStyledTextRunList(node),
      alignment: domToDocxAlignment[node.style.textAlign],
      spacing: defaultParagraphSpacing,
      heading: hTagToHeaderLevel[node.nodeName]
    })
    return [paragraph]
  } 
  // Unordered list
  else if (node.nodeName === "UL") {
    const listElems = []
    node.childNodes.forEach((node: HTMLElement) => {
      if (node.nodeName === "LI") {
        listElems.push(
          new Paragraph({
            alignment: domToDocxAlignment[node.style.textAlign],
            children: getStyledTextRunList(node),
            bullet: {
              level: 0,
            },
            spacing: defaultParagraphSpacing,
          })
        );
      }
    });
    return listElems;
  } 
  // Oredered list
  else if (node.nodeName === "OL") {
    const listElems = []
    node.childNodes.forEach((node: HTMLElement) => {
      if (node.nodeName === "LI") {
        listElems.push(
          new Paragraph({
            alignment: domToDocxAlignment[node.style.textAlign],
            children: getStyledTextRunList(node),
            numbering: {
              reference: "ordered-list",
              level: 0,
            },
            spacing: defaultParagraphSpacing,
          })
        );
      }
    });
    return listElems;
  } 
  // Table
  else if (node.nodeName === "TABLE") {
    const rowList = []
    node.childNodes.forEach((node: HTMLElement) => {
      if (node.nodeName == "TBODY") {
        node.childNodes.forEach((node: HTMLElement) => {
          if (node.nodeName == "TR") {
            const cellList = []
            node.childNodes.forEach((node: HTMLElement) => {
              if (node.nodeName == "TD") {
                cellList.push(
                  new TableCell({
                    margins: {
                      top: 100,  
                      bottom: 100,
                      left: 100,
                      right: 100
                    },
                    children: [new Paragraph({
                      children: getStyledTextRunList(node),
                      spacing: defaultParagraphSpacing,
                    })],
                  })
                )
              }
            })
            rowList.push(
              new TableRow({
                children: cellList,
              })
            )
          }
        })
      }
    });
    const table = new Table({
      rows: rowList,
    });
    return [table]

  } 
  // Figure - render sub-nodes inside (e.g. table inside figure)
  else if (node.nodeName === "FIGURE") {
    const subElements = []
    node.childNodes.forEach((node: HTMLElement) => {
      getSectionChildrenByNode(node).forEach(elem => subElements.push(elem))
    })
    return subElements;
  } else {
    const paragraph =  new Paragraph({
      children: getStyledTextRunList(node),
      spacing: defaultParagraphSpacing,
    })
    return [paragraph]
  }
}

/** Transform string of the html of the student response to a list in docx library format */
export const htmlStringToSectionChildren = (input: string): FileChild[] => {
  const parser = new DOMParser();
  const dom = parser.parseFromString(`<body>${input}</body>`, 'text/html');

  const sectionChildren = [];
  dom.getElementsByTagName("body")[0].childNodes.forEach((node: HTMLElement) => {
    getSectionChildrenByNode(node).forEach(elem => sectionChildren.push(elem));
  });
  return sectionChildren;
};


/** Create a docx document with student responses by arranging information about the response and transforming the html string of the student answer to docx library format */
export const getPrintDocument = (responseDataList, itemCaption: string, itemGroupName: string) : Document => {
  const sections = [];

  // Add a title as the first page
  const titleCaption = new Paragraph({
    children: [
      new TextRun({
        text: itemCaption,
        size: 48,
        bold: true,
      }),
    ],
    alignment: AlignmentType.CENTER,
    spacing: {
      before: 300,
    },
  });
  const titleGroupName = new Paragraph({
    children: [
      new TextRun({
        text: itemGroupName,
        size: 36,
        bold: true,
      }),
    ],
    alignment: AlignmentType.CENTER,
  });

  const pageBreakParagraph = new Paragraph({
    children: [
      new PageBreak()
    ],
  })

  sections.push({
    children: [titleCaption, titleGroupName, pageBreakParagraph],
    footers: {
      default: getFooter(itemCaption, itemGroupName)
    }
  })

  // Render every student response
  responseDataList.forEach((responseData, index) => {

    const sectionChildren = []
    const {taqr_id, test_attempt_id, entries, scan_base64, rationale} = responseData;

    // Place the response and attempt Id in a table on top of the page
    const responseIdentityTable = getResponseIdentityTable(taqr_id, test_attempt_id)
    sectionChildren.push(responseIdentityTable)

    // For the student response and planning (if available) entries, put the caption as a heading and render the content under 
    for (let entry of entries){
      const {caption, str} = entry;
      const captionPragraph = new Paragraph({
        children: [
          new TextRun({
              text: caption,
              size: 32, 
              bold: true,
              shading: {fill: 'D9D9D9'},
          }),
        ],
        spacing: {
          before: 300,
          after: 300
        },
      })
      sectionChildren.push(captionPragraph)
      try {
        const content = htmlStringToSectionChildren(str)
        content.forEach(p => sectionChildren.push(p))
      } 
      // If this doesn't handle a particular student response, flag the error on the page but still download the document
      catch (e){
        console.error(e)
        const errorMessage = new Paragraph({
          children: [
            new TextRun({
                text: '[Technical error: could not render content]',
                size: 32, 
                bold: true,
                color: 'ff0000',
            }),
          ],
        })
        sectionChildren.push(errorMessage)
      }
    }

    try {
      if (scan_base64){
        const scanRendered = new Paragraph({
          children: [
            new ImageRun({
              type: 'jpg',
              data: base64ToArrayBuffer(scan_base64),
              transformation: {
                width: 600,
                height: 620,
              },
            }),
          ],
        })
        sectionChildren.push(scanRendered)
      }
    } catch (e) {
      const errorMessage = new Paragraph({
        children: [
          new TextRun({
              text: '[Technical error: could not render image]',
              size: 32, 
              bold: true,
              color: 'ff0000',
          }),
        ],
      })
      sectionChildren.push(errorMessage)
    }

    if (rationale !== undefined){
      const rationaleCaption = new Paragraph({
        children: [
          new TextRun({
              text: "Annotation:",
              size: 32, 
              bold: true,
              shading: {fill: 'D9D9D9'},
          }),
        ],
        spacing: {
          before: 300,
          after: 300
        },
      })
      const rationaleText =  new Paragraph({
        children: [
          new TextRun({
              text: rationale,
          }),
        ],
      })
      sectionChildren.push(rationaleCaption)
      sectionChildren.push(rationaleText)
    }

    // If it's not the last response, skip to new page to start next response
    if (index !== responseDataList.length - 1) {
      sectionChildren.push(pageBreakParagraph)
    }

    sections.push({
      children: sectionChildren,
      footers: {
        default: getFooter(itemCaption, itemGroupName, responseData)
      }
    })
  });

  const document = new Document({
    numbering: {
      config: [
        {
          reference: "ordered-list",
          levels: [
            {
              level: 0,
              format: "decimal",
              text: "%1.",
              alignment: AlignmentType.START,
            },
          ],
        },
      ],
    },
    styles: {
      default: {
        document: {
          run: {
            size: 24
          }
        }
      },
    },
    sections
  });

  return document;

}

/** Return a footer with the assignment info on the left, and the test attempt ID on the right (unless it's the title page) */
const getFooter = (itemCaption, itemGroupName, responseData?) => {

  const invisibleBorders = {
    top: { style: BorderStyle.NONE, size: 0, color: "FFFFFF" },
    bottom: { style: BorderStyle.NONE, size: 0, color: "FFFFFF" },
    left: { style: BorderStyle.NONE, size: 0, color: "FFFFFF" },
    right: { style: BorderStyle.NONE, size: 0, color: "FFFFFF" },
  }

  const footerCells = [];
  const footerTitleCell = new TableCell({
    width: { size: 50, type: WidthType.PERCENTAGE },
    children: [
      new Paragraph({
        alignment: AlignmentType.LEFT,
        children: [new TextRun({ text: itemCaption })],
      }),
      new Paragraph({
        alignment: AlignmentType.LEFT,
        children: [new TextRun({ text: itemGroupName })],
      }),
    ],
    borders: invisibleBorders
  });
  footerCells.push(footerTitleCell)

  if (responseData){
    const {test_attempt_id} = responseData;
    const footerAttemptCell  = new TableCell({
      width: { size: 50, type: WidthType.PERCENTAGE },
      children: [
        new Paragraph({
          alignment: AlignmentType.RIGHT,
          children: [
            new TextRun({
              text: `Attempt ID: `,
              bold: true,
            }),
            new TextRun({
              text: `${test_attempt_id}`,
              size: 24,
            }),
          ],
        }),
      ],
      borders: invisibleBorders
    })
    footerCells.push(footerAttemptCell)
  }
  const footerTable = new Table({
    rows: [
      new TableRow({
        children: footerCells
      }),
    ],
    width: { size: 100, type: WidthType.PERCENTAGE },
    borders: invisibleBorders
  });
  const footer = new Footer({
    children: [footerTable]
  })
  return footer;
}

/** Put the TAQR Id and Test Attempt ID into a table for the top of the page */
const getResponseIdentityTable = (taqr_id: number, test_attempt_id: number) : Table => {
  const cellWidthSettings = {
    size: 50,
    type: WidthType.PERCENTAGE,
  }

  const table = new Table({
    width: {
      size: 100,
      type: WidthType.PERCENTAGE,
    },
    rows: [
      new TableRow({
        children: [
          new TableCell({
            width: cellWidthSettings,
            margins: cellMarginSettings,
            children: [new Paragraph({
              alignment: AlignmentType.CENTER,
              children: [
                new TextRun({
                  text: "Response ID",
                  bold: true,
                }),
              ],
            })],
          }),
          new TableCell({
            width: cellWidthSettings,
            margins: cellMarginSettings,
            children: [new Paragraph({
              alignment: AlignmentType.CENTER,
              children: [
                new TextRun({
                  text: "Attempt ID",
                  bold: true,
                }),
              ],
            })],
          }),
        ],
      }),
      new TableRow({
        children: [
          new TableCell({
            margins: cellMarginSettings,
            children: [new Paragraph({
              text: `${taqr_id}`,
              alignment: AlignmentType.CENTER,
            })],
          }),
          new TableCell({
            margins: cellMarginSettings,
            children: [new Paragraph({
              text: `${test_attempt_id}`,
              alignment: AlignmentType.CENTER,
            })],
          }),
        ],
      }),
    ],
  });

  return table;
}

/** Extract and arrange needed data to render the student response in the document */
export const arrangeResponseDataForPrint = (questionConfig, lang, studentResponses, isIncludeRationale) : IResponseDataForPrint[] => {
  const responseDataForPrint: IResponseDataForPrint[] = [];
  for (let studentResponse of studentResponses) {
    const {id, test_attempt_id, response_raw, scan_base64, rationale} = studentResponse
    const entryIds = lang == "fr" ? questionConfig.langLink.entryOrder : questionConfig.entryOrder
    const entries = []
    for (let entryId of entryIds){
      const entry = response_raw[entryId]
      let str;
      if (entry) {
        str = entry.str;
      };
      const resultsQuestionElement = deepFind(questionConfig, 'inputEntryId', ''+entryId)
      const caption = resultsQuestionElement.content[0].caption.replace(/\*\*/g, '');
      entries.push({caption, str})
    }
    responseDataForPrint.push({
      taqr_id: id,
      test_attempt_id,
      entries,
      scan_base64,
      rationale: isIncludeRationale ? rationale : undefined
    })
  }
  return responseDataForPrint;
}


/** Given nested nodes, rearrange into a list of text snippets each with appropriate style */
const getStyledTextRunList = (node: HTMLElement, textRunList = [], inheritNodeTagList = [], inheritColorStyle?) => {
  /** Convert class names to text or background color (based on ckeditor colours) */
  const classNameToShadeStyle = (node: HTMLElement) => {
    if (!node.classList) return;
    if (node.classList.contains("marker-yellow")){
      return {shading: {fill: 'FDFD77'}}
    }
    else if (node.classList.contains("marker-green")){
      return {shading: {fill: '62F962'}}
    }
    else if (node.classList.contains("marker-pink")){
      return {shading: {fill: 'FC7899'}}
    }
    else if (node.classList.contains("marker-blue")){
      return {shading: {fill: '72CCFD'}}
    }
    else if (node.classList.contains("pen-red")){
      return {color: 'E92727'}
    }
    else if (node.classList.contains("pen-green")){
      return {color: '128A00'}
    }
  }
  const colorStyle = classNameToShadeStyle(node)
  const thisNodeTagList = [...inheritNodeTagList, node.nodeName]
  if (!node.childNodes.length){
    textRunList.push(new TextRun({
      text: node.textContent,
      italics: thisNodeTagList.includes("I"),
      bold: thisNodeTagList.includes("STRONG"),
      underline: thisNodeTagList.includes("U") ? {color: '000000'} : null,
      ...colorStyle,
      ...inheritColorStyle
    }));
  } else {
    node.childNodes.forEach((childNode: HTMLElement) => {
      getStyledTextRunList(childNode, textRunList, thisNodeTagList, colorStyle)
    })
  }
  return textRunList;
}

function base64ToArrayBuffer(base64: string) {
  let binaryString = window.atob(base64);
  let binaryLen = binaryString.length;
  let bytes = new Uint8Array(binaryLen);
  for (let i = 0; i < binaryLen; i++) {
     let ascii = binaryString.charCodeAt(i);
     bytes[i] = ascii;
  }
  return bytes;
}