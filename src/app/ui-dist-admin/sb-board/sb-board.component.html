<div class="page-body">
  <div>
    <header [breadcrumbPath]="breadcrumb" [hasSidebar]="true"></header>
    <div class="page-content is-fullpage " *ngIf="!hasAccess">
      Loading...
    </div>
    <div class="page-content is-fullpage " *ngIf="hasAccess">
      <ng-container *wlCtx="'IS_EQAO'">
        <img *ngIf="lang.c() === 'en'" style="width:8em;" src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/EQAO_Logo_SVG/1609173725758/EQAO_Logo_SVG.svg">
        <img *ngIf="lang.c() === 'fr'" style="width:8em;" src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/6526/authoring/image/1606866913334/image.png">
      </ng-container>
      <h2> {{schoolBoardInfo.name}} ({{schoolBoardInfo.foreign_id}}) </h2>
      <system-message [slug]="'BANNER_SB'"></system-message>
      <div>
        <menu-bar [menuTabs]="views" [tabIdInit]="selectedView" (change)="selectView($event)"></menu-bar>
        <div [ngSwitch]="selectedView">
          <div *ngSwitchCase="SchoolBoardView.TECH_READI">
            <sb-tech-readiness [boardInfo]="schoolBoardInfo"></sb-tech-readiness>
          </div>
          <!-- <div *ngSwitchCase="SchoolBoardView.CONTACT">
            <sb-it-contact [boardInfo]="schoolBoardInfo"></sb-it-contact>
          </div> -->
          <div *ngSwitchCase="SchoolBoardView.SECURITY">
            <tra-md slug="school_board_it_landing_bottom" [props]="{EMAIL: whiteLabel.getSiteText('supportEmail')}"></tra-md>
          </div>
          <div *ngSwitchCase="SchoolBoardView.MONITORING">
            <sb-monitoring [schoolDistrictId]="schoolBoardInfo.group_id"></sb-monitoring>
          </div>
          <div *ngSwitchCase="SchoolBoardView.SD_ACCOUNTS">
            <panel-sb-sd-accounts [schoolDistrictGroupId]="schoolBoardInfo.group_id"></panel-sb-sd-accounts>
          </div>
          <div *ngSwitchCase="SchoolBoardView.SESSIONS">
            <sb-sessions [boardInfo]="schoolBoardInfo"></sb-sessions>
          </div>
          <div *ngSwitchCase="SchoolBoardView.PRINCIPAL_KITS">
            <panel-sb-s-tw-statement [schoolDistrictGroupId]="schoolBoardInfo.group_id"></panel-sb-s-tw-statement>
          </div>
          <div *ngSwitchCase="SchoolBoardView.SCHOOLS">
            <panel-sb-s-accounts [schoolDistrictGroupId]="schoolBoardInfo.group_id"></panel-sb-s-accounts>
          </div>
          <div *ngSwitchCase="SchoolBoardView.REPORTS">
            <div *ngIf="adminWindows.length > 0" style = "margin-left:1em; margin-bottom:1em;" >
              <tra slug="sa_class_test_window_lb"></tra> 
              <!-- <select (change)="onAdminWindowChanged($event.target.value)" class="school-year-select">
                  <option *ngFor="let aw of adminWindows; let i = index" [value]="i" [selected]="i===currentAdminWindowIndex()">{{getAdminWindowSlug(aw)}}</option>
              </select> -->
            </div>
            <div>
              <da-reports [schoolDistrictId]="schoolBoardInfo.sd_id" ></da-reports>
            </div>
          </div>
          <div *ngSwitchCase="SchoolBoardView.LOCKDOWN_INFO">
            <tra-md slug="txt_ldb_sb_info"></tra-md>
            <hr>
            <button class="button is-info" (click)="openLDBConfig()">
              <tra slug="ldl_ldb_show_config"></tra>
            </button>
          </div>
        </div>
      </div>

      <!-- <sb-tech-readiness [boardInfo]="schoolBoardInfo"></sb-tech-readiness><br/> -->
      <div>

      </div>

    </div>
  </div>
  <footer [hasLinks]="true"></footer>
</div>
<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
    <div [ngSwitch]="cModal().type">
      <div *ngSwitchCase="LDBModal.LDB_CONFIG_MODAL" style="width: 80em;">
        <modal-ldb-config 
          [config]="cmc()"
          [pageModal]="pageModal"
        ></modal-ldb-config>
      </div>
    </div>
  </div>
</div>