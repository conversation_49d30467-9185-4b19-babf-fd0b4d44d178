<div *ngIf="isPageLoading" class="notification is-info">
  <p>Loading...</p>
</div>
<div *ngIf="!isPageLoading">
  <div *ngIf="!asmtStructCtx.data?.asmtStructure">
    No assessment structure found.
  </div>
  <div *ngIf="asmtStructCtx.data.asmtStructure" class="flex-column">
    <div style="max-height: 60em; overflow: auto; padding-right: 1em;">
      <div class="flex-column">
        <div class="flex-column has-border" style="gap: 1em; min-width: 35em;">
          <div class="title is-5">Assessment Configurations</div>
          <div class="flex-column">
            <div class="title is-6">Slug</div>
            <div style="width: 20em;">
              <input class="input" [(ngModel)]="asmtStructCtx.data.asmtStructure.slug" (change)="onAsmtStructChange()">
            </div>
          </div>
          <div class="flex-column">
            <div class="title is-6">Caption</div>
            <div style="width: 30em;">
              <input class="input" [(ngModel)]="asmtStructCtx.data.asmtStructure.caption" (change)="onAsmtStructChange()">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex-row">
      <div 
        cdkDropList
        (cdkDropListDropped)="drop(asmtStructCtx.data.asmtStructure.item_sets.sets, $event);"
        class="flex-column has-border" style="gap: 1em;"
      >
        <div class="title is-5">Sections</div>
        <div class="flex-column" style="max-height: 50em; overflow: auto; padding: 0.5em; gap: 1em">
          <div cdkDrag class="flex-column surface-card" *ngFor="let set of asmtStructCtx.data.asmtStructure.item_sets.sets">
            <div cdkDragHandle style="display: flex; justify-content: flex-end; cursor: move;">
              <a class="button" (click)="removeSection(asmtStructCtx.data.asmtStructure.item_sets.sets, set)">
                <i class="fas fa-trash"  aria-hidden="true"></i>
              </a> 
            </div>
            <div class="flex-column">
              <div class="title is-6">Item Set</div>
              <div class="select" style="width: 18em;">
                <select 
                  style="width: 100%;"
                  [(ngModel)]="set.item_set_structure_slug" 
                  (change)="onAsmtStructChange()"
                >
                  <option *ngFor="let slug of getItemSetStructureSlugs()" [value]="slug">{{slug}}</option>
                </select> 
              </div>
            </div>
            <div class="flex-column">
              <div class="title is-6">Section</div>
              <div class="select" style="width: 18em;">
                <select 
                  style="width: 100%;"
                  [(ngModel)]="set.set" 
                  (change)="onAsmtStructChange()" 
                >
                  <option *ngFor="let slug of getItemSetSectionSlugs(set.item_set_structure_slug)" [value]="slug">{{slug}}</option>
                </select> 
              </div>
            </div>
          </div>
        </div>
        <div style="margin-top: 0.5em;">
          <button class="button" (click)="addPartition(asmtStructCtx.data.asmtStructure.item_sets.sets)">Add Section</button>
        </div>
      </div>
      <div style="max-height: 60em; overflow: auto; padding-right: 1em;">
        <div class="flex-column has-border" style="min-width: 40em;">
          <div class="title is-5">Item Sets</div>
          <div class="flex-row">
            <div class="flex-column" style="gap: 1em;" *ngFor="let itemSet of itemSetStructCtx.data">
              <div class="flex-column">
                <div class="title is-6">Item Set Slug</div>
                <div style="width: 30em;">
                  <input class="input" [(ngModel)]="itemSet.slug" (change)="onItemSetSlugChange(itemSet)">
                </div>
              </div>
              <div *ngIf="itemSet.config.item_slug_defaults" class="flex-column" style="gap: 0em;">
                <div class="title is-6">Item Slug Defaults</div>
                <div class="flex-column indent">
                  <div *ngFor="let default of itemSet.config.item_slug_defaults | keyvalue">
                    <div class="flex-row">
                      <a class="tag-info is-bold" (click)="updateKey('Update Item Slug', default.key, itemSet.config.item_slug_defaults)">{{default.key}}</a>
                      <a class="close" (click)="removeObjKey(itemSet.config.item_slug_defaults, default.key)">
                        <i class="fa fa-times" aria-hidden="true"></i>
                      </a>
                    </div>
                    <div class="flex-column indent">
                      <div class="is-bold">Caption</div>
                      <div class="flex-column indent" style="gap: 0em;">
                        <div class="flex-row">
                          <a class="tag-info is-bold" (click)="updateProperty('New English text', 'en', default.value.caption)">
                            English
                          </a>
                          <a class="tag-info" (click)="updateProperty('New English text', 'en', default.value.caption)">{{default.value.caption.en}}</a>
                        </div>
                        <div class="flex-row">
                          <a class="tag-info is-bold" (click)="updateProperty('New French text', 'fr', default.value.caption)">
                            French
                          </a>
                          <a class="tag-info" (click)="updateProperty('New French text', 'fr', default.value.caption)">{{default.value.caption.fr}}</a>
                        </div>
                      </div>
                      <div class="is-bold">Parameters</div>
                      <div class="flex-column indent" style="gap: 0em;">
                        <div class="flex-row" style="width: 10em; justify-content: space-between;" *ngFor="let param of default.value.item_params | keyvalue">
                          <div class="flex-row" style="gap: 0.2em;">
                            <a class="tag-info is-bold" (click)="updateProperty('New Value', param.key, default.value.item_params)">{{param.key}}</a>
                            <a class="tag-info" (click)="updateProperty('New Value', param.key, default.value.item_params)">{{param.value}}</a>
                          </div>
                          <a class="close" (click)="removeObjKey(default.value.item_params, param.key)">
                            <i class="fa fa-times" aria-hidden="true"></i>
                          </a>
                        </div>
                        <div style="margin-top: 0.5em;">
                          <button class="button is-small" (click)="addParameter(default.value.item_params, 'Parameter')">Add</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div style="margin-top: 0.5em;">
                  <button class="button is-small" (click)="addItemSlugDefault(itemSet.config.item_slug_defaults)">Add Item Default</button>
                </div>
              </div>
              <div class="flex-column">
                <div class="title is-6">Item Set Parameters</div>
                <div class="flex-column indent" style="gap: 0em;">
                  <div class="flex-row" style="width: 10em; justify-content: space-between;" *ngFor="let param of itemSet.config.item_params | keyvalue">
                    <div class="flex-row" style="gap: 0.2em;">
                      <a class="tag-info is-bold" (click)="updateProperty('New Value', param.key, itemSet.config.item_params)">{{param.key}}</a>
                      <a class="tag-info" (click)="updateProperty('New Value', param.key, itemSet.config.item_params)">{{param.value}}</a>
                    </div>
                    <a class="close" (click)="removeObjKey(itemSet.config.item_params, param.key)">
                      <i class="fa fa-times" aria-hidden="true"></i>
                    </a>
                  </div>
                  <div style="margin-top: 0.5em;">
                    <button class="button is-small" (click)="addParameter(itemSet.config.item_params, 'Parameter')">Add</button>
                  </div>
                </div>
              </div>
              <div class="title is-6">Section Sets</div>
              <div class="flex-column surface-card" style="gap: 1em;" *ngFor="let section of itemSet.config.sets">
                <div style="display: flex; justify-content: flex-end;">
                  <a class="button" (click)="removeItemContent(itemSet.sets, section)">
                    <i class="fas fa-trash"  aria-hidden="true"></i>
                  </a> 
                </div>
                <div class="flex-column">
                  <div class="title is-6">Section Slug</div>
                  <div style="width: 30em;">
                    <input class="input" [(ngModel)]="section.slug" (change)="onSectionSlugChange(section)">
                  </div>
                </div>
                <div class="flex-column">
                  <div class="title is-6">Section Caption</div>
                  <div class="flex-column indent" style="gap: 0em;">
                    <div class="flex-row">
                      <div>
                        English
                      </div>
                      <a class="tag-info" (click)="updateProperty('New Value', 'en', section.caption)">{{section.caption.en}}</a>
                    </div>
                    <div class="flex-row">
                      <div>
                        French
                      </div>
                      <a class="tag-info" (click)="updateProperty('New Value', 'fr', section.caption)">{{section.caption.fr}}</a>
                    </div>
                  </div>
                </div>
                <div class="flex-column">
                  <div class="title is-6">Section Parameters</div>
                  <div class="flex-column indent" style="gap: 0em;">
                    <div class="flex-row" style="width: 10em; justify-content: space-between;" *ngFor="let param of section.item_params | keyvalue">
                      <div class="flex-row" style="gap: 0.2em;">
                        <a class="tag-info is-bold" (click)="updateProperty('New Value', param.key, section.item_params)">{{param.key}}</a>
                        <a class="tag-info" (click)="updateProperty('New Value', param.key, section.item_params)">{{param.value}}</a>
                      </div>
                      <a class="close" (click)="removeObjKey(section.item_params, param.key)">
                        <i class="fa fa-times" aria-hidden="true"></i>
                      </a>
                    </div>
                    <div style="margin-top: 0.5em;">
                      <button class="button is-small" (click)="addParameter(section.item_params, 'Parameter')">Add</button>
                    </div>
                  </div>
                </div>
                <!-- <div class="flex-column">
                  <div class="title is-6">Entry Captions</div>
                  <div class="flex-column indent" style="gap: 0em;">
                    <div class="flex-row" style="width: 13em; justify-content: space-between;" *ngFor="let caption of section.item_captions | keyvalue">
                      <div class="flex-row" style="gap: 0.2em;">
                        <a class="tag-info is-bold" (click)="updateProperty('New Value', caption.key, section.item_captions)">{{caption.key}}</a>
                        <a class="tag-info" (click)="updateProperty('New Value', caption.key, section.item_captions)">{{caption.value}}</a>
                      </div>
                      <a class="close" (click)="removeObjKey(section.item_captions, caption.key)">
                        <i class="fa fa-times" aria-hidden="true"></i>
                      </a>
                    </div>
                    <div style="margin-top: 0.5em;">
                      <button class="button is-small" (click)="addParameter(section.item_captions, 'Caption')">Add</button>
                    </div>
                  </div>
                </div> -->
                <div 
                  cdkDropList
                  (cdkDropListDropped)="drop(section.items, $event);"
                  class="flex-column"
                >
                  <div class="title is-6">
                    Score Entries
                  </div>
                  <div class="flex-column indent">
                    <div cdkDrag class="flex-column surface-card" style="gap: 1em;" *ngFor="let item of section.items">
                      <div cdkDragHandle style="display: flex; justify-content: flex-end; cursor: move;">
                        <a class="button" (click)="removeItemContent(section.items, item)">
                          <i class="fas fa-trash"  aria-hidden="true"></i>
                        </a> 
                      </div>
                      <div class="flex-column">
                        <div class="title is-6">Entry Slug</div>
                        <div style="width: 30em;">
                          <input class="input" [(ngModel)]="item.slug" (change)="onItemSetStructChange()">
                        </div>
                      </div>
                      <div class="flex-column">
                        <div class="title is-6">Max Value</div>
                        <div style="width: 7em;">
                          <input class="input" type="number" [(ngModel)]="item.val_max" (change)="onItemSetStructChange()">
                        </div>
                      </div>
                      <div class="flex-column">
                        <div class="title is-6">Entry Parameters</div>
                        <div class="flex-column indent" style="gap: 0em;">
                          <div class="flex-row" style="width: 10em; justify-content: space-between;" *ngFor="let param of item.item_params | keyvalue">
                            <div class="flex-row" style="gap: 0.2em;">
                              <a class="tag-info is-bold" (click)="updateProperty('New Value: ' + param.key, param.key, item.item_params)">{{param.key}}</a>
                              <a class="tag-info" (click)="updateProperty('New Value: ' + param.key, param.key, item.item_params)">{{param.value}}</a>
                            </div>
                            <a class="close" (click)="removeObjKey(item.item_params, param.key)">
                              <i class="fa fa-times" aria-hidden="true"></i>
                            </a>
                          </div>
                          <div style="margin-top: 0.5em;">
                            <button class="button is-small" (click)="addParameter(item.item_params)">Add</button>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div style="margin-top: 0.5em;">
                      <button class="button" (click)="addItem(section.items)">Add Entry</button>
                    </div>
                  </div>
                </div>
              </div>
              <div style="margin-top: 0.5em;">
                <button class="button" (click)="addSection(itemSet.config.sets)">Add Section</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style="margin-top: 0.5em;">
      <button [disabled]="!hasChanges()" (click)="saveChanges()" class="button is-success"><tra slug="ie_save_changes"></tra></button>
    </div>
    <div style="margin-top: 0.5em;" *ngIf="isSaving">
      Saving...
    </div>
  </div>
</div>