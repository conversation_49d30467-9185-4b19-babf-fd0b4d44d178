<div style="padding:1em;">

  <mode-toggle [itemBankCtrl]="itemBankCtrl" [saveLoadCtrl]="saveLoadCtrl" [frameworkCtrl]="frameworkCtrl"></mode-toggle>
  
  <div style="display: flex; justify-content: space-between;">
    <h2 class="assessment-framework">
      {{itemBankCtrl.currentSetName.value}} Framework
      <button [disabled]="isReadOnly()" class="button " (click)="frameworkCtrl.isEditingFramework = !frameworkCtrl.isEditingFramework">
        Edit
      </button>
    </h2>
  </div>
  <div class="notification is-small is-primary black-text" *ngIf="frameworkCtrl.isParamSaveRequired">
    Your parameters are outdated and require a save to be updated.
  </div>
  <div class="notification is-small is-primary black-text" *ngIf="frameworkCtrl.isAsmtStructureSaveRequired">
    Your item structure is outdated and requires a save to be updated.
  </div>
  <div *ngIf="frameworkCtrl.asmtFmrk && frameworkCtrl.isEditingFramework" class="framework-container"> 
    <widget-framework-settings [assetLibraryCtrl]="assetLibraryCtrl"
      [itemBankCtrl]="itemBankCtrl"
      [frameworkCtrl]="frameworkCtrl"
      [quadrantCtrl]="quadrantCtrl"
      [panelCtrl]="panelCtrl"
      [testletCtrl]="testletCtrl"
      [saveLoadCtrl]="saveLoadCtrl"
      (openSectionEditModal)="openSectionEditModal($event)"
    ></widget-framework-settings>
  </div>
  
  <!-- *ngIf="frameworkCtrl.isFrameworkView || (testDesignReleaseHistory && testDesignReleaseHistory.length) " -->
  <!-- *ngIf="isItemListingReady()" -->
  <widget-publishing 
    [publishingCtrl]="publishingCtrl"
    [itemBankCtrl]="itemBankCtrl"
    [frameworkCtrl]="frameworkCtrl"
    [saveLoadCtrl]="saveLoadCtrl"
  ></widget-publishing>
  <p *ngIf="hasRemovedQuestions()" style="color: red;">
    <tra slug="framework_publish_disabled_containing_archived"></tra>
  </p>
  <!-- Warning for missing elements -->
  <div *ngIf="hasRemovedQuestions()" class="tab-container">
    <div class="tab" (click)="toggleAllInFramework()">
      <span><tra slug="framework_contains_archived_items"></tra></span>
      <i [class.fa-chevron-right]="frameworkCollapsed" [class.fa-chevron-down]="!frameworkCollapsed" class="fa"></i>
    </div>
    <div *ngIf="!frameworkCollapsed">
      <div *ngFor="let section of getRemovedQuestionsSections()">
        <div class="tab" (click)="toggleIsSectionCollapsed(section)">
          <span>{{ section }}</span>
          <i [class.fa-chevron-right]="collapsedSections[section]" [class.fa-chevron-down]="!collapsedSections[section]" class="fa"></i>
        </div>
        <div *ngIf="!collapsedSections[section]" class="content">
          <ul>
            <li *ngFor="let question of removedQuestions[section]">{{ question }}</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <widget-test-design-item-banks *ngIf="frameworkCtrl.isFrameworkView || saveLoadCtrl.itemBanksUsed.length " 
    [frameworkCtrl]="frameworkCtrl"
    [saveLoadCtrl]="saveLoadCtrl"
  ></widget-test-design-item-banks>
 

  <ng-container *ngIf="!isSingularReadOnly()">
    <hr/>

    <div class="lang-btn-container" style="justify-content: flex-start;" >
      <button *ngIf="itemBankCtrl.isLangEnabled('en')" class="button is-small " (click)="previewCtrl.setLang('en')" [class.is-info]="itemBankCtrl.isLang('en')">
        <span>EN</span>
      </button>
      <button *ngIf="itemBankCtrl.isLangEnabled('fr')" class="button is-small " (click)="previewCtrl.setLang('fr')" [class.is-info]="itemBankCtrl.isLang('fr')">
        <span>FR</span>
      </button>
    </div>

    <hr/>
    
    <menu-bar 
      [menuTabs]="questionsViews"
      [tabIdInit]=frameworkCtrl.selectedQuestionView
      (change)="frameworkCtrl.selectQuestionView($event)"
    ></menu-bar>
    
    <div [ngSwitch]="frameworkCtrl.selectedQuestionView">
      <widget-quadrants
        *ngSwitchCase="QuestionView.QUADRANTS"
        [itemBankCtrl]="itemBankCtrl"
        [quadrantCtrl]="quadrantCtrl"
        [panelCtrl]="panelCtrl"
        [frameworkCtrl]="frameworkCtrl"
        [itemFilterCtrl]="itemFilterCtrl"
        [testletCtrl]="testletCtrl"
      ></widget-quadrants>
      <widget-testlets
        *ngSwitchCase="QuestionView.TESTLETS"
        [frameworkCtrl]="frameworkCtrl"
        [testletCtrl]="testletCtrl"
      ></widget-testlets>
      <widget-audits
        *ngSwitchCase="QuestionView.AUDITS"
        [assetLibraryCtrl]="assetLibraryCtrl"
        [testletCtrl]="testletCtrl"
        [frameworkCtrl]="frameworkCtrl"
        [auditCtrl]="auditCtrl"
        [itemBankCtrl]="itemBankCtrl"
      ></widget-audits>
      <widget-assembled-forms 
        *ngSwitchCase="QuestionView.ASSEMBLED_FORMS"
        [previewCtrl]="previewCtrl"
        [panelCtrl]="panelCtrl"
        [frameworkCtrl]="frameworkCtrl"
      ></widget-assembled-forms>
      <widget-mscat-panels 
        *ngSwitchCase="QuestionView.MSCAT_PANELS"
        [previewCtrl]="previewCtrl"
        [panelCtrl]="panelCtrl"
        [frameworkCtrl]="frameworkCtrl"
      ></widget-mscat-panels>
      <widget-linear-form-construction
        *ngSwitchCase="QuestionView.FORM_CONSTRUCTION"
        [previewCtrl]="previewCtrl"
        [frameworkCtrl]="frameworkCtrl"

        [assetLibraryCtrl]="assetLibraryCtrl"
        [auditCtrl]="auditCtrl"
        [itemBankCtrl]="itemBankCtrl"
        [itemEditCtrl]="itemEditCtrl"
        [itemFilterCtrl]="itemFilterCtrl"
        [memberAssignmentCtrl]="memberAssignmentCtrl"
        [panelCtrl]="panelCtrl"
        [printViewCtrl]="printViewCtrl"
        [publishingCtrl]="publishingCtrl"
        [quadrantCtrl]="quadrantCtrl"
        [saveLoadCtrl]="saveLoadCtrl"
        [testFormGen]="testFormGen"
        [testletCtrl]="testletCtrl"
        (openSectionEditModal)="openSectionEditModal($event)"
        (openQuestionBulkLockModal)="openQuestionBulkLockModal()"
      ></widget-linear-form-construction>
      <widget-template-review
        *ngSwitchCase="QuestionView.TEMPLATE_REVIEW"
        [previewCtrl]="previewCtrl"
        [frameworkCtrl]="frameworkCtrl"

        [assetLibraryCtrl]="assetLibraryCtrl"
        [auditCtrl]="auditCtrl"
        [itemBankCtrl]="itemBankCtrl"
        [itemEditCtrl]="itemEditCtrl"
        [itemFilterCtrl]="itemFilterCtrl"
        [memberAssignmentCtrl]="memberAssignmentCtrl"
        [panelCtrl]="panelCtrl"
        [printViewCtrl]="printViewCtrl"
        [publishingCtrl]="publishingCtrl"
        [quadrantCtrl]="quadrantCtrl"
        [saveLoadCtrl]="saveLoadCtrl"
        [testFormGen]="testFormGen"
        [testletCtrl]="testletCtrl"
      ></widget-template-review>
      <widget-sample-forms-listing
        *ngSwitchCase="QuestionView.SAMPLE_FORMS"
        [frameworkCtrl]="frameworkCtrl"
        [testFormGen]="testFormGen"
        [previewCtrl]="previewCtrl"
      ></widget-sample-forms-listing>
      <div *ngSwitchCase="QuestionView.STYLE_PROFILE">
        <fieldset [disabled]="isReadOnly()" style="margin: 1em;">
          <tra slug="ie_item_bank_style_profile"></tra>          
          <select [formControl]="itemBankCtrl.styleProfileSelector" style="margin-left:1em;">
            <option *ngFor="let option of profiles" [value]="option.slug">
              <tra [slug]="option.caption"></tra>
            </option>
          </select>
        </fieldset>
        <widget-json-editor
          [data] = "getStyleProfileData()"
          [isUpdateDisabled]="false"
          (submitData) = "updateStyleProfile($event)"
        >
        </widget-json-editor>
      </div>
      <widget-score-entry-settings 
        *ngSwitchCase="QuestionView.SCORE_ENTRY" 
        [previewCtrl]="previewCtrl"
        [frameworkCtrl]="frameworkCtrl"

        [assetLibraryCtrl]="assetLibraryCtrl"
        [auditCtrl]="auditCtrl"
        [itemBankCtrl]="itemBankCtrl"
        [itemEditCtrl]="itemEditCtrl"
        [itemFilterCtrl]="itemFilterCtrl"
        [memberAssignmentCtrl]="memberAssignmentCtrl"
        [panelCtrl]="panelCtrl"
        [printViewCtrl]="printViewCtrl"
        [publishingCtrl]="publishingCtrl"
        [quadrantCtrl]="quadrantCtrl"
        [saveLoadCtrl]="saveLoadCtrl"
      ></widget-score-entry-settings>
    </div>

    <!-- this is needs to load in order for publishing to be safe -->
    <widget-item-listing
      *ngIf="isItemListingReady() && !itemBankCtrl.isBulkLocking"
      [frameworkCtrl]="frameworkCtrl"
      [itemBankCtrl]="itemBankCtrl"
      [itemEditCtrl]="itemEditCtrl"
      [printViewCtrl]="printViewCtrl"
      [saveLoadCtrl]="saveLoadCtrl"
      [itemFilterCtrl]="itemFilterCtrl"
      [testletCtrl]="testletCtrl"
      [panelCtrl]="panelCtrl"
    ></widget-item-listing>

    <hr/>

    <div> 
      <button [disabled]="isReadOnly()" class="button is-small has-icon" style="z-index: 10;" [class.is-success]="saveLoadCtrl.isSaveChangedRequired" (click)="saveLoadCtrl.saveChanges()">
        <span class="icon"><i class="fa fa-floppy-o" aria-hidden="true"></i></span>
        <span><tra slug="ie_save_changes"></tra></span>
      </button> 
    </div>
  </ng-container>
  <div class="custom-modal" *ngIf="cModal()">
    <div class="modal-contents">
        <ng-container [ngSwitch]="cModal().type">
            <section-edit-modal *ngSwitchCase="FrameworkModal.SECTION_EDIT" [partition]="cmc().section" [frameworkCtrl]="frameworkCtrl" [itemBankCtrl]="itemBankCtrl"></section-edit-modal>
            <div *ngSwitchCase="FrameworkModal.BULK_LOCK">
              Are you sure that you would like to {{cmc().isLock ? 'lock' : 'unlock'}} all questions in the test design?
            </div>
            <div *ngSwitchCase="FrameworkModal.BULK_LOCK_PROCESSING" style="padding: 2em;">
              Processing... ({{itemBankCtrl.bulkLockIndex}}/{{itemBankCtrl.bulkLockLength}})
            </div>
        </ng-container>
        <modal-footer *ngIf="cModal().type != FrameworkModal.BULK_LOCK_PROCESSING" [pageModal]="pageModal" ></modal-footer>
    </div>
  </div>

</div>