import { FormControl } from '@angular/forms';
import DeepDiff from 'deep-diff';
import * as _ from 'lodash';
import * as moment from 'moment';
import { Subject } from 'rxjs';
import { AuthRolesService } from '../../auth-roles.service';
import { AuthScopeSetting, AuthScopeSettingsService } from "../../auth-scope-settings.service";
import { AuthService } from '../../../api/auth.service';
import { EditingDisabledService } from '../../editing-disabled.service';
import { extractQuestionAssetVersionIds, updateCurrentQuestionExpectedAnswer } from '../models/expected-answer';
import { FRAMEWORK_CONFIG } from '../models/framework-data';
import { getElementChildren } from '../models';
import { IAssessmentFrameworkDef, QUESTION_WORDING_OPTS } from '../models/assessment-framework';
import { IContentElement, IHistoricalItemRegisterModel, IHistoricalItemRegisterModelTw, IItemRegister, IItemRegisterByItemTd, IItemRegisterSummary, IPsychStatSummary, IQuestionScoringInfo, IScoringCodes, ISequenceConfig } from '../../../ui-testrunner/models/index';
import { ignoreFromDiff, ItemComponentEditService } from '../../item-component-edit.service';
import { IItemTag } from '../../item-tag/item-tag.component';
import { IItemSetResponse, IQuestionConfig, IQuestionImpressionConfig } from '../../../ui-testrunner/models';
import { ItemBankCtrl } from './item-bank';
import { ItemDiffCtrl } from './item-diff';
import { ItemEditCtrl } from './item-edit';
import { ItemFilterCtrl } from './item-filter';
import { ItemMakerService } from '../../item-maker.service';
import { ItemSetFrameworkCtrl } from './framework';
import { ItemType } from '../../models';
import { LangService } from '../../../core/lang.service';
import { MemberAssignmentCtrl } from './member-assignment';
import { RoutesService } from '../../../api/routes.service';
import { serverTimestamp } from '../models/task-types';
import { Subscription } from 'rxjs';
import { EStyleProfile } from 'src/app/core/styleprofile.service';
import { UserRoles } from 'src/app/api/models/roles';
import { Destroyable } from './destroyable';
import { HighlighterService } from 'src/app/ui-testrunner/highlighter.service';
import {IStageInfoByQuestion} from './../../item-workflow-section/model'
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { objectsEqual, removeParameterDuplicates } from '../../services/util';
import { VoiceoverStateService } from 'src/app/ui-testrunner/voiceover-state.service';

export class ItemBankSaveLoadCtrl implements Destroyable {

  currentHash: string;
  isSaving: boolean;
  isSuggesting: boolean;
  isLoading = true;
  isLoadingQuestion;
  isUserInited = false;
  isSaveChangedRequired = false;
  existingFakeSaveDelayTimeout;
  public isInspecting: boolean;
  public itemBanksUsed = [];
  isSavingItemSet:boolean;
  isLoadingItemSets;

  restoreENFc = new FormControl(true);
  restoreFRFc = new FormControl(false);
  restoreParamsFc = new FormControl(true);
  restoreLabel = new FormControl(false);

  importEN = new FormControl(true);
  importFR = new FormControl(false);
  importLabel = new FormControl(false);
  importParams = new FormControl(true);

  public itemFilterCtrl: ItemFilterCtrl;

  currentQuestionRevisions: {
    displayList?: any[],
    originalQuestion?: string; // IQuestionConfig
    restoredToId?: number, 
    restoringToId?: number, 
    skippedOptions?: {skipEN: boolean, skipFR: boolean, skipParams: boolean, skipLabel: boolean} 
  };
  isLoadingRevisions: boolean;
  private saveInterval;
  saveConflictWarningGiven: boolean;
  userSub:Subscription
  autoSaveSub: Subject<any> = new Subject();

  public itemEditCtrl:ItemEditCtrl;
  public itemDiffCtrl: ItemDiffCtrl;
  public isEarlyYears: boolean = false;

  
  constructor(
    public auth: AuthService,
    public myItems: ItemMakerService,
    public authScopeSettings: AuthScopeSettingsService,
    public routes: RoutesService,
    public editingDisabled: EditingDisabledService,
    public authRoles: AuthRolesService,
    public memberAssignmentCtrl:MemberAssignmentCtrl,
    public frameworkCtrl: ItemSetFrameworkCtrl,
    public itemBankCtrl:ItemBankCtrl,
    public itemComponentEdit: ItemComponentEditService,
    public lang: LangService,
    private highlighter: HighlighterService,
    private login: LoginGuardService,
    public voiceoverState: VoiceoverStateService,
  ){
    this.initSaveInterval();
    this.itemComponentEdit.saveCurrQSub.subscribe((sub) => {
      this.saveCurrentQuestionData(true, undefined, undefined, sub?.isAcceptSugg);
    })
  }

  destroy(){
    if (this.saveInterval) {
      clearInterval(this.saveInterval);
    }
    this.userSub.unsubscribe();
  }

  initSaveInterval(){
    this.saveInterval = setInterval(() => {
      this.autoSaveQuestionData();
    }, 30 * 1000);
  }

  /** Replace list of UIDs by list of objects with full user details */
  processStageInfo(stageInfoMap, userDetailMap):IStageInfoByQuestion {
    Object.keys(stageInfoMap).forEach(qId => {
      const qStageInfo = stageInfoMap[qId];
      Object.keys(qStageInfo).forEach(lang => {
        const {assigneeUids} = qStageInfo[lang]
        qStageInfo[lang].assignees = []
        if (!assigneeUids) return;
        assigneeUids.forEach(uid => {
          const {first_name, last_name, contact_email} = userDetailMap[uid]
          qStageInfo[lang].assignees.push({
            uid, first_name, last_name, contact_email
          })
        })
        delete qStageInfo[lang].assigneeUids
      })
    })
    return stageInfoMap;
  }

  processHistoricalPsychStatSummary(historicalPsychStatSummary: IPsychStatSummary[], historicalPsychStats: any[]) {
    type tdMap = Map<number, {slug: string, psychStats: any, isSelected: boolean}>
    const testWindowTDMap = new Map<number, {title: string, testDesigns: tdMap, isSelected: boolean}>();

    historicalPsychStatSummary.forEach((psychStat) => {
      console.log(psychStat.test_window_id, 'Loading summary test window ID')
      if(!testWindowTDMap.has(psychStat.test_window_id)) {
        const title = psychStat.title;
        const tdMap: tdMap = new Map();
        testWindowTDMap.set(psychStat.test_window_id, {title, testDesigns: tdMap, isSelected: false});
      }
      const testWindow = testWindowTDMap.get(psychStat.test_window_id);
      testWindow.testDesigns.set(psychStat.test_design_id, {slug: psychStat.twtar_slug, psychStats: {}, isSelected: false});
    });

    historicalPsychStats.forEach((psychStat) => {
      const testWindow = testWindowTDMap.get(psychStat.test_window_id);
      if(testWindow && testWindow.testDesigns.has(psychStat.test_design_id)) {
        testWindow.testDesigns.get(psychStat.test_design_id).psychStats[psychStat.item_id] = psychStat;
      }
    })

    return testWindowTDMap;
  }

  getPsychStatProps() {
    const psychStatsProps = ["lang", "NR", "NF", "omit", "p", "rpb", "crpb", "form_code", "95p_lower", "95p_upper", "p_high", "p_mid", "p_low", "total", "high", "mid", "low", "iri", "rbis", "crbis", "imported_on", "test_design_id",  "export_id"]
    return psychStatsProps
  }

  processHistoricalItemRegisterByItem(historicalItemRegister:IItemRegister[]) : IItemRegisterByItemTd {
    const byItem: IItemRegisterByItemTd = new Map()
    for (let record of historicalItemRegister){
      const {question_id, test_window_id, test_design_id} = record;
      if (!byItem.has(question_id)){
        byItem.set(question_id, new Map())
      }
      const td_slug = test_window_id+';'+test_design_id;
      byItem.get(question_id).set(td_slug, record)
    }
    return byItem
  }
  processHistoricalItemRegisterSummary(historicalItemRegisterSummary:IItemRegisterSummary[], historicalItemRegister:IItemRegister[]) : IHistoricalItemRegisterModel {
    let latest_tw_id:number;
    const test_windows:IHistoricalItemRegisterModelTw[] = [];
    const twRef:Map<number, IHistoricalItemRegisterModelTw> = new Map();
    const parseTwTitle = (titleJson:string) => {
      try {
        return JSON.parse(titleJson).en;
      }
      catch (e){
        return titleJson
      }
    }
    for (let record of historicalItemRegisterSummary){
      const tw_title_str = parseTwTitle(record.tw_title)
      if (!twRef.has(record.test_window_id)){
        latest_tw_id = Math.max(latest_tw_id || 0, record.test_window_id);
        const tw = {
          is_selected: false,
          id: record.test_window_id,
          title: tw_title_str,
          test_designs: [],
        }
        twRef.set(record.test_window_id, tw);
        test_windows.push(tw)
      }
      const tw = twRef.get(record.test_window_id);
      tw.test_designs.push({
        slug: record.test_window_id+';'+record.test_design_id,
        caption: `[TD: ${record.test_design_id}] (${record.twtar_slug}) ${tw_title_str}`,
        td_id: record.test_design_id
      })
    }
    let historicalProps:{slug:string, caption:string}[] = [];
    if (historicalItemRegister.length > 0){
      const sampleRecord = historicalItemRegister[0];
      Object.keys(sampleRecord).forEach(prop => {
        if (!['test_window_id', 'twtar_slug', 'test_design_id'].includes(prop)){
          const isAnyNonNull = historicalItemRegister.some( record => !!record[prop]);
          if (isAnyNonNull){
            historicalProps.push({slug: prop, caption: prop}) // caption to be updated
          }
        }
      })
    }

    const selected_td_slugs:Map<string, boolean> = new Map()
    if (latest_tw_id){
      const tw = twRef.get(latest_tw_id);
      tw.is_selected = false;
      for (let td of tw.test_designs){
        selected_td_slugs.set(td.slug, true);
      }
    }

    return {
      selected_td_slugs,
      historicalProps,
      test_windows,
      list: historicalItemRegisterSummary,
    }
  }

  public getGroupID() {
    return this.itemBankCtrl.groupId
  }

  /**
   * This function is called to retrieve archived questions
   * @returns - array of the archived questions
   */
  async  getArchivedQuestions() {
    let archivedQuestions = [];
    const question_set_id = this.itemBankCtrl.customTaskSetId;
    //we follow the method from load items so archived questions can still be viewed
    try {
      const res = await this.auth.apiGet(this.routes.TEST_AUTH_ITEM_SET, question_set_id, { query: { getArchived: 'true'} });
  
      for (let questionRecord of res.questions) {
        const q = Object({
          ...JSON.parse(questionRecord.config),
          label: questionRecord.question_label,
          id: questionRecord.id,
          type: questionRecord.item_type
        });
  
        if (q.type === ItemType.SEQUENCE) {
          q.children = [];
        }
        archivedQuestions.push(q);
      }
    } catch (error) {
      // Handle any errors that occur during the API call
      console.error('Error fetching archived questions:', error);
    }
  
    return archivedQuestions.filter( (q) => {
      return !this.itemBankCtrl.getParentId(q) && 
        q.type !== ItemType.SEQUENCE //Assumes children have already been filtered
      ;
    });
  }
  updateCurrentQuestionExpectedAnswer = _.throttle(() => this._updateCurrentQuestionExpectedAnswer(), 1000);
  loadCustomTaskSet(queryParams:{snapshotFilter:string}) {
    console.log('loadCustomTaskSet', +(new Date()))
      const question_set_id = this.itemBankCtrl.customTaskSetId;
      this.auth
        .apiGet(this.routes.TEST_AUTH_ITEM_SET, question_set_id, {query: queryParams})
        .then( async (res:IItemSetResponse) => {
          this.myItems.loadMyGroupMembers(res.single_group_id, res.group_id)
              .then(result => {
                this.memberAssignmentCtrl.setGroupMembers(result);
              });
          
          this.itemBankCtrl.publicPwdProtected = res.public_pwd_protected;
          this.itemBankCtrl.availableTags = res.availableTags || [];
          this.itemBankCtrl.scoringInfo = res.scoringInfo || [];
          this.itemBankCtrl.historicalItemRegister = this.processHistoricalItemRegisterByItem(res.historicalItemRegister || []);
          this.itemBankCtrl.itemRegisterSummary = this.processHistoricalItemRegisterSummary(res.historicalItemRegisterSummary || [], res.historicalItemRegister || []);
          this.itemBankCtrl.psychStatsProps = this.getPsychStatProps();
          this.itemBankCtrl.historicalPsychStatSummary = this.processHistoricalPsychStatSummary(res.historicalPsychStatsSummary || [], res.historicalPsychStats || [])
          console.log(this.itemBankCtrl.historicalPsychStatSummary, 'psych stat mapping')
          this.itemBankCtrl.stageInfoByQuestion = this.processStageInfo(res.stageInfoByQuestion.stageInfoMap || {}, res.stageInfoByQuestion.userDetailMap || {});
          this.itemBankCtrl.pendingGraphicReqCountByQuestion = res.pendingGraphicReqCountByQuestion || {}
          this.itemBankCtrl.availableScoringCodes = res.availableScoringCodes || [];
          this.itemBankCtrl.batchAllocPolicies = res.batchAllocPolicies || [];
          for(const tag of this.itemBankCtrl.availableTags) {
            this.itemBankCtrl.availableTagsMap[tag.id] = tag;
          }
          this.itemBankCtrl.groupId = res.group_id
          this.itemBankCtrl.single_groupId = res.single_group_id
          console.log(this.routes.TEST_AUTH_ITEM_SET, res)
          this.itemBankCtrl.questions = [];
          this.itemBankCtrl.itemInfo = {};
          this.itemBankCtrl.versionIdMap = {};

          for(let questionRecord of res.questions) {
            if(questionRecord.versionId) {
              this.itemBankCtrl.versionIdMap[questionRecord.id] = questionRecord.versionId
            }
            const q = Object({
              ... JSON.parse(questionRecord.config),
              label: questionRecord.question_label,
              id: questionRecord.id,
              question_set_id: questionRecord.question_set_id,
              type: questionRecord.item_type
            });

            if(q.type === ItemType.SEQUENCE) {
              q.children = [];
            }
            this.itemBankCtrl.questions.push(q);
            
            this.itemBankCtrl.itemInfo[questionRecord.id] = {
              order: questionRecord.order,
              parentId: questionRecord.parent_id,
              linkedTags: []
            } //Want this to be separate from the config since they are meta-properties that shouldn't be imported with a config.
          }

          for(const tagLink of res.tagLinks) {
            this.itemBankCtrl.itemInfo[tagLink.item_id].linkedTags.push(this.itemBankCtrl.availableTagsMap[tagLink.tag_id]);
          }
          this.itemBankCtrl.refreshQuestionsView();

          // set active langages
          this.itemBankCtrl.setActiveItemSetLanguages(res.languages);
          this.itemBankCtrl.setSelectedStyleProfile(res.style_profile);

          // Determine if Item Set is EYS
          if (res.is_score_entry == 1) {
            this.isEarlyYears = true;
          }
          
          this.frameworkCtrl.asmtFmrk = res.framework ? JSON.parse(<string>res.framework) : FRAMEWORK_CONFIG;

          await this.syncParameters();

          // apply to previous framework that doesn't have this isLangLink prop for partiton if useLangSpecificSectionProps is turned on - fix for partiton to avoid circular deps
          if(this.frameworkCtrl.asmtFmrk.useLangSpecificSectionProps) {
            this.frameworkCtrl.asmtFmrk.partitions?.forEach(partition => {
              if(partition.langLink && !partition.langLink.isLangLink) {
                partition.langLink.isLangLink = true;
              }
            })
          }

          this.auth.apiGet(this.routes.TEST_AUTH_GROUP_ROLES, res.group_id, {query: {single_group_id: res.single_group_id, group_id: [res.group_id, res.single_group_id]}})
            .then((roles)=>{
              this.authRoles.currentRoles = roles
              this.myItems.updateRoleFlags(roles.map(r => r.role_type));
              this.updateSingularEditing(res.isSingle);
              // this.itemEditCtrl.registerFormControlsForDisable();

              if (this.itemBankCtrl.questions.length === 0 && !this.editingDisabled.isSingularReadOnly()) {
                this.itemBankCtrl.createNewQuestion(ItemType.ITEM); // todo:RACE there are cases where don't want to add items to the item set by default
              }
              this.currentHash = res.hash;
              if (this.frameworkCtrl.isFrameworkView) {
                this.itemBanksUsed = res.childItemBanksInfo;
              }
              this.itemBankCtrl.currentSetName.setValue(res.name);
              this.itemBankCtrl.currentSetDescription = res['description'];
              this.itemBankCtrl.currentSetSlug = res['slug'];
              this.isLoading = false;
              if(this.editingDisabled.isSingularReadOnly()) {
                return;
              }
              if (this.itemBankCtrl.targetQuestionLabel) {
                this.itemBankCtrl.selectQuestionByLabel(this.itemBankCtrl.targetQuestionLabel);
              } 
              else if (this.itemBankCtrl.targetTargetItemId) {
                this.itemBankCtrl.selectQuestionById(+this.itemBankCtrl.targetTargetItemId);
              } 
              else {
                const firstQ = this.itemBankCtrl.findFirstQuestion();
                if(firstQ) {
                  this.itemBankCtrl.selectQuestion(firstQ);            
                } 
              }
              if(this.frameworkCtrl.isFrameworkView) {
                this.itemBankCtrl.switchToFrameworkPublishingView();
              }

              // //Disable controls for read-only
              // this.auth.apiGet(this.routes.TEST_AUTH_GROUP_ROLES, res.group_id, { query: {group_id: res.group_id}}).then((roles)=>{
              //   for(const r of roles) {
              //     if(r.role_type === 'test_item_author_rev' ) {
              //       this.authScopeSettings.setSetting(AuthScopeSetting.DISABLE_EDITING, true);
              //     } 
              //     else {
              //       //For clarity. The default should be false anyway.
              //       this.authScopeSettings.setSetting(AuthScopeSetting.DISABLE_EDITING, false);
              //       break;
              //     }
              //   }
              // });


              let value = res.showComments;
              if (typeof value === 'undefined') {
                value = true;
              }
              this.itemBankCtrl.showComments.setValue(!!(+value));
              this.itemBankCtrl.showComments.valueChanges.subscribe(value => {
                this.auth.apiPatch(this.routes.TEST_AUTH_SHOW_COMMENTS, this.auth.getUid(), {value: value ? 1 : 0})
              });

              if(this.itemFilterCtrl.filterQuestionIds) {
                this.itemFilterCtrl.updateItemFilter();
              }
          }
        );

        }).then(() => {
          if (!this.itemBankCtrl.questions) return;
          // const questionIds = this.itemBankCtrl.questions.map(q => q.id);
          // this.auth.apiFind(this.routes.TEST_AUTH_ITEM_IMPRESSION, { query: { question_id: String(questionIds) }})
          //   .then((res) => {
          //     if (res && res.data) {
          //       this.itemBankCtrl.questionImpressions = res.data.reduce((acc, cv: IQuestionImpressionConfig) => {
          //         return { ...acc, [cv.question_id]: cv }
          //       }, {})
          //     }
          //   })
        }).catch((error)=>{
          console.log(error);
          if (error.data){
            alert(`You do not have access to authoring group. (${error.data.id}) ${error.data.description}`)
          }
          else{
            alert('Failure while loading test design')
          }
          this.isLoadAccessFailed = true;
        });
  }
  isLoadAccessFailed:boolean;
  initCustomTaskSet(queryParams:{snapshotFilter:string}) {
    this.userSub = this.auth.user().subscribe(userInfo => {
      if (userInfo && !this.isUserInited) {
        this.isUserInited = true;
        this.loadCustomTaskSet(queryParams);
      }
    })
  }
  updateSingularEditing(isSingle: boolean) {
    if(isSingle && this.editingDisabled.isReadOnly()) { // If read only and single
      this.authScopeSettings.setSetting(AuthScopeSetting.DISABLE_EDITING_SINGULAR, true);
      this.frameworkCtrl.isFrameworkView = true;
      this.itemBankCtrl.switchToFrameworkPublishingView();
    }
  }
  saveChanges(editParam?: boolean) {
    this.isSaveChangedRequired = true;
    try {
      this.saveCurrentQuestionData(undefined, undefined, editParam);
    } catch (e) { }
    this.saveMainBlob((res) => {
      this.isSaveChangedRequired = false;
      this.fakeNeedForSaveAgain();
    });
  }
  saveMainBlob(then: (res: any) => void) {
    this.saveConflictWarningGiven = false;
    this.saveContent();
  }
  async saveItemSetSlugOverride(slug:string) {
    this.isSavingItemSet = true;
    try {
      const patchedRecord = await this.auth.apiPatch(
        this.routes.TEST_AUTH_ITEM_SET_SLUG,
        this.itemBankCtrl.customTaskSetId,
        {slug}
      )
      this.itemBankCtrl.currentSetSlug = patchedRecord.slug
      this.isSavingItemSet = false;
      return patchedRecord
    }
    catch (e){
      alert('You do not have sufficient privileges to update this property.')
      this.isSavingItemSet = false;
    }
  }
  saveItemSet(override?: boolean, info?:{name?:string, description?:string, languages?:string, style_profile?:EStyleProfile}) {
    if (!info){ info = {} }
    this.isSavingItemSet = true;
    this.frameworkCtrl.asmtFmrk.testFormType = this.frameworkCtrl.testFormConstructionMethod.value;
    this.frameworkCtrl.asmtFmrk.questionWordSlug = this.frameworkCtrl.questionWordingFc.value;
    return this.auth.apiPatch(
      this.routes.TEST_AUTH_ITEM_SET,
      this.itemBankCtrl.customTaskSetId,
      {
        ... info,
        oldHash: this.currentHash,
        override
      }
    )
    .then(res => {
      if (res.hash) { this.currentHash = res.hash; }
      if (info.name){ this.itemBankCtrl.currentSetName.setValue(info.name) }
      if (info.description !== null){ this.itemBankCtrl.currentSetDescription = info.description; }
      if (info.languages){this.itemBankCtrl.setActiveItemSetLanguages(info.languages);}
      if (info.style_profile){this.itemBankCtrl.setSelectedStyleProfile(info.style_profile);}
      this.isSavingItemSet = false;
    })
    .catch(e => {
      if (e.message === 'REQ_OVERRIDE') {
        const proceed = confirm('Someone has saved this item set info since you last saved or opened it. Would you like to overwrite their changes?');
        if (proceed) {
          return this.saveItemSet(true, info);
        }
      } 
      else {
        alert('cannot save');
      }
      this.isSavingItemSet = false;
    });
  }

  async syncParameters() {
    if(!this.frameworkCtrl.asmtFmrk.assessmentType) {
      return;
    }

    await this.auth.apiGet(this.routes.TEST_AUTH_QUESTION_SET_PARAMETERS, this.frameworkCtrl.asmtFmrk.assessmentType, {query: {lang: this.frameworkCtrl.asmtFmrk.assessmentLanguage}}).then((standardParamsDb) => {
      if(this.frameworkCtrl.asmtFmrk.standardParameters) {
        const deepCopy = JSON.parse(JSON.stringify(this.frameworkCtrl.asmtFmrk.standardParameters));
        if(!objectsEqual(deepCopy, standardParamsDb)) {
          this.frameworkCtrl.isParamSaveRequired = true;
        }
      }

      this.frameworkCtrl.asmtFmrk.standardParameters = standardParamsDb
      removeParameterDuplicates(this.frameworkCtrl.asmtFmrk.primaryDimensions, this.frameworkCtrl.asmtFmrk.secondaryDimensions, this.frameworkCtrl.asmtFmrk.standardParameters)
    }).catch((err) => {
      console.error(err);
    });
  }

  async saveContent(override?: boolean) {
    let test_design_question_sets;
    if (this.isSavingDisabled()){
      const currentQuestion = this.itemBankCtrl.currentQuestion;
      this.login.quickPopup(`${this.lang.tra('ie_save_locked')} (${currentQuestion ? `${currentQuestion.id}: ${currentQuestion.label }`: ''})`);
      this.isSaveChangedRequired = false;
      return Promise.resolve()
    }
    if (this.frameworkCtrl.isFrameworkView) {
      test_design_question_sets = this.itemBanksUsed.map(itemBank => itemBank.id);
    }
    if (this.frameworkCtrl.isTestFormScoreEntry()) {
      await this.frameworkCtrl.ensureItemInSection();

    }
    this.isSavingItemSet = true;
    this.frameworkCtrl.asmtFmrk.testFormType = this.frameworkCtrl.testFormConstructionMethod.value;
    this.frameworkCtrl.asmtFmrk.questionWordSlug = this.frameworkCtrl.questionWordingFc.value;
    console.log(this.frameworkCtrl.getLinearFormQuestions())
    await this.syncParameters();
    await this.frameworkCtrl.calculateTestFormParams();
    return this.auth.apiPatch(
      this.routes.TEST_AUTH_FRAMEWORKS,
      this.itemBankCtrl.customTaskSetId,
      {
        test_design_question_sets,
        framework: JSON.stringify(this.frameworkCtrl.asmtFmrk),
        oldHash: this.currentHash,
        override,
      }
    )
    .then(res => {
      if (res.hash) {
        this.currentHash = res.hash;
      }
      this.isSaveChangedRequired = false;
      this.isSavingItemSet = false;
      this.frameworkCtrl.isParamSaveRequired = false;
      this.frameworkCtrl.isAsmtStructureSaveRequired = false;
      alert('Changes saved successfully.');
    })
    .catch(e => {
      if (e.message === 'REQ_OVERRIDE') {
        const proceed = confirm('Someone has saved this framework since you last saved or opened it. Would you like to overwrite their changes?');
        if (proceed) {
          return this.saveContent(true);
        }
      } else {
        alert('cannot save');
      }
      this.isSavingItemSet = false;
    });
  }
  fakeItemSetSaveBuffer() {
    this.isLoadingItemSets =  true;
    setTimeout(() => {
      this.isLoadingItemSets =  false;
    }, 400);
  }
  fakeNeedForSaveAgain() {
    if (this.existingFakeSaveDelayTimeout) {
      clearTimeout(this.existingFakeSaveDelayTimeout);
    }
    this.existingFakeSaveDelayTimeout = setTimeout(() => {
      this.isSaveChangedRequired = true;
    }, 5000);
  }
  _updateCurrentQuestionExpectedAnswer() {
    updateCurrentQuestionExpectedAnswer(
      this.itemBankCtrl.getCurrentQuestionContent(), // currentQuestion, 
      this.itemBankCtrl.currentQuestion, // currentMetaContainer, 
      this.frameworkCtrl.identifySingleEntryParams()
    )
  }


  saveAssessmentFramework() {
    this.saveAssessmentFrameworkId();
    if (this.frameworkCtrl.isEditingFramework) {
      this.saveAsmtFwrkEdits();
    }
  }
  saveAssessmentFrameworkId = () => this.saveChanges();

  loadDocument = async  (documentMap:Map<number, IQuestionConfig>, itemId:number, isEnglish:boolean=false) : Promise<IQuestionConfig> => {
    if (itemId){
      const questionRecord = await this.auth.apiGet(
        this.routes.TEST_AUTH_QUESTIONS,
        itemId,
      )
      const questionContent:IQuestionConfig = JSON.parse(questionRecord.config);
      let questionDisplay = questionContent;
      if (!isEnglish){
        questionDisplay = questionContent.langLink
      }
      documentMap.set(+itemId, questionDisplay);
      return questionDisplay;
    }
    throw new Error()
  }

  // Takes an array of document ids and retrived the questions in one call, for reference document
  loadDocuments = async  (documentMap:Map<number, IQuestionConfig>, documentItemIds:number[], isEnglish:boolean=false) : Promise<IQuestionConfig[]> => {
    if (documentItemIds){
      const questionRecords = await this.auth.apiFind(
        this.routes.TEST_AUTH_QUESTIONS,
        {
          query:{
            load_by_question_ids: true,
            question_ids: documentItemIds
          }
        },
      )

      const questionDisplays = []
      const questionRecordsArray = Object.values(questionRecords);
      questionRecordsArray.forEach((questionRecord: any) => {
        const questionContent:IQuestionConfig = JSON.parse(questionRecord.config);
        let questionDisplay = questionContent;
        if (!isEnglish){
          questionDisplay = questionContent.langLink
        }
        documentMap.set(+questionRecord.id, questionDisplay);
        questionDisplays.push(questionDisplay)
      });
      
      return questionDisplays;
    }
    throw new Error()
  }

  saveCurrentQuestionData(forceAllow?: boolean, description?:string, isEditParam?: boolean, isAcceptSugg:number = 0) {
    return this.saveTargetQuestionData(this.itemBankCtrl.currentQuestion, false, forceAllow, description, isEditParam, isAcceptSugg);
  }

  saveTargetQuestionData(targetQuestion: IQuestionConfig, isSavingMultiple?: boolean, forceAllow?: boolean, descriptionOfChange?:string, isEditParam?: boolean, isAcceptSugg:number = 0) {
    if (!isSavingMultiple && this.isSaving) {
      return Promise.resolve();
    }
    if (!forceAllow && this.isSavingDisabled() && !isEditParam){
      if(this.itemBankCtrl.isSuggestionMode(this.itemBankCtrl.getQuestionContent(targetQuestion)) && !forceAllow) {
        return this.saveSuggestions(targetQuestion); //intercept and save suggestions instead
      } else {
        return Promise.resolve();
      }
    }
    return new Promise<void>((resolve, reject) => {
      if (!targetQuestion) {
        return resolve();
      }
      const previousQuestion = targetQuestion;
      let previousQuestionState;
      if (previousQuestion && previousQuestion.id === this.itemBankCtrl.questionStateOnLoad.id) {
        previousQuestionState = this.itemBankCtrl.questionStateOnLoad.state;
      }
      return this.saveQuestionData(previousQuestion, previousQuestionState, false, forceAllow, descriptionOfChange, isEditParam, isAcceptSugg)
      .then(res => resolve(res))
        .catch(e => reject(e));
    });
  }

  restoreCurrentQuestionData() {
    this.setInspecting(false); //Do this before saving since we need to enable editing to load the new revision
    
    const restoreEN = this.restoreENFc.value;
    const restoreFR = this.restoreFRFc.value;
    const restoreParams = this.restoreParamsFc.value;
    const restoreLabel = this.restoreLabel.value;

    //If we are not restoring something, we need to load the previous version of that section first
    if(!restoreEN || !restoreFR || !restoreParams) {
      this.restoreRevision({skipEN: restoreEN, skipFR: restoreFR, skipParams: restoreParams, skipLabel: restoreLabel});
    }
  }

  restoreRevision(skipOptions? : {skipEN: boolean, skipFR: boolean, skipParams: boolean, skipLabel: boolean}){
    const revisionList = this.currentQuestionRevisions.displayList;
    if(revisionList && revisionList.length > 0){
      this.loadQuestionRevision(revisionList[0].id, false, skipOptions);
    }
  }

  loadPreviousRevision() {
    if (this.currentQuestionRevisions && this.itemBankCtrl.currentQuestion){
      const restoration = JSON.parse(this.currentQuestionRevisions.originalQuestion);
      Object.keys(restoration).forEach(prop => {
        this.itemBankCtrl.currentQuestion[prop] = restoration[prop];
      })
    }
  }

  cancelInspect() {
    this.setInspecting(false);
    if (this.currentQuestionRevisions){
      this.currentQuestionRevisions.displayList = null; //Hide the revision history window after cancelling
      this.loadPreviousRevision();
    }
  }

  setInspecting(inspecting: boolean) {
    this.isInspecting = inspecting;
    this.editingDisabled.setEditingDisabled(inspecting || this.itemBankCtrl.isEditingDisabledRequired());
  }

  saveQuestionData(question: IQuestionConfig, previousQuestionState: string, isEditing: boolean= false, forceAllow: boolean = false, change_note?:string, isEditParam?:boolean, isAcceptSugg:number = 0) {
    
    if(this.itemBankCtrl.isSuggestionMode(this.itemBankCtrl.getQuestionContent(question)) && !forceAllow) {
      return this.saveSuggestions(question); //intercept and save suggestions instead
    }
    
    return new Promise<void>((resolve, reject) => {
      if (!question) {
        return resolve();
      }
      if (!forceAllow && this.isSavingDisabled() && !isEditParam){
        return resolve(null);
      }
      
      // add function to extract asset ids
      let itemAssetsVersionIds = this.extractAssetVersionIds(question);
      this.itemBankCtrl.sanitizeQuestionBeforeSave(question);
      const config = JSON.stringify(question);
      const id = question.id;
      if (config === previousQuestionState) {
        return resolve();
      } 
      const diff = DeepDiff(JSON.parse(previousQuestionState || '{}'), question, ignoreFromDiff);

      // If nothing was actually updated, don't include this save in the log
      const AUTO_SAVE_PROPS = ['updated_on', 'readSel', 'isTrackingChanges']

      const isSaveLogged = diff.some(d => {
        const potentialProp = d.path[0] == "langLink" ? d.path[1] : d.path[0]
        return !AUTO_SAVE_PROPS.includes(potentialProp)
      })
      
      // Save the changes made
      this.itemDiffCtrl.refreshDiffs(question);

      const question_label = question.label;
      const is_editing = isEditing ? 1 : 0;
      this.isSaving = true;

      const parent_id = this.itemBankCtrl.getParentId(question);

      return this.auth.apiPatch(
        this.routes.TEST_AUTH_QUESTIONS,
        id,
        {
          question_label,
          config,
          order: this.itemBankCtrl.getOrder(question),
          diff: JSON.stringify(diff),
          is_editing,
          change_note,
          last_touched_by: this.auth.getDisplayName(),
          item_asset_version_ids: itemAssetsVersionIds,
          parent_id,
          item_asset_ids: this.getAssetIDs(question),
          is_accept_sugg: isAcceptSugg, // If question is modified via accepting a suggestion, rather than directly
          lang: this.lang.c(),
          isSaveLogged
        }
      )
      .then(res => {
        this.isSaving = false;
        this.itemBankCtrl.questionStateOnLoad.id = id;
        this.itemBankCtrl.questionStateOnLoad.state = config;

         // Re-save how any existing highlight comments map onto the latest version of the question
        if (res.versionId) {
          this.highlighter.saveHighlightInfoMap({test_question_id: id, test_question_version_id: res.versionId});
          if(this.itemBankCtrl.versionIdMap) this.itemBankCtrl.versionIdMap[id] = res.versionId;
        }
          this.itemBankCtrl
        if (this.itemBankCtrl.currentQuestion && this.itemBankCtrl.currentQuestion.id === this.itemBankCtrl.questionStateOnLoad.id){
          console.log('viewing same question')
          this.itemBankCtrl.currentQuestion.updated_on = (new Date()).toISOString();
        }
        resolve(res);
      })
      .catch(e => {
        this.isSaving = false;
        reject(e);
      });
    });
  }

  private getAssetIDs(question: IQuestionConfig): number[] {
    let assetIdSet = new Set<number>();
    const getAssetIds = (elements: IContentElement[], assetIdSet: Set<number>) => {
      if (elements) {
        for(const element of elements) {
          if(element.assetId) {
            assetIdSet.add(element.assetId);
          }
          getAssetIds(getElementChildren(element, {includeSolution: true, includeDnDMoveableImages: true, includeDnDTargets: true, includeVoiceover: true}), assetIdSet);
        }
      }
    }
    getAssetIds(question.content, assetIdSet);
    return Array.from(assetIdSet);
  }
  
  autoSaveQuestionData() {
    // !this.itemBankCtrl.isEditActivationPending() &&
    if (this.itemBankCtrl.currentQuestion && !this.isInspecting) {
      const question = this.itemBankCtrl.currentQuestion;
      this.voiceoverState.updateAllSyncedStatuses(question);
      let previousQuestionState;
      if (this.itemBankCtrl.questionStateOnLoad && this.itemBankCtrl.currentQuestion.id === this.itemBankCtrl.questionStateOnLoad.id) {
        previousQuestionState = this.itemBankCtrl.questionStateOnLoad.state;
      }
      this.saveQuestionData(question, previousQuestionState, true);
      this.autoSaveSub.next();
    }
  }

  isRevisionsOpen:boolean;
  loadCurrentQuestionRevisions() {
    if (this.isRevisionsOpen){
      this.isRevisionsOpen = false;
      return 
    }
    this.isRevisionsOpen = true;
    this.currentQuestionRevisions = {};
    this.currentQuestionRevisions.originalQuestion = JSON.stringify(this.itemBankCtrl.currentQuestion)
    if (this.currentQuestionRevisions.displayList) {
      this.currentQuestionRevisions.displayList = null;
      if(this.isInspecting) {
        this.cancelInspect();
      }
    } else {
      this.isLoadingRevisions = true;
      this.auth.apiFind(
        this.routes.TEST_AUTH_QUESTION_REVISIONS,
        { query: {test_question_id: this.itemBankCtrl.currentQuestion.id} }
      )
      .then(res => {
        const displayList = [];
        let lastDate: string;
        for (let i = 0; i < res.data.length; i++) {
          const entry = res.data[i];
          let createdOnDate = moment(entry.created_on).format('MMMM DD, YYYY');
          const createdOnTime = moment(entry.created_on).format('h:mma');
          if (createdOnDate === lastDate) {
            createdOnDate = '';
          } else {
            lastDate = createdOnDate;
          }
          displayList.push({
            ... entry,
            createdOnDate,
            createdOnTime,
          });
        }
        this.currentQuestionRevisions.displayList = displayList;
        this.isLoadingRevisions = false;
      })
      .catch(e => {
        alert('Could not load question revisions');
        this.isLoadingRevisions = false;
      });
    }
  }


  extractAssetVersionIds(question: IQuestionConfig) {
    let accumlator_array = [];
    extractQuestionAssetVersionIds(question.content, accumlator_array);
    if (question.langLink) {
      extractQuestionAssetVersionIds(question.langLink.content, accumlator_array);
    }
    return accumlator_array;
  }

  saveAsmtFwrkEdits() {
    let frameworkPayloadBase: Partial<IAssessmentFrameworkDef> = {
      caption: this.frameworkCtrl.asmtFmrk.caption,
      subcaption: this.frameworkCtrl.asmtFmrk.subcaption,
      timeLastTouch: serverTimestamp(),
    };
    throw new Error('wip');
  }


  loadQuestionRevision(id: number, inspecting: boolean, skipOptions?: {skipEN: boolean, skipFR: boolean, skipParams: boolean, skipLabel: boolean}) {
    console.log('loadQuestionRevision');
    this.setInspecting(inspecting);
    if (id === this.currentQuestionRevisions.restoringToId) {
      return;
    }

    if (id === this.currentQuestionRevisions.restoredToId && 
      this.equivalentSkipOptions(skipOptions, this.currentQuestionRevisions.skippedOptions)) {
      return;
    }

    this.currentQuestionRevisions.restoringToId = id;
    const completeLoad = () => {
      this.currentQuestionRevisions.restoringToId = null;
      
      //!inspecting indicates we are leaving inspect mode (restoring) when loading
      if(!inspecting) {
        //Only save once the load has been completed
        this.saveCurrentQuestionData();
        this.currentQuestionRevisions.displayList = null;
        this.itemEditCtrl.closeElementRestore(); //close the restore options dialog
      }
    };

    this.auth
      .apiGet( this.routes.TEST_AUTH_QUESTION_REVISIONS, id )
      .then(res => {
        console.log('load revision', id, res, this.currentQuestionRevisions);
        const questionRestored = JSON.parse(res.config);
        const targetQuestion = this.itemBankCtrl.currentQuestion;
        questionRestored.id = targetQuestion.id;
        Object.keys(questionRestored).forEach(key => {
          if(skipOptions) {
            //If we are restoring these keys, that means we shouldn't load the previous value of them.
            if(skipOptions.skipParams && key === 'meta') {
              return;
            }
            if(skipOptions.skipLabel && key === 'label') {
              return;
            }
            if(skipOptions.skipFR && key === 'langLink') {
              return;
            }
            if(skipOptions.skipEN && key !== 'langLink' && key !== 'meta') {
              return;
            }
          }
          targetQuestion[key] = questionRestored[key];
          // During inspect preview or if restoring with the label, update the label on right panel also
          if (!skipOptions || !skipOptions.skipLabel) this.itemBankCtrl.currentQuestionLabel.setValue(questionRestored.label);
        });
        this.currentQuestionRevisions.restoredToId = id;

        this.currentQuestionRevisions.skippedOptions = skipOptions;
        // Somewhere here, take care of the highlights...
        completeLoad();
      })
      .catch(e => {
        alert('Could not inspect question revision');
        completeLoad();
      });
  }

  equivalentSkipOptions(a?: {skipEN: boolean, skipFR: boolean, skipParams: boolean, skipLabel: boolean}, b?: {skipEN: boolean, skipFR: boolean, skipParams: boolean, skipLabel: boolean}): boolean {
    //not having a skip option implies all the skip options were false.
    const skipKeys = ['skipEN', 'skipFR', 'skipParams', 'skipLabel'];
    return (!a && !b) || 
      (a && b && _.every(skipKeys, key => a[key] == b[key])) ||
      (!a && b && _.every(skipKeys, key => !b[key])) ||
      (a && !b && _.every(skipKeys, key => !a[key]));
  }

  isSavingDisabled = () => {
    return this.editingDisabled.isReadOnly(this.itemBankCtrl.isQLockAvail());
  };

  saveRevisionMessage(revision, message: string) {
    this.auth.apiPatch(this.routes.TEST_AUTH_QUESTION_REVISIONS, revision.id, {message}).then(() => {
      revision.message = message;
    })
  }

  createSuggestionState() {
    const suggestionState = this.itemBankCtrl.getCurrentQuestionContent();

    this.isSuggesting = true;
    return this.auth.apiCreate(this.routes.TEST_AUTH_SUGGESTIONS,{
      test_question_id: this.itemBankCtrl.currentQuestion.id,
      origin_question_version_id: this.itemBankCtrl.currentQuestion.versionId,
      config: JSON.stringify(suggestionState),
      lang: this.lang.c()
    }).then( () => {
      this.isSuggesting = false;
      if(!this.itemComponentEdit.usingEditingMode()) {
        this.itemDiffCtrl.refreshSuggestions();
      }
      // When entering suggestion mode, clone the mappings of comment to string intervals so that they can be adjusted inependently for the suggestion
      this.highlighter.initSuggHighlightMap()
    }).catch(() => {
      this.isSuggesting = false;
    });
  }

  saveSuggestions(question?, isRejectSugg: number = 0) {
    if((!question && !this.itemBankCtrl.currentQuestion) || !this.itemComponentEdit.hasSuggestions()) {
      return Promise.resolve();
    }

    if(!question) {
      question = this.itemBankCtrl.currentQuestion;
    }
    const prevState = this.itemComponentEdit.suggestionStateOnLoad;
    const suggestionState = this.itemComponentEdit.suggestion.state;

    this.itemBankCtrl.sanitizeQuestionBeforeSave(suggestionState, true);

    // Changes are between current suggestion and previous suggestion
    const diffsFromPrevSugg = this.itemComponentEdit.deepDiff(prevState, suggestionState);

    const hasNonChangeCounterDiffFromPrev = _.some(diffsFromPrevSugg, (d) => {
      return _.last(d.path) !== '_changeCounter';
    });

    if(!hasNonChangeCounterDiffFromPrev) {
      return Promise.resolve();
    }

    const changes = []; //For tracking author and time of individual changes
    for(const diff of diffsFromPrevSugg) {
      const element = this.itemComponentEdit.getElementFromDiff(diff);
      const entryId = element?.entryId;
      if(entryId) {
        if(diff.kind === 'E') {
          changes.push({entryId, prop: diff.path[diff.path.length - 1]})
        } else {
          changes.push({entryId});
        }
      }
    }

    // Compile annotations of text fields that differ from the original string - label who is responsible for each change
    const annotations = {}
    const originalState = this.itemComponentEdit.originalQuestionState
    const diffsFromOg = this.itemComponentEdit.deepDiff(originalState, suggestionState);
    this.itemDiffCtrl.refreshDiffs(question);
    for(const diff of diffsFromOg) {
      const element = this.itemComponentEdit.getElementFromDiff(diff);
      const entryId = element?.entryId;
      if (!entryId) continue
      //Applicable if the field is a text field (an editable string), and it has been changed
      if(entryId && diff.kind === 'E') {
        const prop = diff.path[diff.path.length - 1]
        const isTextDiffProp = this.itemComponentEdit.isTextDiffProp(prop, element)
        if (isTextDiffProp) {
          const diffsForElement = this.itemComponentEdit.getDiffs(entryId);
          const diffForProp = diffsForElement.find( d => d.path[d.path.length-1] === prop);
          const annotation = this.itemComponentEdit.getRawTextDiffs(diffForProp, entryId, prop)
          if (!annotations[entryId]) annotations[entryId] = {}
          annotations[entryId][prop] = annotation
        } 
      }
    }
    
    this.isSuggesting = true;
    this.isSaving = true;
    return this.auth.apiPatch(this.routes.TEST_AUTH_SUGGESTIONS, this.itemComponentEdit.suggestion?.id, {
      test_question_id: question.id,
      origin_question_version_id: question.versionId,
      config: JSON.stringify(suggestionState),
      lang: this.lang.c(),
      changes,
      annotations,
      is_reject_sugg: isRejectSugg
    }, {
      query: {question_id: question.id} 
    }).then( (res) => {
      this.isSuggesting = false;
      this.isSaving = false;
      this.itemComponentEdit.suggestionStateOnLoad =  _.cloneDeep(suggestionState);
      // Re-save how any existing highlight comments map onto the latest version of the suggestion
      this.highlighter.saveHighlightInfoMap({test_question_id: question.id, test_question_suggestion_version_id: res.version_id})
    }).catch(() => {
      this.isSuggesting = false;
      this.isSaving = false;
    });
  }

}