import * as _ from 'lodash';
import * as moment from 'moment-timezone';

import { Component, OnInit, OnDestroy, ViewEncapsulation, ViewChild, ElementRef } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { FormControl } from '@angular/forms';
import { ScrollService } from '../../core/scroll.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { LangService } from '../../core/lang.service';
import { Observable, Subscription } from 'rxjs';
import { RoutesService } from '../../api/routes.service';
import {AuthService, getFrontendDomain} from '../../api/auth.service';
import { ItemMakerService, IItemSetDef } from '../item-maker.service';
import { IAuthoringGroup } from '../item-maker.service';
import { IMenuTabConfig } from '../../ui-partial/menu-bar/menu-bar.component';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { AuthRolesService } from '../auth-roles.service';
import { IItemTag } from '../item-tag/item-tag.component';
import { map, startWith } from 'rxjs/operators';
import { SidepanelService } from 'src/app/core/sidepanel.service';
import { testAuthPanels } from './../../core/main-nav/panels/test-auth';
import { mtz } from 'src/app/core/util/moment';
import { IAuthGroupOption } from '../view-im-group-access/model/types';
import { EditingDisabledService } from '../editing-disabled.service';
import { GridApi, ColDef, GridOptions } from 'ag-grid-community';
import { agGridNumberSort } from '../../core/util/sort';

enum ViewTypes {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED'
}



const dateSort = (d1, d2) => {
  const m_d1 = moment(d1);
  const m_d2 = moment(d2);
  if (m_d1.isBefore(m_d2)) {
    return 1;
  } 
  else {
    return -1;
  }
}

const ASSIGNMENT_SORT_UID = (a,b, uid) => {
  if (+a.assigned_uid === +uid) {
    if (+a.assigned_uid === +b.assigned_uid) {
      return dateSort(a.created_on, b.created_on);
    }
    return -1;
  } 
  else {
    if (+a.assigned_uid === +b.assigned_uid) {
      return dateSort(a.created_on, b.created_on);
    }
    return 1;
  }
}

export enum AuthoringModal {
  MANAGE_GROUP      = 'manage-group',
  MANAGE_ITEM_BANK     = 'manage-item-bank',
  MANAGE_GROUP_TAGS = 'manage-group-tags'
}

export enum AuthProfileView {
  CODES = 'assessment-codes',
  MARKING = 'scoring-policy',
  ADMIN_WINDOW = 'test-window',
  REPORTING = 'reporting',
  STYLE     = 'style',
}

export enum AuthView {
  TW_ALLOC  = 'tw-alloc',
  DESIGNS = 'designs',
  SETS    = 'sets',
  PROFILES  = 'profiles',
  GROUPS  = 'groups',
  POLICIES = 'batch-allocation-policies',
}

export enum AuthTwtarSelection {
  ACADEMIC_YEAR = 'ac_year',
  TW_TYPE_SLUG = 'tw_type',
  TW_ID = 'tw_id',
  TWTAR_ID = 'twtar_id',
}


@Component({
  selector: 'view-im-dashboard',
  templateUrl: './view-im-dashboard.component.html',
  styleUrls: ['./view-im-dashboard.component.scss'],
  encapsulation: ViewEncapsulation.Emulated
})
export class ViewImDashboardComponent implements OnInit, OnDestroy {

  @ViewChild('tagInput') tagInput: ElementRef;
  constructor(
    public lang: LangService,
    private loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private sidePanel: SidepanelService,
    private router: Router,
    private routes: RoutesService,
    private route: ActivatedRoute,
    private auth: AuthService,
    public myItems: ItemMakerService,
    private scrollService: ScrollService,
    public whitelabel: WhitelabelService,
    private pageModalService: PageModalService,
    private authRoles: AuthRolesService,
    public editingDisabled: EditingDisabledService,
  ) { }
    
  AuthoringModal = AuthoringModal;
  
  public breadcrumb = [];
  public questionSets:IItemSetDef[] = [];
  public frameworks:IItemSetDef[] = [];
  public groups:IAuthoringGroup[] = [];
  public isCreatingNewQuestionSet:boolean;
  public isCreatingNewTestAuthor:boolean;
  public isCreatingNewTestDesign:boolean;
  public authRoleTypes;
  public AuthView = AuthView;
  public AuthProfileView = AuthProfileView;

  public contentSearchQuery = new FormControl();
  private routeSub:Subscription;
  private isUserInited:boolean;
  isShowDetails: boolean = false;
  isShowDetailsTable: boolean = false;

  public pageModal: PageModalController;
  
  filteredTags: Observable<IItemTag[]>;
  searchTags: IItemTag[] = [];
  compositeAsmtTemplateQsId: number;
  viewArchived: boolean = false;//to set if currently viewing archived items or not
  viewArchivedLoading: boolean;//to lock toggle button to avoid errors
  
  public searchItems: any[] = [];
  public isItemSearchById:boolean;
  public isTestDesignSearchById: boolean;

  isCreatingNewAuthGroup: boolean;
  openAuthGroupOptions: IAuthoringGroup = null;
  isItemSetCreateSaving: boolean;
  isAuthGroupCreateSaving: boolean;
  itemSetCreateError:string;
  testAuthorCreateError:string;
  itemSetCreateForm = {
    slug: new FormControl(),
    name: new FormControl(),
    description: new FormControl(),
    group_id: new FormControl(),
  };
  isTestDesignCreateSaving:boolean;
  testDesignCreateError:string;
  authGroupCreateError:string;
  testDesignCreateForm = {
    slug: new FormControl(),
    name: new FormControl(),
    description: new FormControl(),
    group_id: new FormControl(),
  }
  groupCreateForm = {
    description: new FormControl()
  };
  issues;
  itemBankDescrFilter: string;
  itemBankGroupFilter: string;

  authProfileViews: IMenuTabConfig<AuthProfileView>[] = [
    {id: AuthProfileView.CODES, caption: 'Assessment Codes' },
    {id: AuthProfileView.ADMIN_WINDOW, caption: 'Admin. Window Profiles' },
    {id: AuthProfileView.MARKING, caption: 'Scoring Policies' },
    {id: AuthProfileView.REPORTING, caption: 'Reporting / Cut Score Profiles' },
    {id: AuthProfileView.STYLE, caption: 'Style Profiles' },
  ];
  
  assessmentViews: IMenuTabConfig<ViewTypes>[] = [
    {id: ViewTypes.ACTIVE, caption: 'Active' },
    {id: ViewTypes.ARCHIVED, caption: 'Archived' },
  ];
  outstandingIssues: IMenuTabConfig<ViewTypes>[] = [
    {id: ViewTypes.ACTIVE, caption: 'Active' },
    {id: ViewTypes.ARCHIVED, caption: 'Resolved' },
  ];

  selectedAssessmentTab = ViewTypes.ACTIVE
  selectAssessmentTab(id:ViewTypes){
    this.selectedAssessmentTab = id;
  }

  showActions: {[key: string]: boolean} = {};
  
  private routeDataSubscription: Subscription;
  view: string;

  // AG Grid properties
  gridApiFormAllocation: GridApi;
  gridColumnApiFormAllocation;
  defaultColDef: ColDef = {
    filter: true,
    sortable: true,
    resizable: true
  };

  gridOptionsFormAllocation: GridOptions = {
    rowSelection: 'single',
    enableCellTextSelection: true,
    suppressPaginationPanel: false
  };
  gridOptionsAsmtCodeFilter: GridOptions = {
    rowSelection: 'multiple',
    enableCellTextSelection: false,
    suppressPaginationPanel: false
  };

  formAllocationColumnDefs: ColDef[] = [
    { field: 'id', headerName: 'ID', width: 130 },
    { field: 'type_slug', headerName: 'Assessment Code', width: 200 },
    { field: 'form_code', headerName: 'Form', width: 80 },
    { 
      field: 'test_date_start', 
      headerName: 'Date', 
      width: 140,
      valueGetter: (params) => {
        if (params.data.test_date_start) {
          return mtz(params.data.test_date_start).format('MMM DD (ddd)');
        }
        return '';
      }
    },
    { 
      field: 'test_date_start', 
      headerName: 'Time', 
      width: 100,
      valueGetter: (params) => {
        if (params.data.test_date_start) {
          return mtz(params.data.test_date_start).format('HH:mm');
        }
        return '';
      }
    },
    { field: 'test_duration', headerName: 'Dur.', width: 80 },
    { 
      field: 'signoffProgress', 
      headerName: 'Signoffs', 
      width: 80,
      valueGetter: (params) => {
        if (params.data.signoffProgress && params.data.signoffProgress.progress !== undefined) {
          return Math.floor(params.data.signoffProgress.progress * 100) + '%';
        }
        return '0%';
      }
    },
    { 
      field: 'is_active', 
      headerName: 'Active Status', 
      width: 100,
      valueGetter: (params) => {
        if (params.data.is_active === 1) {
          return 'ACTIVE_FULL';
        }
        if (params.data.is_active_for_auth === 1) {
          return 'AUTH_SETUP';
        }
        return 'INACTIVE';
      }
    },
    { field: 'is_print', headerName: 'Print?', width: 70 },
    { field: 'lang', headerName: 'Language', width: 70 },
    { field: 'long_name', headerName: 'Descriptive Name', width: 400 },
  ];

  // Add these new properties
  public selectedAssessmentCodes: string[] = [];
  public allAssessmentCodes: string[] = [];
  public isFilterModalOpen: boolean = false;
  public filteredTestDesigns: any[] = [];
  
  // Add ag-grid properties for the filter modal
  public filterModalGridApi: GridApi;
  public filterModalColumnDefs: ColDef[] = [
    { 
      field: 'code', 
      headerName: 'Assessment Code', 
      checkboxSelection: true,
      headerCheckboxSelection: true,
      width: 300
    }
  ];
  public filterModalRowData: any[] = [];

  ngOnInit() {
    this.authRoleTypes = this.authRoles.getAuthRoles();
    this.loginGuard.activate();
    this.scrollService.scrollToTop();
    this.sidePanel.activate(testAuthPanels)
    this.routeSub = this.route.params.subscribe(routeParams => {
      this.initRouteView();
    });
    this.initBreadcrumb();
    this.initTwtar();
    this.initAssessmentProfiles()
    this.routeDataSubscription = this.route.data.subscribe((data: any) => {
      this.view = data.view;
      this.initBreadcrumb();
    });
    this.pageModal = this.pageModalService.defineNewPageModal();
    
    // Add this to load saved filter from localStorage
    this.loadSavedFilter();
  }

  initAssessmentProfiles(){
    if (this.authProfileViews.length == 1){
      this.selectedAuthProfileView = this.authProfileViews[0].id
    }
  }


  //////// TWTAR
  twtarAcademicYears =  []
  twTypeSlugs =  []
  twtarTypeSlugs =  []
  testWindows =  []
  testDesigns =  []
  twTdTypes =  []
  twTypeToTwtarType:Map<string, Set<string>> = new Map()
  isSaving = false
  isEditPermission = false
  isAllocatingNewTwtarTd = false
  isCondensedTwtarList = false
  twtarAcademicYearSelected =  null
  twTypeSlugSelected =  null
  testWindowSelected =  null
  testDesignSelected =  null
  newTwtarForm:any = null
  init__tw_id?:number | string
  init_twtar_id?:number | string
  availableTestDesigns = {} // map by tw type slug
  isTwtarInited:boolean;
  isAdminWindowPanelCollapsed = false;
  isFormPanelCollapsed = false;

  async initTwtar(){
    this.twTdTypes = await this.auth.apiFind('public/test-auth/twtar/tw-td-types', {});
    const testWindows = await this.auth.apiFind('public/test-auth/twtar/test-windows', {});
    this.testWindows = testWindows.map(r => {
      try {
        r.title = (JSON.parse(r.title || '{}'))['en']
      }
      catch(e){}
      return r 
    })
    this.mapTwTypeToTwtarType(this.twTdTypes);
    this.twtarAcademicYears = [... new Set(testWindows.map(r => r.academic_year))].sort()
    this.twTypeSlugs = [... new Set(testWindows.map(r => r.type_slug))].sort()
    this.twtarTypeSlugs = [... new Set(this.twTdTypes.map(r => r.type_slug))].sort()
    this.initFormAllocFilter()
    this.isTwtarInited = true
  }
  initFormAllocFilter(){
    try {
      const queryMap = this.route.snapshot.queryParamMap
      this.twtarAcademicYearSelected = queryMap.get(AuthTwtarSelection.ACADEMIC_YEAR);
      this.twTypeSlugSelected = queryMap.get(AuthTwtarSelection.TW_TYPE_SLUG);
      this.init__tw_id = queryMap.get(AuthTwtarSelection.TW_ID);
      this.init_twtar_id = queryMap.get(AuthTwtarSelection.TWTAR_ID);
    }
    catch(e){}

    // load from cache if now query parameters
    if (!this.twtarAcademicYearSelected){
      try {
        this.twtarAcademicYearSelected = localStorage.getItem('twtarAcademicYearSelected') || null;
        this.twTypeSlugSelected = localStorage.getItem('twTypeSlugSelected') || null;
      }
      catch(e){}
    }

    // select test window
    if (this.init__tw_id){
      for (let tw of this.testWindows){
        if (tw.id == this.init__tw_id){
          this.selectTW(tw);
          this.init__tw_id = undefined;
        }
      }
    }
  }
  onFormAllocFilterUpdate(){
    localStorage.setItem('twtarAcademicYearSelected', this.twtarAcademicYearSelected);
    localStorage.setItem('twTypeSlugSelected', this.twTypeSlugSelected);
    // also reflect in query parameters
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { 
        [AuthTwtarSelection.ACADEMIC_YEAR]: this.twtarAcademicYearSelected,
        [AuthTwtarSelection.TW_TYPE_SLUG]: this.twTypeSlugSelected,
        [AuthTwtarSelection.TW_ID]: this.testWindowSelected?.id,
        [AuthTwtarSelection.TWTAR_ID]: this.testDesignSelected?.id,
      },
      queryParamsHandling: 'merge', // preserves existing params
    });
  }
  mapTwTypeToTwtarType(twTdTypes){
    const mapping = this.twTypeToTwtarType
    for (let twTdType of twTdTypes){
      if (!mapping.has(twTdType.tw_type_slug)){
        mapping.set(twTdType.tw_type_slug, new Set)
      }
      mapping.get(twTdType.tw_type_slug).add(twTdType.type_slug);
    }
  }
  async selectTW(tw){
    this.testWindowSelected = tw;
    this.newTwtarForm = null
    this.testDesignSelected = null
    await this.reloadTwtar();
    this.onFormAllocFilterUpdate()
    // Apply the filter after loading the test designs
    this.applyFilter();
  }
  changeDuration(twtar){
    this.loginGuard.quickPopup('Duration edits are currently disabled on the authoring dashboard. Please access through the Test Controller view.')
  }
  renderProgressPerc(num:number){
    return (num ? (Math.floor(num*100)+'%') : '0%');
  }
  printVersionEditStart(twtar){
    twtar._isPrintVersionEdit = true
    twtar._printVersionEdit = {
      is_print: !!twtar.is_print,
      _framework_id: twtar._framework_id,
      _print_configs: twtar._print_configs || [],
    }
  }
  getPrintEdit(){
    return this.testDesignSelected._printVersionEdit
  }
  printVersionEditCancel(twtar){
    twtar._isPrintVersionEdit = false;
    twtar._printVersionEdit = null;
  }
  addArrEl(arr){
    arr.push({})
  }
  removeArrEl(arr, i){
    arr.splice(i, 1)
  }
  async printVersionEditSave(twtar){
    const config = twtar._printVersionEdit
    const {is_print, _framework_id, _print_configs} = config
    await this.auth.apiPatch(
      'public/test-ctrl/test-design-print', 
      twtar.id, 
      { is_print, _framework_id, _print_configs }, 
      { query: {
        tc_group_id: twtar.tc_group_id,
        test_window_id: twtar.test_window_id,
      }}
    )
    twtar.is_print = is_print
    twtar._framework_id = _framework_id
    twtar._print_configs = _print_configs
    twtar._isPrintVersionEdit = false
  }
  isTDSelectedPubAlloc(){
    if (this.testDesignSelected){
      const td_id = this.testDesignSelected.test_design_id
      if (!td_id || td_id == 0 || td_id == '0'){
        return false;
      }
      return true
    }
    return false;
  }

  comingSoon() {
    this.loginGuard.quickPopup('Feature disabled')
  }
  getTwtarLang(twtar){
    return twtar.lang // || 'en'
  }
  getTwtarResourceInfo(twtar){
    return twtar._resourceInfo || {}
  }
  getTwtarItemSetId(twtar){
    return twtar.display_item_set_id
  }
  isCurrentAllocatedToWindow(){
    return !!this.getTwtarItemSetId(this.testDesignSelected)
  }
  isTwtarItemSetDiscrep(twtar){
    if (twtar.source_item_set_id){
      if (twtar.display_item_set_id !== twtar.source_item_set_id){
        return true
      }
    }
    return false;
  }
  renderDate(db_date:string){
    return mtz(db_date).format(this.lang.tra('datefmt_timestamp'))
  }
  getTwtarStartDateCondensed(twtar){
    if (twtar.test_date_start){
      return mtz(twtar.test_date_start).format('YYYY-MM-DD')
    }
  }
  getTwtarStartTime(twtar){
    if (twtar.test_date_start){
      return mtz(twtar.test_date_start).format('HH:mm')
    }
  }
  sortTwtarByDate(){
    console.log('sortTwtarByDate')
    this.testWindowSelected.testDesigns = _.orderBy(this.testWindowSelected.testDesigns, ['test_date_start', 'type_slug', 'form_code'])
    
    // Re-apply the filter after sorting
    this.applyFilter();
  }
  isTwtarTdSuperceded(){
    try {
      return this.testDesignSelected?.publishedDesignInfo?.isSuperceded
    }
    catch(e){}
    return false
  }
  getTwtarStartDate(twtar){
    if (twtar.test_date_start){
      return this.renderDate(twtar.test_date_start)
    }
    else {
      return '(No Specific Date Controls)'
    }
  }
  getTwtarLanguageInfo(twtar){
    let str = '';
    const isSameLangSchool = (twtar.req_sd_lang==1);
    const isDiffLangSchool = (twtar.req_sd_lang_not==1);

    if(twtar.lang == 'en'){  
      str += 'English Language';
      if (isSameLangSchool){
        str+=' (English schools only)';
      }
      if (isDiffLangSchool){
        str+=' (Francophone schools only)';
      }
    }
    else if(twtar.lang == 'fr'){  
      if (isDiffLangSchool && isSameLangSchool){
        str+='French (but conflicting flags)';
      }
      else if (isSameLangSchool){
        str+='Francophone';
      }
      else if (isDiffLangSchool){
        str+='French-Immersion';
      }
      else {
        str+='French Language'
      }
    }
    else {
      str += '(Unknown Language)'
    }
    return str;
  }
  getCoverPageLink(twtar){
    const itemSetId = this.getTwtarItemSetId(twtar)
    return `/${twtar.lang || this.lang.c()}/test-auth/cover-page-preview/${twtar.id}/${twtar.test_design_id}/${itemSetId}`;
  }
  getTwtarResourceLink(twtar){
    const resourceInfo = this.getTwtarResourceInfo(twtar);
    return `/${twtar.lang || this.lang.c()}/test-auth/shared-test-version/${resourceInfo.qs_id}/${resourceInfo.td_id}`;
  }
  getTwtarAssessmentLink(twtar){
    const itemSetId = this.getTwtarItemSetId(twtar)
    return `/${twtar.lang || this.lang.c()}/test-auth/shared-test-version/${itemSetId}/${twtar.test_design_id}`;
  }
  getTwtarFrameworkLink(twtar){
    const id = this.getTwtarItemSetId(twtar);
    const lang = this.getTwtarLang(twtar);
    if (id){
      return this.getFrameworkRoute(id, lang);
    }
  }
  isTwSelected(tw){
    return (this.testWindowSelected === tw)
  }
  isTwtarSelected(twtar){
    return (this.testDesignSelected === twtar)
  }
  checkFrameworkForGroupingAlignment(framework:IItemSetDef, twtarTypeSlug:string, twtarTypeSlugToGroupIds:Map<string, Set<number>>){
    // if (twtarTypeSlug){
    //   const mapping = twtarTypeSlugToGroupIds.get(twtarTypeSlug)
    //   if (!mapping.has(framework.group_id)){
    //     return false
    //   }
    // }
    return true;
  }
  isStatsRegenDisabled(){
    if (this.testDesignSelected._isStatsRegenerating){
      return true
    }
    return !this.whitelabel.getSiteFlag('IS_AUTH_FORM_STATS_GEN_ENABLED')
  }
  async triggerTwtarStatsRegen(){
    const twtar = this.testDesignSelected;
    const twtar_id = twtar.id;
    const test_window_id = this.testWindowSelected.id;
    const type_slug = twtar.type_slug;
    const reloadInfo = await this.auth.apiCreate('public/test-auth/twtar/stats', {twtar_id, test_window_id, type_slug})
    if (reloadInfo.estimated_completion_time){
      this.loginGuard.quickPopup(`Stats regeneration triggered. Estimated completion time is ${reloadInfo.estimated_completion_time} minutes.`)
    }
    else {
      this.loginGuard.quickPopup('Stats regeneration triggered. Please refresh the page in a few minutes to view the results.')
    }
  }
  async getTwtarForTw(tw_id){
    const {records, groupMappings, isEditPermission} = await this.auth.apiGet('public/test-auth/twtar/test-designs', tw_id, {});
    this.isEditPermission = isEditPermission;
    for (let record of records){
      record._print_configs = []
      try {
        record._framework_id = record.print_configs.framework_id
        record._print_configs = record.print_configs.print_configs || []
      }
      catch(e){}
    }
    const availableAuthGroups = groupMappings.filter(r => r.type_slug == this.twTypeSlugSelected).map( r => r.group_id)
    let availableTestDesigns = [... this.frameworks].filter(r => availableAuthGroups.includes(r.group_id));
    availableTestDesigns = availableTestDesigns.sort((a,b) => {
      return a.id - b.id
    })
    const twtarTypeSlugToGroupIds = new Map();
    for (let groupMapping of groupMappings){
      if (!twtarTypeSlugToGroupIds.has(groupMapping.type_slug)){
        twtarTypeSlugToGroupIds.set(groupMapping.type_slug, new Set())
      }
      twtarTypeSlugToGroupIds.get(groupMapping.type_slug).add(groupMapping.group_id)
    }
    return {
      twtars: records,
      availableTestDesigns,
      twtarTypeSlugToGroupIds,
    };
  }
  getTwtarStatusActive(twtar){
    if (twtar.is_active == 1){
      return 'ACTIVE_FULL'
    }
    if (twtar.is_active_for_auth == 1){
      return 'AUTH_SETUP'
    }
    return 'INACTIVE'
  }
  getTwtarStatusSample(twtar){
    if (twtar.is_sample == 1){
      return 'SAMPLE'
    }
    if (twtar.is_field_test == 1){
      return 'FIELD_TEST'
    }
    if (twtar.is_secured == 1){
      return 'OPERATIONAL'
    }
  }
  newTwtarModalStart(){
    this.newTwtarForm = {
      type_slug: null,
      item_set_id: null,
      long_name: '',
      form_code: '',
      lang: 'en',
    } 
  }
  newTwtarModalCancel(){
    this.newTwtarForm = null
  }
  async removeTwtar(twtar){
    this.isSaving = true;
    await this.auth.apiRemove('public/test-auth/twtar/test-designs', twtar.id);
    await this.reloadTwtar();
    this.newTwtarForm = null 
    this.isSaving = false;
  }
  async newTwtarModalSave(){
    const { 
      type_slug,
      item_set_id,
      form_code,
      long_name,
      lang,
    } = this.newTwtarForm;
    if (!(type_slug && item_set_id && long_name && lang && form_code)){
      this.loginGuard.quickPopup('Please fill all fields to continue');
      return;
    }
    this.isSaving = true;
    const record = await this.auth.apiCreate('public/test-auth/twtar/test-designs', {
      test_window_id: this.testWindowSelected.id,
      type_slug,
      item_set_id,
      form_code,
      long_name,
      lang,
    });
    await this.reloadTwtar();
    this.newTwtarForm = null 
    this.isSaving = false;
  }
  async reloadTwtar(){
    const tw = this.testWindowSelected
    const {twtars, availableTestDesigns, twtarTypeSlugToGroupIds} = await this.getTwtarForTw(tw.id);
    const twTwtarTypeMap = this.twTypeToTwtarType.get(tw.type_slug) || new Set();
    const twtarTypeSlugs = this.twtarTypeSlugs.filter(type_slug => twTwtarTypeMap.has(type_slug)  )

    tw.testDesigns = twtars
    tw.availableTestDesigns = availableTestDesigns
    tw.twtarTypeSlugToGroupIds = twtarTypeSlugToGroupIds
    tw.twtarTypeSlugs = twtarTypeSlugs

    this.tempReloadAllTwtarDetail(twtars);
    // select twtar
    if (this.init_twtar_id){
      for (let twtar of tw.testDesigns){
        if (twtar.id == this.init_twtar_id){
          this.selectTwtar(twtar);
          this.init_twtar_id = undefined;
        }
      }
    }
  }
  isAllPanelsBalanced(){
    return !(this.isAdminWindowPanelCollapsed || this.isFormPanelCollapsed)
  }
  getFormPanelMode(){
    if (this.newTwtarForm && this.isEditPermission){
      return 'new-twtar'
    }
    else if (this.testDesignSelected && !this.newTwtarForm){
      return 'existing-twtar'
    }
    else {
      return 'none'
    }
  }
  tempReloadAllTwtarDetail(twtars){ // this is intended to be temporary, we should just be pulling this info in the initial load to reduce the number of API requests
    for (let twtar of twtars){
      this.loadSignoffs(twtar)
    }
  }
  getTwtarTwtt(twtar){
    const twTdType = this.twTdTypes.find(el => el.type_slug == twtar.type_slug)
    return twTdType || {}
  }
  getSignoff(stage){
    return this.testDesignSelected.signoffs[stage.slug] || {}
  }
  async loadSignoffs(twtar){
    twtar.signoffs = null;
    const signoffs = {};
    const {records, signoffSteps, publishedDesignInfo} = await this.auth.apiGet(this.routes.TEST_AUTH_TWTAR_SIGNOFFS, twtar.id, {query:{tw_type_slug: this.twTypeSlugSelected}})
    for (let record of records){
      signoffs[record.signoff_slug] = record;
    }
    for (let td of publishedDesignInfo.published_designs){
      // update twtar assigned td info 
      if (td.assigned_td_id == td.td_id){
        this.reflectTwtarTdAlloc(twtar, td)
      }
    }
    let progress = 0;
    if (signoffSteps.length){
      for (let step of signoffSteps){
        if (signoffs[step.slug]?.is_signed_off == 1){
          progress += 1;
        }
      }
      progress = progress / signoffSteps.length
    }
    twtar.signoffProgress = {progress}
    twtar.signoffs = signoffs
    twtar.signoffSteps = signoffSteps || []
    twtar.publishedDesignInfo = publishedDesignInfo
  }
  reflectTwtarTdAlloc(twtar, td){
    twtar.test_design_id = td.td_id 
    twtar.td_created_by_contact_email = td.contact_email
    twtar.td_created_by_first_name = td.first_name 
    twtar.td_created_by_last_name = td.last_name 
    twtar.td_created_by_uid = td.created_by_uid 
    twtar.td_created_on = td.created_on
  }
  getPublishedDesignOptions(){
    return this.testDesignSelected.publishedDesignInfo.published_designs
  }
  async signoffCancel(twtar, slug){
    twtar.signoffs[slug]._isEditing = false
  }
  async signoffStart(twtar, slug){
    if (!twtar.signoffs[slug]){
      twtar.signoffs[slug] = {};
    }
    twtar.signoffs[slug]._isEditing = true
    twtar.signoffs[slug]._comment = ''
  }
  async signoffApply(twtar, slug){
    twtar.signoffs[slug].isSaving = true
    const comment = twtar.signoffs[slug]._comment;
    const saveRecord = await this.auth.apiPatch(
      this.routes.TEST_AUTH_TWTAR_SIGNOFFS, 
      twtar.id, 
      { comment }, 
      { query: {signoff_slug: slug,}
    })
    twtar.signoffs[slug].isSaving = false // redundant
    twtar.signoffs[slug] = saveRecord;
    twtar.signoffs[slug].is_signed_off = 1
  }
  async signoffRevoke(twtar, slug){
    twtar.signoffs[slug].isSaving = true
    const revoked_note = prompt('Please provide a brief reasoning for revoking the previous signoff (required)')
    await this.auth.apiRemove(this.routes.TEST_AUTH_TWTAR_SIGNOFFS, twtar.id, {
      query: {
        signoff_slug: slug,
        revoked_note,
      }
    })
    twtar.signoffs[slug].isSaving = false
    twtar.signoffs[slug] = {};
  }
  getTwtarCoverInfo(twtar){
    const twTdType = this.getTwtarTwtt(twtar)
    return twTdType || {};
  }
  async selectTwtar(twtar){
    const twtar_id = twtar.id
    this.isAllocatingNewTwtarTd = false;
    if (this.testDesignSelected === twtar){
      this.testDesignSelected = undefined;
      return
    }
    this.testDesignSelected = twtar;
    const aggDetails = (obj) => {
      const arr = [];
      for (let prop in obj){
        arr.push({prop, value: obj[prop]})
      }
      return arr;
    }
    const twTdType = this.getTwtarTwtt(twtar)
    this.testDesignSelected.details = [
      {title:'Test Type', list:aggDetails(twTdType || {})},
      {title:'Test Design', list:aggDetails(twtar || {})},
      {title:'Test Window', list:aggDetails(this.testWindowSelected || {})},
    ]
    // store handy variables
    twtar._resourceInfo = {
      caption: twTdType.resource_caption,
      td_id: twTdType.resource_td_id,
      qs_id: twTdType.resource_qs_id,
      created_on: twTdType.resource_created_on,
      created_by_first_name: twTdType.resource_created_by_first_name,
      created_by_last_name: twTdType.resource_created_by_last_name,
    };
    if (twTdType.resource_td_id){
      twtar._resourceInfo.isSet = true;
    }
    twtar._stats = await this.auth.apiGet('public/test-auth/twtar/stats', twtar_id, {});
    // console.log('this.testDesignSelected.details', this.testDesignSelected.details)
    await this.loadSignoffs(twtar) 
    this.onFormAllocFilterUpdate();
    this.isFormPanelCollapsed = false;
  }
  isCurrentItemStatsLoaded(){
    return !!this.getTwtarStatMeta('export_id')
  }
  getTwtarStatMeta(prop:string){
    const stats = this.currentItemStats()
    if (stats){
      return stats[prop]
    }
  }
  getTwtarFormStatNumeric(prop:string){
    const data = this.getTwtarStatMeta('data');
    let n = 0
    if (data){
      for (let record of data.forms){
        n += +(record[prop] || 0)
      }
    }
    return n 
  }
  currentItemStats(){
    const twtar = this.testDesignSelected
    return twtar._stats
  }
  async allocateTestDesignforTwtar(td){
    this.isSaving = true
    const twtar = this.testDesignSelected;
    try {
      await this.auth.apiPatch(
        'public/test-ctrl/test-designs', 
        twtar.id, 
        { test_design_id: td.td_id, tqr_ovrd_td_id: td.td_id}, 
        { query: {
          tc_group_id: twtar.tc_group_id,
          test_window_id: twtar.test_window_id,
        }}
      )
      this.reflectTwtarTdAlloc(twtar, td)
      this.loadSignoffs(twtar)
    }
    catch(e) {
      this.loginGuard.quickPopup('Could not allocate new test design')
    }
    this.isSaving = false
  }
  //////

  _filterTag(value:string): IItemTag[] {
    const filterValue = value.toLowerCase();
    return this.myItems.availableTags.filter(option => !this.searchTags.filter(t=>t.id === option.id).length && option.caption.toLowerCase().includes(filterValue));
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
    clearInterval(this.issueReloadInterval);
  }

  initTables(){ }

  initRouteView(){
    this.loadData();
  }

  initBreadcrumb(){
    this.breadcrumb = [
      this.breadcrumbsService.TESTAUTH_DASHBOARD(),
    ];
    if(this.view){
      this.breadcrumb.push( this.breadcrumbsService._CURRENT(''+this.view, this.route.toString()) )
    }
  }

  loadData(){
    return this.auth
    .user()
    .subscribe(userInfo => {
      if (userInfo && !this.isUserInited){
        this.isUserInited = true;
        this.myItems
          .loadMyAuthoringGroups()
          .then(() => {
            this.refreshGroupAsSuper();
            this.refreshGroupsAsDTP();
            this.refreshGroupAsTemplateAuthor();
            this.myItems.loadAvailableTags();
            this.initIssues();
            this.initTags();
            return Promise.all([
              this.myItems.loadMyItemSets().then((itemSets: IItemSetDef[]) => this.questionSetsFull = this.questionSets = itemSets),
              this.myItems.loadMyFrameworks().then((frameworks: IItemSetDef[]) => this.frameworksFull = this.frameworks = frameworks),
            ])
            .then(() => {
              this.applyItemSetFilters()
            })
          })
          // .then(() => {
          //   //This placement ensures we have the user info as required before loading the issues 
          //   this.loadIssues();
          // });
      }
    })
  }
  
  isAuthContentView(){
    const authScopedViews:string[] = [
      AuthView.DESIGNS,
      AuthView.SETS,
    ]
    return authScopedViews.includes(this.view)
  }
  isAuthGroupScopedView(){
    const authScopedViews:string[] = [
      AuthView.DESIGNS,
      AuthView.SETS,
      AuthView.PROFILES,
    ]
    return authScopedViews.includes(this.view)
  }
  isNotGroupView(){
    return this.view !== AuthView.GROUPS
  }

  selectedAuthProfileView:AuthProfileView
  selectAuthProfileView(view:AuthProfileView){
    this.selectedAuthProfileView = view
  }

  getAuthoringGroupOptions():IAuthGroupOption[]{
    const selectedGroups = this.getAuthoringGroups()
      .filter(record => this.groupFilterSet.has(record.group_id))
    return selectedGroups
  }

  groupFilterSet:Set<number> = new Set()
  toggleGroupFilter(groupId:number){
    // console.log('toggleGroupFilter', groupId)
    if (this.groupFilterSet.has(groupId)){
      this.groupFilterSet.delete(groupId)
    }
    else {
      this.groupFilterSet.add(groupId)
    }
    this.applyItemSetFilters()
  }
  isGroupToggled(groupId:number){
    return this.groupFilterSet.has(groupId)
  }
  isAnyGroupSelected(){
    return !(this.groupFilterSet.size === 0)
  }

  initTags() {
    this.filteredTags = this.contentSearchQuery.valueChanges.pipe(
      startWith(''),
      map(value => this._filterTag(value))
    )
  }
  questionSetsFull:IItemSetDef[] = [];
  frameworksFull:IItemSetDef[] = [];
  applyItemSetFilters(){
    const descrFilter = (this.itemBankDescrFilter || '').toLowerCase();
    // const groupFilter = (this.itemBankGroupFilter || '').toLowerCase();
    // if (!descrFilter && !groupFilter){
    //   return true;
    // }
    const groupFilter = [... this.groupFilterSet]
    // console.log('applyItemSetFilters')
    const filterItemSet = (itemSet) => {
      let slugToggle, nameToggle, descToggle, groupToggle;
      slugToggle = true;
      descToggle = true;
      nameToggle = true;
      groupToggle = true;
      if (descrFilter){
        const slug = (itemSet.slug || '').toLowerCase()
        if (slug.indexOf(descrFilter) === -1){
          slugToggle = false;
        }
        const name = (itemSet.name || '').toLowerCase()
        if (name.indexOf(descrFilter) === -1){
          nameToggle = false;
        }
        const description = (itemSet.description || '').toLowerCase()
        if (description.indexOf(descrFilter) === -1){
          descToggle = false;
        }
      }
      // if (groupFilter){
      //   const groupName = this.getGroupNameById(itemSet.group_id).toLowerCase()
      //   if (groupName.indexOf(groupFilter) === -1)
      // groupToggle = false;
      // }
      if (groupFilter.length){
        groupToggle = groupFilter.includes(itemSet.group_id)
      }
      return ((slugToggle || nameToggle || descToggle) && groupToggle);
    }
    this.questionSets = this.questionSetsFull.filter(filterItemSet);
    this.frameworks = this.frameworksFull.filter(filterItemSet);
  }
  

  issueReloadInterval;
  initIssues(){
    this.loadIssues();
    // this.issueReloadInterval = setInterval(()=>this.loadIssues(), 5000)
  }

  groupTypeSlugsSelectToggle(typeSlug:string){
    if (this.groupTypeSlugsCheck(typeSlug)){
      this.myItems.selectedAgTypeSlugs.delete(typeSlug)
    }
    else {
      this.myItems.selectedAgTypeSlugs.add(typeSlug)
    }
  }
  groupTypeSlugsCheck(typeSlug:string){
    return this.myItems.selectedAgTypeSlugs.has(typeSlug)
  }
  getGroupTypeSlugs(){
    return this.myItems.agTypeSlugs
  }
  isAuthGroupFiltered(group:IAuthoringGroup){
    if (this.myItems.selectedAgTypeSlugs.size > 0){
      if (!this.groupTypeSlugsCheck(group.type_slug)){
        return false
      }
    }
    return true
  }

  loadIssues() {
    const uid = this.auth.user().getValue().uid;
    const ASSIGNMENT_SORT = (a,b) => ASSIGNMENT_SORT_UID(a,b,uid);
    this.auth.apiFind('public/test-auth/issues', {})
      .then(issues => {
        issues.data.sort(ASSIGNMENT_SORT);
        this.issues = issues.data;
      })
      .catch(e => {
        console.error(e);
      });
  }

  getAuthoringGroups(){
    return this.myItems.getAuthoringGroups();;
  }

  getGroupNameById(groupId: number) {
    return this.myItems.getGroupNameById(groupId);
  }

  checkQuestionSetSuperRole(questionSet:IItemSetDef){
    const group = this.myItems.getGroupById(questionSet.group_id);
    let single_group; 
    
    if(questionSet.single_group_id) {
      single_group = this.myItems.getGroupById(questionSet.single_group_id);
    }
    return (group && group.isSuper) || (single_group && single_group.isSuper);
  }

  checkPersonalQuestionSet(questionSet:IItemSetDef){
    const group = this.myItems.getGroupById(questionSet.group_id);
    return (group && !!group.isPersonal);
  }

  hasGroupsAsSuper() {
    return this.myItems.hasGroupsAsSuper;
  }

  hasGroupsAsTemplateAuthor() {
    return this.myItems.hasGroupsAsTemplateAuthor;
  }

  refreshGroupAsSuper(){
    this.myItems.refreshGroupsAsSuper();
  }

  refreshGroupsAsDTP(){
    this.myItems.refreshGroupsAsDTP();
  }

  refreshGroupAsTemplateAuthor() {
    this.myItems.refreshGroupsAsTemplateAuthor();
  }

  toggleNewQuestionSetCreator(){
    this.isCreatingNewQuestionSet = !this.isCreatingNewQuestionSet;
    if (this.isCreatingNewQuestionSet){
      this.refreshGroupAsSuper();
      this.refreshGroupsAsDTP();
      this.refreshGroupAsTemplateAuthor();
    }
  }

  toggleNewTestDesignCreator(){
    this.isCreatingNewTestDesign = !this.isCreatingNewTestDesign;
    if (this.isCreatingNewTestDesign){
      this.refreshGroupAsSuper();
      this.refreshGroupsAsDTP();
      this.refreshGroupAsTemplateAuthor();
    }
  }

  toggleNewAuthGroupCreator() {
    this.isCreatingNewAuthGroup = !this.isCreatingNewAuthGroup;
  }

  openItemBankAccessModal(questionSet: IItemSetDef) {
    const config = {
      itemSetId: questionSet.id,
      singleGroupId: questionSet.single_group_id || -1,
      authoringGroupId: questionSet.group_id,
      groupName: questionSet.name,
      questionSet
    }

    this.pageModal.newModal({
      type: AuthoringModal.MANAGE_ITEM_BANK,
      config,
      finish: this.manageItemBankModalFinish
    })
  }

  manageItemBankModalFinish = () => {
    this.pageModal.closeModal();
  }

  openAuthGroupTagsModal(group: IAuthoringGroup) {
    if(!group.isSuper) {
      return;
    }

    const config = {
      authGroupId: group.group_id,
      groupName: group.isPersonal ? this.lang.tra('test_auth_personal') : group.description
    }
    
    this.pageModal.newModal(
      {
        type: AuthoringModal.MANAGE_GROUP_TAGS,
        config,
        finish: this.manageGroupTagModalFinish
      }
    );
  }

  manageGroupTagModalFinish = () => {
    this.pageModal.closeModal();
  }

  openAuthGroupOptionsModal(group: IAuthoringGroup) {
    if (!group.isSuper || group.isPersonal) {
      return;
    }

    const config = {
      authoringGroupId: group.group_id,
      groupName: group.description
    };
    this.pageModal.newModal({
      type: AuthoringModal.MANAGE_GROUP,
      config,
      finish: this.manageGroupModalFinish
    });
  }
  
  manageGroupModalFinish = () => {
    this.pageModal.closeModal();
  }

  createNewItemBank(){
    this.itemSetCreateError = null;
    const payload:any = {};
    const errors = [];
    Object.keys(this.itemSetCreateForm).forEach(key => {
      const val = this.itemSetCreateForm[key].value;
      payload[key] = val;
      if (!val){
        switch(key){
          case 'slug': errors.push(this.lang.tra('test_auth_short_name')); break;
          case 'name': errors.push(this.lang.tra('test_auth_title')); break;
          case 'description': errors.push(this.lang.tra('test_auth_description')); break;
          case 'group_id': errors.push(this.lang.tra('test_auth_authoring_group')); break;
        }
      }
    });
    if (errors.length > 0){
      this.itemSetCreateError = `${this.lang.tra('required_fields_error')} (${errors.join(', ')})`;
    }
    else{
      this.isItemSetCreateSaving = true;
      this.myItems
        .createNewItemBank(payload)
        .then(res => {
          this.isItemSetCreateSaving = false;
          this.isCreatingNewQuestionSet = false;
        })
    }
  }

  isArchivingItemBank:boolean;
  archiveItemBank(itemBank:IItemSetDef) {
    if (confirm(`${this.lang.tra('item_set_archive_confirmation')} ${itemBank.name}?`)){
      this.isArchivingItemBank = true;
      this.myItems
        .archiveItemBank(itemBank.id)
        .then(res => {
          this.isArchivingItemBank = false;
          this.loginGuard.quickPopup(this.lang.tra('item_set_archive_success'));
        } )
        .catch(e => this.isArchivingItemBank = false )
    }
  }
  recoverItemBank(itemBank:IItemSetDef) {
    if (confirm(`${this.lang.tra('item_set_recovery_confirmation')} ${itemBank.name}?`)){
      this.isArchivingItemBank = true;
      this.myItems
        .recoverItemBank(itemBank.id)
        .then(res => {
          this.isArchivingItemBank = false;
          this.loginGuard.quickPopup(this.lang.tra('item_set_recovery_success'));
        } )
        .catch(e => this.isArchivingItemBank = false )
    }
  }

  archiveTestDesign(itemBank:IItemSetDef) {
    if (confirm(`${this.lang.tra('item_set_archive_confirmation')} ${itemBank.name}?`)){
      this.isArchivingItemBank = true;
      this.myItems
        .archiveTestDesign(itemBank.id)
        .then(res => {
          this.isArchivingItemBank = false;
          this.loginGuard.quickPopup(this.lang.tra('item_set_archive_success'));
        })
        .catch(e => this.isArchivingItemBank = false )
    }
  }

  createNewTestDesign(){
    this.testDesignCreateError = null;
    const payload:any = { };
    const errors = [];
    if (this.compositeAsmtTemplateQsId){
      payload['compositeAsmtTemplateQsId'] = this.compositeAsmtTemplateQsId
    }
    Object.keys(this.testDesignCreateForm).forEach(key => {
      const val = this.testDesignCreateForm[key].value;
      payload[key] = val;
      if (!val){
        switch(key){
          case 'slug': errors.push(this.lang.tra('test_auth_short_name')); break;
          case 'name': errors.push(this.lang.tra('test_auth_title')); break;
          case 'description': errors.push(this.lang.tra('test_auth_description')); break;
          case 'group_id': errors.push(this.lang.tra('test_auth_authoring_group')); break;
        }
      }
    });
    if (errors.length > 0){
      this.testDesignCreateError = `${this.lang.tra('required_fields_error')} (${errors.join(', ')})`;
    }
    else{
      this.isTestDesignCreateSaving = true;
      this.myItems
        .createNewTestDesign(payload)
        .then(res => {
          this.isTestDesignCreateSaving = false;
          this.isCreatingNewTestDesign = false;
        })
    }
  }

  createNewAuthGroup() {
    this.authGroupCreateError = null;
    const payload: any = { };
    const errors = [];
    Object.keys(this.groupCreateForm).forEach(key => {
      const val = this.groupCreateForm[key].value;
      payload[key] = val;
      if (!val) {
        switch (key) {
          case 'description': errors.push(this.lang.tra('ie_name')); break;
        }
      }
    });
    if (errors.length > 0) {
      this.authGroupCreateError = `${this.lang.tra('required_fields_error')} (${errors.join(', ')})`;
    } else {
      if (this.validateGroupName(this.groupCreateForm.description)){
        this.isAuthGroupCreateSaving = true;
        this.myItems
            .createNewAuthGroup(payload)
            .then(res => {
              this.isAuthGroupCreateSaving = false;
              this.isCreatingNewAuthGroup = false;
              this.resetGroupForm();
            });
      }

    }
  }
  
  resetGroupForm(){
    this.groupCreateForm.description.reset();
  }

  validateGroupName(name){
    let groups = this.getAuthoringGroups();
    for (let i = 0; i < groups.length; i++){
        if (groups[i].description === name.value){
          this.authGroupCreateError = 'Group name already exists!';
             return false;
        }
      }
    return true;
  }
  toggleViewArchive( getItemSets = true) {
    this.viewArchivedLoading = true;
    this.viewArchived = !this.viewArchived;
    // Check to see if it is an item set or a framework we're looking for in the archived
    const loadMethod = getItemSets ? this.myItems.loadMyItemSets : this.myItems.loadMyFrameworks;

    loadMethod.call(this, this.viewArchived).then((data: IItemSetDef[]) => {
      if (getItemSets) {
        this.questionSetsFull = this.questionSets = data;
      } else {
        this.frameworksFull = this.frameworks = data;
      }
    }).catch(err => {
      console.log(err)
    }).finally(() => {
      this.viewArchivedLoading = false;
      this.applyItemSetFilters();
    });
  }

  getFrameworkRoute(itemSetId:number, lang?: string){
    if(!lang){
      lang = this.lang.c()
    }
    return `/${lang}/test-auth/framework/1/${itemSetId}`;
  }

  getItemRoute(item) {
    return this.getQuestionSetRoute(item.question_set_id) + "/" + encodeURIComponent(item.question_label);
  }

  getQuestionSetRoute(itemSetId:number){
    return `/${this.lang.c()}/test-auth/item-set-editor/${itemSetId}`;
  }

  getCommentsRoute() {
    return `/${this.lang.c()}/test-auth/issues/`
  }

  getGroupAccessRoute(groupId:number){
    return `/${this.lang.c()}/test-auth/group/${groupId}/access`;
  }

  getAssetLibraries() {
    return [];
  }

  // Creating new authors
  toggleNewTestAuthorCreator() {
    this.isCreatingNewTestAuthor = !this.isCreatingNewTestAuthor;
  }

  getNotificationsRouterLink(questionSet) {
    return `/en/test-auth/notifications/${questionSet.id}`
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }

  toggleActionDropdown(id: number, event: any, isGroup: boolean = false) {
    const dropdownKey = isGroup ? `group${id}` : `${id}`;
    const isShowing = !!this.showActions[dropdownKey];
    this.hideAllDropdowns();
    if(!isShowing) {
      this.showActions[dropdownKey] = true;
    }
    event.stopPropagation();
  }

  isActionDropdownActive(id: number) {
    return !!this.showActions[id];
  }

  hideAllDropdowns() {
    for(const key of Object.keys(this.showActions)) {
      this.showActions[key] = false;
    }
  }

  async contentSearch() {
    const queryParams = {
      searchQuery: this.contentSearchQuery.value, 
      isItemSearchById: this.isItemSearchById, 
      tagIds: this.searchTags.map( t => t.id)
    }
    this.searchItems = await this.auth.apiFind(this.routes.TEST_AUTH_CONTENT_SEARCH, {query: queryParams})
  }

  isGroupDropdownActive(group_id: number) {
    return !!this.showActions[`group${group_id}`];
  }

  addTag(tag:IItemTag) {
    if(this.searchTags.filter(t => t.id === tag.id).length) {
      return;
    }

    this.searchTags.push(tag);
    this.contentSearchQuery.setValue("");//Refreshes the filtered list
    this.tagInput.nativeElement.blur();
    this.contentSearch();
  }

  deleteTag(tag:IItemTag) {
    const index = this.searchTags.findIndex( t => t.id === tag.id); 
    if(index === -1) {
      return;
    }
    this.searchTags.splice(index, 1);
    this.contentSearchQuery.setValue(this.contentSearchQuery.value);//Refreshes the filtered list
    this.contentSearch();
  }
  
  isReadOnly = () => this.editingDisabled.isReadOnly(true);

  onFormAllocationGridReady(params: any) {
    this.gridApiFormAllocation = params.api;
    this.gridColumnApiFormAllocation = params.columnApi;
    params.columnApi.autoSizeColumns();
  }

  onFormAllocationSelectionChanged(event: any) {
    const selectedRows = this.gridApiFormAllocation.getSelectedRows();
    if (selectedRows.length > 0) {
      this.selectTwtar(selectedRows[0]);
    }
  }

  // Add these new methods
  loadSavedFilter() {
    try {
      const savedFilter = localStorage.getItem('twtarAssessmentCodeFilter');
      if (savedFilter) {
        this.selectedAssessmentCodes = savedFilter.split(',');
        this.applyFilter();
      }
    } catch (e) {
      console.error('Error loading saved filter:', e);
    }
  }
  
  saveFilter() {
    try {
      localStorage.setItem('twtarAssessmentCodeFilter', this.selectedAssessmentCodes.join(','));
    } catch (e) {
      console.error('Error saving filter:', e);
    }
  }
  
  openFilterModal() {
    // Extract unique assessment codes from all test designs
    if (this.testWindowSelected && this.testWindowSelected.testDesigns) {
      const codes = this.testWindowSelected.testDesigns.map(td => td.type_slug);
      this.allAssessmentCodes = Array.from(new Set(codes)).sort() as string[];
      
      // Prepare row data for the filter modal grid
      this.filterModalRowData = this.allAssessmentCodes.map(code => ({
        code: code,
        selected: this.selectedAssessmentCodes.includes(code)
      }));
    }
    this.isFilterModalOpen = true;
  }
  
  closeFilterModal() {
    this.isFilterModalOpen = false;
  }
  
  applyFilter() {
    if (!this.testWindowSelected || !this.testWindowSelected.testDesigns) {
      return;
    }
    
    if (this.selectedAssessmentCodes.length === 0) {
      this.filteredTestDesigns = [...this.testWindowSelected.testDesigns];
    } else {
      this.filteredTestDesigns = this.testWindowSelected.testDesigns.filter(
        td => this.selectedAssessmentCodes.includes(td.type_slug)
      );
    }
    
    this.saveFilter();
  }
  
  resetFilter() {
    this.selectedAssessmentCodes = [];
    this.applyFilter();
  }
  
  toggleAssessmentCode(code: string) {
    const index = this.selectedAssessmentCodes.indexOf(code);
    if (index === -1) {
      this.selectedAssessmentCodes.push(code);
    } else {
      this.selectedAssessmentCodes.splice(index, 1);
    }
    this.applyFilter();
  }
  
  isAssessmentCodeSelected(code: string): boolean {
    return this.selectedAssessmentCodes.includes(code);
  }
  
  getFilteredCount(): string {
    if (!this.testWindowSelected || !this.testWindowSelected.testDesigns) {
      return '0 allocated designs';
    }
    
    const total = this.testWindowSelected.testDesigns.length;
    const filtered = this.filteredTestDesigns.length;
    
    if (this.selectedAssessmentCodes.length === 0) {
      return `${total} allocated designs`;
    } else {
      return `${filtered} of ${total} allocated designs`;
    }
  }

  // Add method for filter modal grid ready
  onFilterModalGridReady(params: any) {
    this.filterModalGridApi = params.api;
    
    // Pre-select rows based on selectedAssessmentCodes
    this.filterModalGridApi.forEachNode(node => {
      if (this.selectedAssessmentCodes.includes(node.data.code)) {
        node.setSelected(true);
      }
    });
  }
  
  // Add method for filter modal selection changed
  onFilterModalSelectionChanged() {
    const selectedNodes = this.filterModalGridApi.getSelectedNodes();
    this.selectedAssessmentCodes = selectedNodes.map(node => node.data.code);
    this.applyFilter();
  }
}

