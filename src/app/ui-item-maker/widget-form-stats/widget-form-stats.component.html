<div class="form-stats-container">

   <mode-toggle 
    [itemBankCtrl]="itemBankCtrl" 
    [saveLoadCtrl]="saveLoadCtrl" 
    [frameworkCtrl]="frameworkCtrl"
  ></mode-toggle>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-indicator">
    <i class="fa fa-spinner fa-spin"></i> Loading stats...
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="error-message">
    {{error}}
  </div>

  <!-- Stats Display -->
  <div *ngIf="statsData && !isLoading" class="stats-content">
    <!-- Meta Information -->
    <div class="meta-section">
      <h3>Item Analysis for {{getAssessmentName()}}</h3>
      <table style="width: auto;">
        <tr>
          <td>Job ID:</td>
          <td><code>{{statsData.export_id}}</code></td>
        </tr>
        <tr>
          <td>Generated On:</td>
          <td>{{formatDate(statsData.created_on)}}</td>
        </tr>
        <tr>
          <td>Test Taker Submissions:</td>
          <td>{{getNumAttempts()}}</td>
        </tr>
      </table>
    </div>

    <!-- Download Links -->
    <div class="downloads-section">
      <h3>Download Reports</h3>
      <div *ngFor="let asset of statsData.urlsSigned">
        <a 
            (click)="downloadStatsReport(asset)" 
            class="button is-small"
        >
          <i class="fa fa-download"></i> 
          {{asset.caption}}
        </a>
      </div>
      

    <!-- Regenerate Stats Button -->
    <div class="actions-section">
      <button 
        class="button is-small"
        [disabled]="isRegenerating"
        (click)="regenerateStats()"
      >
        <i class="fa fa-refresh"></i> 
        Regenerate Statistics
        <span *ngIf="isRegenerating" class="is-pulled-right">
          <i class="fa fa-spinner fa-spin"></i>
        </span>
      </button>
    </div>
  </div>
</div> 