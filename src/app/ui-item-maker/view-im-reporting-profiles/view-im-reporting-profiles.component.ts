import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { ReportingProfilesService } from '../reporting-profiles.service';
import { IAuthGroupOption } from '../view-im-group-access/model/types';
import { ILayoutConfig, INodeRefConfig, IReportingProfile, IReportingProfileNew, IReportingProfileView, IRepProfileDB, TAlign, TFontStyle } from './model/types';
import { applyInputSlugConstraints, authGroupAsmtProfileReload, createNewAsmtProfile, isMultiAuthGroupScope, IViewAuthGroupProfileLoader, onAuthGroupOptionsChange, onAuthGroupSelection } from 'src/app/core/util/components-authoring';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';

// Enum to manage which sub-profile is in focus
export enum SubProfileFocus {
  NONE = 'NONE',
  DOMAIN_SCHEMA = 'DOMAIN_SCHEMA',
  SCALING_FACTOR = 'SCALING_FACTOR',
  CATEGORY_SCHEMA = 'CATEGORY_SCHEMA',
  CUT_SCORE = 'CUT_SCORE',
  REPORTING_LAYOUT = "REPORTING_LAYOUT",
  ASSESSMENT_STRUCTURE = "ASSESSMENT_STRUCTURE"
}

@Component({
  selector: 'view-im-reporting-profiles',
  templateUrl: './view-im-reporting-profiles.component.html',
  styleUrls: ['./view-im-reporting-profiles.component.scss']
})
export class ViewImReportingProfilesComponent implements OnInit, OnChanges, IViewAuthGroupProfileLoader {

  @Input() authGroupOptions: IAuthGroupOption[];

  profiles: IReportingProfileView[] = [];
  newProfile: Partial<IReportingProfile> = {};
  profileEdit: IReportingProfile = {};
  isPreserveFormAfterCreation = false;
  isMultiEdit = false;
  
  // Authoring group and profile selection
  selectedAuthoringGroupId: number;
  selectedProfileId: number;
  
  // Sub-profile management
  subProfileFocus = SubProfileFocus.NONE;
  SubProfileFocus = SubProfileFocus; // for template access

  // Loading states
  isLoading = false;
  isLoadingError = false;
  isLoaded = false;

  // Internal state for changes
  currentAuthGroupHash: string;

  constructor(
    private rpService: ReportingProfilesService,
    private auth: AuthService,
    private routes: RoutesService,
    private login: LoginGuardService
  ) { }

  ngOnInit() {
    onAuthGroupSelection(this);
  }
  ngOnChanges(changes: SimpleChanges) {
    onAuthGroupOptionsChange(this, changes)
  }

  reloadProfiles = () => authGroupAsmtProfileReload(this, () => this.rpService.findReportingProfiles(this.authGroupOptions));
  isMultiAuthGroupScope = () => isMultiAuthGroupScope(this);

  async createProfile() {
    applyInputSlugConstraints(this, ['slug']);
    await createNewAsmtProfile(this, {
      requiredFields: ['slug'],
      create: (payload:IReportingProfileNew) => this.rpService.createReportingProfile(this.selectedAuthoringGroupId, payload)
    })
  }

  selectProfile(profile: IReportingProfile) {
    this.subProfileFocus = SubProfileFocus.NONE;
    if (this.selectedProfileId == profile.id){
      this.selectedProfileId = null
    }
    else {
      this.selectedProfileId = profile.id;
    }
  }

  getSelectedProfileSlug(){
    if (this.profiles){
      const profile = this.profiles.find( record => record.id === this.selectedProfileId);
      if (profile){
        return profile.slug
      }
    }
  }

  setSubProfileFocus(focus: SubProfileFocus) {
    this.subProfileFocus = focus;
    if(focus == SubProfileFocus.REPORTING_LAYOUT) {
      this.loadLayoutProfile();
    }
  }

  nodeRefsCtx: {
    isSaving: boolean,
    hasChanges: boolean
  } = {
    isSaving: false,
    hasChanges: false,
  }


  LayoutConfigCtx: {
    isSaving: boolean,
    hasChanges: boolean
  } = {
    isSaving: false,
    hasChanges: false,
  }


  layoutProfileCtx: {
    isError: boolean,
    isLoading: boolean,
    layoutConfig: string,
    txtNodeRefs: INodeRefConfig
  } = {
    isError: false,
    isLoading: false,
    layoutConfig: null,
    txtNodeRefs: null,
  }

  resetNodeRefsCtx() {
    this.nodeRefsCtx = {
      isSaving: false,
      hasChanges: false,
    }
  }

  resetLayoutConfCtx() {
    this.nodeRefsCtx = {
      isSaving: false,
      hasChanges: false,
    }
  }

  resetLayoutProfile() {
    this.layoutProfileCtx = {
      isError: false,
      isLoading: false,
      layoutConfig: null,
      txtNodeRefs: null,
    }
  }
  
  async loadLayoutProfile(selectedProfileId: number = this.selectedProfileId) {
    if(!selectedProfileId) {
      return;
    }
    this.resetLayoutProfile();
    this.resetNodeRefsCtx();
    this.resetLayoutConfCtx();

    this.layoutProfileCtx.isLoading = true;

    let reportingProfileRaw: IRepProfileDB;
    
    try {
      try {
        reportingProfileRaw = await this.auth.apiGet(this.routes.TEST_AUTH_RP_LAYOUT_PROFILES, selectedProfileId)
        console.log(reportingProfileRaw);
      } catch (err) {
        this.login.quickPopup("Reporting profile doesn't exists.");
        this.layoutProfileCtx.isError = true;
        return;
      }
  
      try {
        this.layoutProfileCtx.layoutConfig = reportingProfileRaw.layout_config
        this.layoutProfileCtx.txtNodeRefs = JSON.parse(reportingProfileRaw.text_node_ref_config);
        console.log(this.layoutProfileCtx.layoutConfig);
        console.log(this.layoutProfileCtx.txtNodeRefs);
      } catch(err) {
        this.login.quickPopup("Unable to process configurations.");
        this.layoutProfileCtx.isError = true;
        return;
      }

    } finally {
      this.layoutProfileCtx.isLoading = false;
    }
  }

  async saveChanges() {
    if(this.LayoutConfigCtx.hasChanges && !this.LayoutConfigCtx.isSaving) {
      await this.saveLayoutConfig();
    }
    if(this.nodeRefsCtx.hasChanges && !this.nodeRefsCtx.isSaving) {
      await this.saveTextNodeRefs();
    }
  }

  async saveTextNodeRefs() {
    this.nodeRefsCtx.isSaving = true;
    try {
      await this.auth.apiPatch(this.routes.TEST_AUTH_RP_TEXT_NODE_REFS, this.selectedProfileId, this.layoutProfileCtx.txtNodeRefs)
      this.nodeRefsCtx.hasChanges = false;
      this.login.quickPopup('Your changes have been saved.')
    } catch(err) {
      this.login.quickPopup('Unable to save changes.')
    } finally {
      this.nodeRefsCtx.isSaving = false;
    }
  }

  onTextNodeRefChange() {
    this.nodeRefsCtx.hasChanges = true;
  }

  async saveLayoutConfig() {
    this.LayoutConfigCtx.isSaving = true;
    try {
      await this.auth.apiPatch(this.routes.TEST_AUTH_RP_LAYOUT_CONFIGS, this.selectedProfileId, {config: this.layoutProfileCtx.layoutConfig})
      this.LayoutConfigCtx.hasChanges = false;
      this.login.quickPopup('Your changes have been saved.')
    } catch(err) {
      this.login.quickPopup('Unable to save changes.')
    } finally {
      this.LayoutConfigCtx.isSaving = false;
    }
  }

  onLayoutConfChange() {
    this.LayoutConfigCtx.hasChanges = true;
  }

  getAlignments(): TAlign[] {
    return ['left', 'center', 'right']
  }

  getFontStyles(): TFontStyle[] {
    return ['regular', 'bold']
  }

  getTextNodeRefs() {
    return Object.keys(this.layoutProfileCtx.txtNodeRefs);
  }

  isSavingDisabled() {
    return (this.nodeRefsCtx.isSaving || !this.nodeRefsCtx.hasChanges) && (this.LayoutConfigCtx.isSaving || !this.LayoutConfigCtx.hasChanges)
  }

  isNotSaving() {
    return !this.nodeRefsCtx.isSaving && !this.LayoutConfigCtx.isSaving
  }
}
