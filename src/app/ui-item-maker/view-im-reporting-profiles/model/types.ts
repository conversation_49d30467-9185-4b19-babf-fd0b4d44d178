
// todo: fill out all of the missing properties

export interface IReportingProfileNew {
    authoring_group_id: number,
    slug: string,
  }
  
  export interface IReportingProfile {
    id?: number;
    authoring_group_id?: number;
    slug?: string;
  
    created_on?: string; // datetime
    created_by_uid?: number;
    is_revoked?: boolean; // stored as TINYINT(1) in DB, but treated as boolean in TS
    revoked_on?: string | null; // datetime
    revoked_by_uid?: number | null;
  
    domain_schema_id?: number | null;
    domain_score_scaling_factor_profile_id?: number | null;
    category_schema_id?: number | null;
    cut_score_schema_id?: number | null;
    default_cut_score_profile_id?: number | null;
  
    config?: any; // stored as JSON in DB
    // ... other fields
  }
  export interface IReportingProfileView extends IReportingProfile {
    ag_name: string
  }
  
  
  export interface IDomainSchema {
    id?: number;
    authoring_group_id?: number;
    config?: any;
  }
  
  export interface IScalingFactorProfile {
    id?: number;
    authoring_group_id?: number;
    config?: any;
    // ...
  }
  
  export interface ICategorySchema {
    id?: number;
    authoring_group_id?: number;
    config?: any;
  }
  
  export interface ICutScoreProfile {
    id?: number;
    reporting_profile_id?: number;
    config?: any;
    // ...
  }
  
  export type TFontStyle = 'bold' | 'regular'

  export type TAlign = 'left' | 'right' | 'center'
  
  export enum BLOCK_TYPES {
    TEXT = 'TEXT',
    OVERALL = 'OVERALL',
    HEADER = 'HEADER',
    GRAPHS = 'GRAPHS',
    DIVIDER = 'DIVIDER',
    FOOTER = 'FOOTER'
  }
  
  export interface ILayoutBlock {
    blockType: BLOCK_TYPES,
    columns?: ILayoutNode[][],
    data?: ILayoutNode
  }
  
  export interface ILayoutNode {
    marginTop?: number
  }
  export interface ILayoutText extends ILayoutNode{
    slug: string,
    align: TAlign
    fontStyle: TFontStyle,
    fontSize: number
  }
  
  export interface ILayoutGraphs extends ILayoutNode{
    showUnits?: boolean
  }
  
  export interface ILayoutOverall extends ILayoutNode {
    titleSlug: string,
    contentSlug: string,
    titleFontSize: number,
    contentFontSize: number
  }
  
  export interface ILayoutHeader extends ILayoutNode {
    // slug: string,
    // align: TAlign
    // fontStyle: TFontStyle,
    // fontSize: number
  }
  
  export interface ILayoutConfig {
    normalFont: string,
    boldFont: string,
    layout: ILayoutBlock[],
  }
  
  export interface INodeRefConfig {
    [key: string]: {en: string, fr: string}
  }
  
  export interface IRepProfileDB {
    id: number,
    slug: string,
    layout_config: string,
    text_node_ref_config: string,
    layout_profile_id: number,
    layout_config_id: number,
    text_node_refs_id: number
  }