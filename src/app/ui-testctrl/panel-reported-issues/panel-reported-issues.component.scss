.record-limit-info {
    display: inline-flex;
    min-height: 30px;
    align-items: center;
    padding-left: 5px;
}

.comment-entry {
    margin-bottom: 1.0em;
    position: relative;
    .name-bubble {
        position: absolute;
        left: -4em;
        width: 1em;
        height: 1em;
        color: #333;
        border-radius: 100%;
    }
    .comment-id {
        font-size: 0.8em;
        .comment-time {
            color: #ccc;
        }
    }
}

.grid-container {
    flex-grow: 2;
}

.reported-issue-main {
    width:44em; 
    padding:2em; 
    align-self: flex-start; 
    max-height: 90vh;
    overflow: auto;
}

table.table.is-bordered {
    th {
        background-color: #f1f1f1;
    }
}