import { firebaseApp, firebaseDb } from 'src/firebase/firebase';
import { collection, query, where, orderBy, limit, onSnapshot } from "firebase/firestore";
import { doc, setDoc, updateDoc, getDoc, arrayUnion, arrayRemove } from "firebase/firestore";
import { Observable } from "rxjs";

export const FIREBASE_COLLECTION_ID_REPORTED_ISSUES_PRESENCE = 'reported-issues-presence';

export const REPORTED_ISSUES_PRESENCE_HEARTBEAT_MS = 60 * 1000;
export const REPORTED_ISSUES_PRESENCE_BUFFER_MS = 20 * 1000;

const limitCount = 1000;

export interface FBC_RI_PresenceRecord extends FBC_RI_Presence {
    id: string;
}

export interface FBC_RI_Presence {
    ctx: string;
    ric_id: number;
    last_touch_on: any; // datetime
    users: FBC_RI_Presence_User[];
}

export interface FBC_RI_Presence_User {
    uid: number;
    initials: string;
    display_name: string;
    tab_session_hash: string;
    last_touch_on: any; // datetime
}

export function observePresenceRecords(
    ctx: string,
): Observable<FBC_RI_PresenceRecord[]> {
    const presenceQuery = query(
        collection(firebaseDb, FIREBASE_COLLECTION_ID_REPORTED_ISSUES_PRESENCE),
        where("ctx", "==", ctx),
        orderBy("last_touch_on", "desc"),
        limit(limitCount)
    );

    return new Observable<FBC_RI_PresenceRecord[]>((subscriber) => {
        const unsubscribe = onSnapshot(presenceQuery, (querySnapshot) => {
            const records: FBC_RI_PresenceRecord[] = querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data() as FBC_RI_Presence
            }));
            subscriber.next(records);
        }, (error) => {
            subscriber.error(error);
        });

        return () => unsubscribe();
    });
}

const genRecKey = (
    ctx: string,
    ricId: number
) => [ctx, ricId].join(';');

const genInitialsFromDisplayName = (
    display_name: string
) => {
    const parts = ('' + (display_name || '')).split(' ').filter(str => !!str);
    const initialParts = parts.map(part => part[0]);
    if (initialParts.length > 2) {
        return initialParts[0] + initialParts[initialParts.length - 1];
    } else if (initialParts.length == 2) {
        return initialParts[0] + initialParts[1];
    } else if (initialParts.length == 1) {
        return initialParts[0];
    } else {
        return '*';
    }
};

export const updatePresenceRecord = async (
    ctx: string,
    ricId: number,
    uid: number,
    display_name: string,
    tab_session_hash: string
) => {
    const now = new Date();
    const initials = genInitialsFromDisplayName(display_name);

    const docKey = genRecKey(ctx, ricId);
    const recordRef = doc(firebaseDb, FIREBASE_COLLECTION_ID_REPORTED_ISSUES_PRESENCE, docKey);
    const recordSnapshot = await getDoc(recordRef);

    if (!recordSnapshot.exists()) {
        await setDoc(recordRef, {
            ctx,
            ric_id: ricId,
            last_touch_on: now,
            users: [
                {
                    uid,
                    display_name,
                    initials,
                    tab_session_hash,
                    last_touch_on: now
                }
            ]
        });
    } else {
        const recordData = recordSnapshot.data();
        const userExists = recordData.users.some((u: any) => u.uid === uid);
        const updatePayload: any = { last_touch_on: now };

        if (!userExists) {
            updatePayload.users = arrayUnion({
                uid,
                initials,
                display_name,
                tab_session_hash,
                last_touch_on: now
            });
        } else {
            updatePayload.users = recordData.users.map((u: any) =>
                u.uid === uid ? { ...u, last_touch_on: now } : u
            );
        }

        await updateDoc(recordRef, updatePayload);
    }
};

export const removePresenceRecord = async (
    ctx: string,
    ricId: number,
    uid: number
) => {
    const docKey = genRecKey(ctx, ricId);
    const recordRef = doc(firebaseDb, FIREBASE_COLLECTION_ID_REPORTED_ISSUES_PRESENCE, docKey);
    const recordSnapshot = await getDoc(recordRef);

    if (recordSnapshot.exists()) {
        const recordData = recordSnapshot.data();
        const updatedUsers = recordData.users.filter((u: any) => u.uid !== uid);
        await updateDoc(recordRef, { users: updatedUsers });
    }
};

export const isWithinHeartbeat = (
    lastTouch: Date,
    isVerbose = false
): boolean => {
    const now = new Date();
    const thresholdTimeMs = now.getTime() - (REPORTED_ISSUES_PRESENCE_HEARTBEAT_MS + REPORTED_ISSUES_PRESENCE_BUFFER_MS);
    const lastTouchMs = parseFirebaseTimestamp(lastTouch);
    if (isVerbose) {
        console.log('isWithinHeartbeat', (now.getTime() - lastTouchMs) / (1000 * 60));
    }
    return lastTouchMs >= thresholdTimeMs;
};

const parseFirebaseTimestamp = (fbTimestamp) => {
    if (fbTimestamp) {
        if (typeof fbTimestamp.seconds === 'number') {
            return fbTimestamp.seconds * 1000;
        }
    }
    throw new Error('Invalid Firebase timestamp');
}; 