<div class="page-body">
    <div>
      <header
      [breadcrumbPath]="breadcrumb"
      ></header>
      <div class="page-content is-fullpage">
        <div class="dashboard-cards-container" style="margin-bottom: 1em;">
        
            <table>
                <tr>
                    <th>Message Code</th>
                    <th>Updated On</th>
                    <th>Message (English)</th>
                    <th>Message (French)</th>
                    <th>Description</th>
                    <th>Color</th>
                    <th>Edit</th>
                </tr>
                <tr *ngFor="let message of systemMessages">
                    <ng-container *ngIf="!message._edit">
                        <td><code>{{message.slug}}</code></td>
                        <td>{{message.created_on}}</td>
                        <td>
                          <tra-md [slug]="message._data.en"></tra-md>
                        </td>
                        <td>
                          <tra-md [slug]="message._data.fr"></tra-md>
                        </td>
                        <td>{{message.description}}</td>
                        <td>
                            <select [disabled]="true" [(ngModel)]="message.color">
                                <option
                                [ngValue]="null"
                                >default</option>
                                <option
                                *ngFor="let color of availableColor"
                                [ngValue]="color"
                                >{{color}}</option>
                            </select>
                        </td>
                        <td>
                            <button class="button is-small" (click)="recordEditStart(message)">
                                Edit
                            </button>
                        </td>
                    </ng-container>
                    <ng-container *ngIf="message._edit">
                        <td><code>{{message.slug}}</code></td>
                        <td>{{message.created_on}}</td>
                        <td>
                            <textarea class="textarea" [(ngModel)]="message._edit._data.en"></textarea>
                        </td>
                        <td>
                            <textarea class="textarea" [(ngModel)]="message._edit._data.fr"></textarea>
                        </td>
                        <td>
                            <input class="input" [(ngModel)]="message._edit.description">
                        </td>
                        <td>
                          <select class="select" [(ngModel)]="message._edit.color">
                              <option
                              [ngValue]="null"
                              >default</option>
                              <option
                              *ngFor="let color of availableColor"
                              [ngValue]="color"
                              >{{color}}</option>
                          </select>
                        </td>
                        <td>
                            <button class="button is-small is-success"  (click)="recordEditSave(message)">
                                Save
                            </button>
                            <button class="button is-small"  (click)="recordEditCancel(message)">
                                Cancel
                            </button>
                        </td>
                    </ng-container>
                </tr>
            </table>
        
        </div>
    </div>