import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from 'src/app/api/auth.service';
import { MemDataPaginated } from '../../ui-partial/paginator/helpers/mem-data-paginated';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { RoutesService } from 'src/app/api/routes.service';
import { AccountType } from 'src/app/constants/account-types';
import { BreadcrumbsService, IBreadcrumbRoute } from 'src/app/core/breadcrumbs.service';
import { LangService } from 'src/app/core/lang.service';
import { ScrollService } from 'src/app/core/scroll.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { MyCtrlOrgService } from '../my-ctrl-org.service';
import { PageModalController, PageModalService } from '../../ui-partial/page-modal.service';
import { userGroupSummaryColumns, userGroupTypes } from '../view-tc-dashboard/types/tc-summary';
import { FormControl, FormGroup } from '@angular/forms';

const AVAILABLE_MSG_COLOR = [
  'red'
]

interface ISystemMessage {
  slug: string,
  description: string,
  created_on?: string,
  data?: string,
  color?: string,
  _data?: any,
  _edit?: ISystemMessage,
}

@Component({
  selector: 'view-tc-system-messages',
  templateUrl: './view-tc-system-messages.component.html',
  styleUrls: ['./view-tc-system-messages.component.scss']
})
export class ViewTcSystemMessagesComponent implements OnInit {
  
  public systemMessages:ISystemMessage[];
  public routeSub: Subscription;
  public breadcrumb: IBreadcrumbRoute[];
  pageModal: PageModalController;
  availableColor = AVAILABLE_MSG_COLOR;
  constructor(private loginGuard: LoginGuardService, // 
    private router: Router,
    private route: ActivatedRoute,
    private breadcrumbsService: BreadcrumbsService,
    private scrollService: ScrollService,
    private lang: LangService,
    private auth: AuthService,
    private pageModalService: PageModalService,
    private routes: RoutesService,
    private myCtrlOrg: MyCtrlOrgService,
    private whitelabel: WhitelabelService,
  ) { }

  ngOnInit(): void {
    this.scrollService.scrollToTop();
    this.loginGuard.activate([AccountType.TEST_CTRL]);
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.routeSub = this.route.params.subscribe(routeParams => {
      // this.setupId = routeParams['setupId'];
      this.breadcrumb = [
        this.breadcrumbsService.TESTCTRL_DASHBOARD(),
        this.breadcrumbsService._CURRENT('System Messages', this.router.url),
      ]
      this.loadData()
    });
  }

  async loadData(){
    const records = await this.auth.apiFind(this.routes.TEST_CTRL_SYSTEM_MESSAGE)
    records.forEach( r => {
      try {
        r._data = JSON.parse(r.data)
      }
      catch(e){
        r._data = {};
      }
    })
    this.systemMessages = records
  }

  recordEditStart(record:ISystemMessage){
    record._edit = {
      description: record.description,
      slug: record.slug,
      _data: JSON.parse(JSON.stringify(record._data)),
      color: record.color
    }
  }

  async recordEditSave(record:ISystemMessage){
    const {_edit} = record
    // For backwards compatibility when app still uses data.message instead of by language
    _edit._data.message = _edit._data.en || "";
    const editPayload = {
      slug: _edit.slug,
      description: _edit.description,
      payload: JSON.stringify(_edit._data),
      color: _edit.color,
    };
    const newRecord = await this.auth.apiCreate('public/test-ctrl/system-message', editPayload)
    record._data =  _edit._data
    record.slug =  _edit.slug
    record.description =  _edit.description
    record.created_on =  newRecord.created_on
    record.color =  _edit.color
    record._edit = null;
  }

  recordEditCancel(record:ISystemMessage){
    record._edit = null;
  }

}
