import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import { UiPartialModule } from '../ui-partial/ui-partial.module';
import { FullCalendarModule } from '@fullcalendar/angular';
import { ClipboardModule } from 'ngx-clipboard';
import { MarkdownModule } from 'ngx-markdown';
import { ViewTcCreateAccountComponent } from './view-tc-create-account/view-tc-create-account.component';
import { ViewTcDashboardComponent } from './view-tc-dashboard/view-tc-dashboard.component';
import { ViewTcNewTestWindowComponent } from './view-tc-new-test-window/view-tc-new-test-window.component';
import { ViewTcTestWindowComponent } from './view-tc-test-window/view-tc-test-window.component';
import { ViewTcAccountsComponent } from './view-tc-accounts/view-tc-accounts.component';
import { ViewTcItemBankComponent } from './view-tc-item-bank/view-tc-item-bank.component';
import { ViewTcItemBankItemComponent } from './view-tc-item-bank-item/view-tc-item-bank-item.component';
import { ViewTcItemBankFrameworkComponent } from './view-tc-item-bank-framework/view-tc-item-bank-framework.component';
import { ViewTcTestDesignComponent } from './view-tc-test-design/view-tc-test-design.component';
import { ViewTcInstitutionsComponent } from './view-tc-institutions/view-tc-institutions.component';
import { UiTestadminModule } from '../ui-testadmin/ui-testadmin.module';
import { ViewTcSebGenComponent } from './view-tc-seb-gen/view-tc-seb-gen.component';
import { UiTestrunnerModule } from '../ui-testrunner/ui-testrunner.module';
import { ViewTcReportingComponent } from './view-tc-reporting/view-tc-reporting.component';
import { UiTestctrlRoutingModule } from './ui-testctrl-routing.module';
import { ViewTcBoardsSchoolsComponent } from './view-tc-boards-schools/view-tc-boards-schools.component';
import { ViewTcStudentsTeachersClassesComponent } from './view-tc-students-teachers-classes/view-tc-students-teachers-classes.component';
import { TcTableBoardsComponent } from './tc-table-boards/tc-table-boards.component';
import { TcTableSchoolsComponent } from './tc-table-schools/tc-table-schools.component';
import { TcTableStudentsComponent } from './tc-table-students/tc-table-students.component';
import { TcTableTeachersComponent } from './tc-table-teachers/tc-table-teachers.component';
import { TcTableClassesComponent } from './tc-table-classes/tc-table-classes.component';
import { TcTableCommonComponent } from './tc-table-common/tc-table-common.component';
import { TcTableCommonImportComponent } from './tc-table-common-import/tc-table-common-import.component';
import { ViewTcSchoolComponent } from './view-tc-school/view-tc-school.component';
import { ViewTcBoardsComponent } from './view-tc-boards/view-tc-boards.component';
import { ViewTcClassesComponent } from './view-tc-classes/view-tc-classes.component';
import { ViewTcAsmtSessionItemAnalysisComponent } from './view-tc-asmt-session-item-analysis/view-tc-asmt-session-item-analysis.component';
import { ViewTcTeachersComponent } from './view-tc-teachers/view-tc-teachers.component';
import { ViewTcStudentsComponent } from './view-tc-students/view-tc-students.component';
import { PanelTwtarComponent } from './panel-twtar/panel-twtar.component';
import { AgGridModule } from 'ag-grid-angular';
import { PanelReportedIssuesComponent } from './panel-reported-issues/panel-reported-issues.component';
import { PanelStudentLookupComponent } from './panel-student-lookup/panel-student-lookup.component';
import { PanelTcStudentAttemptInfoComponent } from './panel-tc-student-attempt-info/panel-tc-student-attempt-info.component';
import { ViewTcirDashboardTempComponent } from './view-tcir-dashboard-temp/view-tcir-dashboard-temp.component';
import { PanelStudentExceptionsComponent } from './panel-student-exceptions/panel-student-exceptions.component';
import { ViewTcdeDashboardComponent } from './view-tcde-dashboard/view-tcde-dashboard.component';
import { PanelRealTimeAuditsComponent } from './panel-real-time-audits/panel-real-time-audits.component';
import { PanelUnsubmissionRequestsComponent } from './panel-unsubmission-requests/panel-unsubmission-requests.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatMomentDateModule } from '@angular/material-moment-adapter';
import { MatChipsModule } from '@angular/material/chips';
import { MatButtonModule } from '@angular/material/button';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';
import { MatDialogModule } from '@angular/material/dialog';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { MatMenuModule } from '@angular/material/menu';
import { PanelTwSummaryComponent } from './panel-tw-summary/panel-tw-summary.component';
import { PanelSchoolParticipComponent } from './panel-school-particip/panel-school-particip.component';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { ViewTcRolesComponent } from './view-tc-roles/view-tc-roles.component';
import { ViewTcHelpPageComponent } from './view-tc-help-page/view-tc-help-page.component';
import { UiItemMakerModule } from "../ui-item-maker/ui-item-maker.module";
import { PanelScheduledSessionsComponent } from './panel-scheduled-sessions/panel-scheduled-sessions.component';
import { PanelTwStatementComponent } from './panel-tw-statement/panel-tw-statement.component';
import { ViewTcSystemMessagesComponent } from './view-tc-system-messages/view-tc-system-messages.component';
import { ViewTcSaKitDashboardComponent } from './view-tc-sa-kit-dashboard/view-tc-sa-kit-dashboard.component';
import { ViewSaKitDashboardComponent } from './view-sa-kit-dashboard/view-sa-kit-dashboard.component';
import { PanelAccommodationsComponent } from './panel-accommodations/panel-accommodations.component';
import { ViewTcirReportsComponent } from './view-tcir-reports/view-tcir-reports.component';
import { IssueThreadBlockComponent } from './issue-thread-block/issue-thread-block.component';


@NgModule({
    imports: [
      CommonModule,
      FullCalendarModule,
      ReactiveFormsModule,
      UiPartialModule,
      UiTestadminModule,
      MarkdownModule,
      UiTestrunnerModule,
      FormsModule,
      UiTestctrlRoutingModule,
      AgGridModule,
      ScrollingModule,  
      ClipboardModule,
      MatDialogModule,
      MatAutocompleteModule,
      MatFormFieldModule,
      MatInputModule,
      MatDatepickerModule,
      MatMomentDateModule,
      MatChipsModule,
      MatButtonModule,
      MatSlideToggleModule,
      MatTooltipModule,
      MatCheckboxModule,
      MatSelectModule,
      MatMenuModule,
      CKEditorModule,
      UiItemMakerModule
    ],
  declarations: [
    ViewTcCreateAccountComponent,
    ViewTcDashboardComponent,
    ViewTcNewTestWindowComponent,
    ViewTcTestWindowComponent,
    ViewTcAccountsComponent,
    ViewTcItemBankComponent,
    ViewTcItemBankItemComponent,
    ViewTcItemBankFrameworkComponent,
    ViewTcTestDesignComponent,
    ViewTcInstitutionsComponent,
    ViewTcSebGenComponent,
    ViewTcReportingComponent,
    ViewTcBoardsSchoolsComponent,
    ViewTcStudentsTeachersClassesComponent,
    TcTableBoardsComponent,
    TcTableSchoolsComponent,
    TcTableStudentsComponent,
    TcTableTeachersComponent,
    TcTableClassesComponent,
    TcTableCommonComponent,
    TcTableCommonImportComponent,
    ViewTcSchoolComponent,
    ViewTcBoardsComponent,
    ViewTcClassesComponent,
    ViewTcAsmtSessionItemAnalysisComponent,
    ViewTcTeachersComponent,
    ViewTcStudentsComponent,
    PanelTwtarComponent,
    PanelReportedIssuesComponent,
    PanelStudentLookupComponent,
    PanelTcStudentAttemptInfoComponent,
    ViewTcirDashboardTempComponent,
    PanelStudentExceptionsComponent,
    ViewTcdeDashboardComponent,
    PanelRealTimeAuditsComponent,
    PanelUnsubmissionRequestsComponent,
    PanelTwSummaryComponent,
    PanelSchoolParticipComponent,
    ViewTcRolesComponent,
    ViewTcHelpPageComponent,
    PanelScheduledSessionsComponent,
    PanelTwStatementComponent,
    ViewTcSystemMessagesComponent,
    ViewTcSaKitDashboardComponent,
    ViewSaKitDashboardComponent,
    PanelAccommodationsComponent,
    ViewTcirReportsComponent,
    IssueThreadBlockComponent,
  ],
  exports: [
    TcTableCommonComponent,
    TcTableCommonImportComponent,
    PanelTwStatementComponent
  ]
})
export class UiTestctrlModule { }
