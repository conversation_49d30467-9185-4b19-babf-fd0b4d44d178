import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService, getFrontendDomain } from 'src/app/api/auth.service';
import { UserRoles } from 'src/app/api/models/roles';
import { MemDataPaginated } from '../../ui-partial/paginator/helpers/mem-data-paginated';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { RoutesService } from 'src/app/api/routes.service';
import { AccountType } from 'src/app/constants/account-types';
import { BreadcrumbsService, IBreadcrumbRoute } from 'src/app/core/breadcrumbs.service';
import { LangService } from 'src/app/core/lang.service';
import { ScrollService } from 'src/app/core/scroll.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { MyCtrlOrgService } from '../my-ctrl-org.service';
import { PageModalController, PageModalService } from '../../ui-partial/page-modal.service';
import { userGroupSummaryColumns, userGroupTypes } from '../view-tc-dashboard/types/tc-summary';
import { FormControl, FormGroup } from '@angular/forms';

export interface ICtrlRole {
  uid: number,
  ur_id: number,
  group_id: number,
  first_name: string,
  last_name: string,
  contact_email: string,
  role_type: string,
  created_on: string | Date | null,
  is_revoked: string | boolean | number,
  revoked_on: string | Date | null
}

export interface IAuthorityClassification {
  AuthorityClassification?: IClassificationType[]
}

export interface IClassificationType {
  ClassificationType?: string,
}

interface ICtrlRoleType{
  name: string,
  value: string
}

@Component({
  selector: 'view-tc-roles',
  templateUrl: './view-tc-roles.component.html',
  styleUrls: ['./view-tc-roles.component.scss']
})
export class ViewTcRolesComponent implements OnInit {
  
  public userGroupSummaryColumns = userGroupSummaryColumns;
  public userGroupTypes = userGroupTypes;
  public routeSub: Subscription;
  public breadcrumb: IBreadcrumbRoute[];
  public ctrlRoles: ICtrlRole[];
  
  public ctrlRoleTypes:ICtrlRoleType[] = [
    {name: "Test_Controller (Data Retriever)", value: UserRoles.TEST_CTRL_DATA_RETR},
    {name: "Test_Controller (Issue Tracker)", value: UserRoles.TEST_CTRL_ISSUE_TRACKER},
    {name: "Test_Controller (Certification Body Liaison)", value: UserRoles.TEST_CTRL_LIAS_CERT_BODY},
    {name: "Test_Controller (Internal Liaison)", value: UserRoles.TEST_CTRL_LIAS_INTERNAL},
    {name: "Test_Controller (Test Administrator Liaison)", value: UserRoles.TEST_CTRL_LIAS_TEST_ADMIN},
    {name: "Test_Controller (Meta Regulator)", value: UserRoles.TEST_CTRL_META_REG},
    {name: "Test_Controller (Score Validator)", value: UserRoles.TEST_CTRL_SCORE_VALID},
    {name: "Test_Controller (Window Monitor)", value: UserRoles.TEST_CTRL_WINDOW_MONITOR},
    {name: "Test_Controller (Issue Reviewer)", value: UserRoles.TEST_CTRL_ISSUE_REVIEWER},
    {name: "Test_Controller (Data Exporter)", value: UserRoles.TEST_CTRL_DATA_EXPORTER},
    {name: "Support", value: UserRoles.DEBUG},
  ]
    
  districts = 'districts';
  isUidForm:boolean;
  public ctrlRolesTable: MemDataPaginated<ICtrlRole>;
  pageModal: PageModalController;
  formGroup = new FormGroup({
    form_email: new FormControl(),
    form_uid: new FormControl(),
    form_role_type: new FormControl(),
    test_window_id: new FormControl(),
    form_first_name: new FormControl(),
    form_last_name: new FormControl(),
  })
  isLoaded: boolean = false;
  isInviteNewAccount: boolean; 
  constructor(private loginGuard: LoginGuardService, // 
    private router: Router,
    private route: ActivatedRoute,
    private breadcrumbsService: BreadcrumbsService,
    private scrollService: ScrollService,
    private lang: LangService,
    private auth: AuthService,
    private pageModalService: PageModalService,
    private routes: RoutesService,
    private myCtrlOrg: MyCtrlOrgService,
    private whitelabel: WhitelabelService,) { }
    private ctrlRoleTypeMap: Map<string, string> = new Map();
  ngOnInit(): void {
    this.scrollService.scrollToTop();
    this.loginGuard.activate([AccountType.TEST_CTRL]);
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.routeSub = this.route.params.subscribe(routeParams => {
      // this.setupId = routeParams['setupId'];
      this.breadcrumb = [
        this.breadcrumbsService.TESTCTRL_DASHBOARD(),
        this.breadcrumbsService._CURRENT('Test_Controller Roles', this.router.url),
      ]
      this.loadControllerRoles()
    });
    this.ctrlRoleTypes.map(
      ctrlRoleType => {
        this.ctrlRoleTypeMap.set(ctrlRoleType.value, ctrlRoleType.name)
      }
    )
  }

  loadControllerRoles() {
    this.isLoaded = false;
    this.auth.apiGet(this.routes.TEST_CTRL_ROLES, 0)
    .then(res => {
      this.ctrlRoles = res[0].records;
      //processing the classification, turning it from an object of array to a string of types seperated by comas.
      for(let ctrlRole of this.ctrlRoles){
        ctrlRole.role_type = this.ctrlRoleTypeMap.get(ctrlRole.role_type)
        ctrlRole.created_on = ctrlRole.created_on? new Date(ctrlRole.created_on) : null
        ctrlRole.revoked_on = ctrlRole.revoked_on? new Date(ctrlRole.revoked_on) : null
        ctrlRole.is_revoked = ctrlRole.is_revoked == 1? "True":"False"
      }
      this.ctrlRolesTable = new MemDataPaginated({
        data: this.ctrlRoles,
        pageSize: 10,
        sortSettings: {}
      });
      this.ctrlRolesTable.refresh();
      this.isLoaded = true;
    })
    .catch((e)=>{
      if(e.message == "GROUP_ROLE_REQ_FOR_ROUTE"){
        this.loginGuard.quickPopup("This account no longer has permission to view this page. Please contact support.")
      }
    });
  }
  formateDate(date : Date | null){
    if(!date){
      return "";
    }
    const readableDate = date.toLocaleDateString('en-US', {
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
    
    const readableTime = date.toLocaleTimeString('en-US', {
      hour: '2-digit', 
      minute: '2-digit', 
      hour12: true
    });
    return `${readableDate}, ${readableTime}`
  }
  cModal() {
    return this.pageModal.getCurrentModal();
  }

  cmc() { return this.cModal().config; }
  newCtrlRoleModalStart() {
    const config: any = {};
    this.pageModal.newModal({
      type: 'ctrl_role',
      config,
      finish: config => this.newCtrlRoleModalFinish(config)
    });
  }
  newCtrlRoleModalFinish(config) {
    const controls = this.formGroup.controls;
    let group_id = this.whitelabel.getSiteText("tc_group_id")
    if(controls.form_role_type.value == UserRoles.DEBUG){
      group_id = "1"
    }
    
    const payload = {
      email: this.isUidForm ? null : controls.form_email.value,
      uid: this.isUidForm ? controls.form_uid.value : null,
      role_type: controls.form_role_type.value||'',
      test_window_id: controls.test_window_id.value,
      group_id: group_id || null,
      invite: this.isInviteNewAccount,
      first_name: controls.form_first_name.value || "",
      last_name: controls.form_last_name.value || "",
      emailLinkDomain: getFrontendDomain(),
    }
    if(!payload.group_id){
      this.loginGuard.quickPopup("Group Id is missing.")
    }
    else{
      this.auth.apiCreate(this.routes.TEST_CTRL_ROLES, payload).then(res => {
        this.resetForm();
        this.loginGuard.quickPopup("Created role successfully.")
        this.loadControllerRoles();
      }).catch(e => {
          if(e.message){
            this.loginGuard.quickPopup(e.message)
          }
          else{
            this.loginGuard.quickPopup("Failed to create new user role.")
          }
        }
      )
    }
  }
  revokeRole(ur_id: number){
    this.loginGuard.confirmationReqActivate({
      caption: "Do you confirm to revoke this user role?",
      confirm: () => {
        this.auth.
        apiRemove(this.routes.TEST_CTRL_ROLES, ur_id)
        .then(res => {
          for(let ctrlRole of this.ctrlRoles){
            if(ctrlRole.ur_id == ur_id){
              ctrlRole.is_revoked = "True";
              ctrlRole.revoked_on = new Date()
            }
          }
        })
        .catch(e => {
          if(e.message){
            this.loginGuard.quickPopup(e.message)
          }
          else{
            this.loginGuard.quickPopup("Failed to revoke user role.")
          }
        })
      },
      close: () => {}
  });
  }
  pageChanged() {
    // if (!this.classroomSelections.isAllSelected) {
    //   this.classrooms.forEach(classroom => classroom.__isSelected = false);
    // }
  }
  resetForm() {
    this.formGroup.controls.form_email.reset();
    this.formGroup.controls.form_uid.reset();
    this.formGroup.controls.form_role_type.reset();
    this.formGroup.controls.test_window_id.reset();
  }

  isABED(){
    return this.whitelabel.isABED();
  }

}
