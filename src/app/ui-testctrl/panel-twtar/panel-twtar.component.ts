import { <PERSON><PERSON>nent, OnInit, OnDestroy, OnChanges, SimpleChanges, Input } from '@angular/core';
import { LoginGuardService } from '../../api/login-guard.service';
import { ITestWindow, MyCtrlOrgService } from '../my-ctrl-org.service';
import { AuthService, DB_TIMEZONE } from '../../api/auth.service';
import { RoutesService } from '../../api/routes.service';
import { ScrollService } from '../../core/scroll.service';
import { LangService } from '../../core/lang.service';
import { ActivatedRoute, Router } from '@angular/router';
import { BreadcrumbsService, IBreadcrumbRoute } from '../../core/breadcrumbs.service';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { RowNodeEvent } from 'ag-grid-community/dist/lib/entities/rowNode';
import * as moment from 'moment-timezone';
import { mtz } from 'src/app/core/util/moment';
import { dateAndTimeToDbDate, initTimeOptions } from 'src/app/ui-partial/input-time/input-time.component';
import { FormControl } from '@angular/forms';
import { dateColDef } from 'src/app/core/util/datecoldef';
import { PERUSAL_TYPES, PERUSAL_END_TYPES } from './../../ui-schooladmin/sa-tw-statement/model/perusal';

const twtdarPatchFields = [
  'num_sub_sessions',
  'type_slug',
  'slug',
  'order',
  'lang',
  'form_code',
  'test_design_id',
  'long_name',
  'qs_is_test_design',
  'is_secured',
  'is_scheduled',
  'is_questionnaire',
  'is_sample',
  'test_duration',
  'is_field_test',
  'test_date_start',
  'test_date_end',
  'perusal_type',
  'perusal_end_type',
  'perusal_offset_hours',
  'perusal_duration_hours',
  'perusal_date_start',
  'perusal_date_end',
  'is_schedule_range',
  'is_classroom_common_form',
  'can_credential',
  'is_pipeline_exclude',
  'req_sd_lang',
  'req_sd_lang_not',
  'subsession_meta',
  'user_metas_filter',
  'accomm_user_meta_constraint',
  'max_condition_on_option',
  'selection_order',
  'tqr_ovrd_td_id',
  'is_active_for_qa',
  'is_active_for_auth',
  'is_active',
  'is_print', // add print config
  'is_marking_req',
  'hard_close_on',
  'is_date_restricted',
  'is_score_entry',,
  'is_session_duration_excluded',
  'is_session_name_excluded',
  'print_before_days',
  'upload_until_days',
  'is_exclude_weekends',
  'exception_dates',
]

@Component({
  selector: 'panel-twtar',
  templateUrl: './panel-twtar.component.html',
  styleUrls: ['./panel-twtar.component.scss']
})
export class PanelTwtarComponent implements OnInit, OnChanges {

  PERUSAL_TYPES = PERUSAL_TYPES;
  PERUSAL_END_TYPES = PERUSAL_END_TYPES;

  @Input() testWindowId:number; // this is only used to track changes, central services is the source of truth so this never actually gets fed in anywhere

  perusalTypeOptions = [
    {value: PERUSAL_TYPES.NO_LIMIT, caption: "No limit (until end of the test window)"},
    {value: PERUSAL_TYPES.RELATIVE_SESSION_START, caption: "Relative to: Session start"},
    {value: PERUSAL_TYPES.RELATIVE_FIRST_STUDENT_START, caption: "Relative to: First student start"},
    {value: PERUSAL_TYPES.STATIC_TIME, caption: "Absolute time"},
  ]

  perusalEndOptions = [
    { value: PERUSAL_END_TYPES.NO_LIMIT, caption: "No limit (until end of the test window)"},
    { value: PERUSAL_END_TYPES.ASSESSMENT_END, caption: "End of the assessment"},
    { value: PERUSAL_END_TYPES.DURATION_HOURS, caption: "Custom duration hours"},
    { value: PERUSAL_END_TYPES.STATIC_TIME, caption: "Absolute time"},
  ]

  renderPerusalCaption(perusal_type: PERUSAL_TYPES){
    const perusalOption = this.perusalTypeOptions.find(o => o.value == perusal_type)
    return perusalOption?.caption
  }

  renderPerusalEndCaption(perusal_end_type: PERUSAL_END_TYPES){
    const perusalEndOption = this.perusalEndOptions.find(o => o.value == perusal_end_type)
    return perusalEndOption?.caption
  }

  selectedTwTdAr:any;
  selectedTd:any;
  isTdRestrictedToPrevQSet:boolean = true;
  isAssigning:boolean;
  isRightPanelVisible: boolean = true;
  nowDate: string = "";
  minDate: string = "";
  TWEndDate: string = "";
  TWStartDate: string = "";
  disableDatePicker: boolean = false;
  showInactiveTwtdars: boolean = false;
  showActiveTwtdars: boolean = true;
  showActiveForAuthTwtdars: boolean = false;
  showSampleTwtdars: boolean = false;
  showSecureTwtdars: boolean = true;
  showFieldTestTwtdars: boolean = true;
  isAdvancedEditing: boolean = false;
  newExceptionDate: string;
  twarRecords = [];
  twarGridOptions:any = 
  {
    columnDefs: [
      { headerName:'id', field:'id', width:100, checkboxSelection:true },
      { headerName: this.lang.tra('tc_tw_twtdar_col_sample'), field:'is_sample', width:110 },
      { headerName: this.lang.tra('tc_tw_twtdar_col_ass_start_date'), field:'test_date_start', ...dateColDef(this.lang, 'datefmt_timestamp')}, // todo:DB_DATA_MODEL todo:WHITELABEL activate based on envrionment
      { headerName: this.lang.tra('tc_tw_twtdar_col_ass_end_date'), field:'test_date_end', ...dateColDef(this.lang, 'datefmt_timestamp')},
      { headerName: this.lang.tra('tc_tw_twtdar_col_name'), field:'long_name' },
      { headerName: this.lang.tra('mrkg_language'), field:'lang', width:110 },
      // { headerName: this.lang.tra('tc_tw_twtdar_col_fr_immersion'), field:'is_fi', width:150 }, // todo: consider re-activation for EQAO
      { headerName: this.lang.tra('tc_tw_twtdar_col_ass_code'), field:'type_slug' },
      { headerName: 'Form', field:'form_code', width:80 },
      { headerName: this.lang.tra('tc_tw_twtdar_col_duration'), field:'test_duration', width:110  },
      { headerName: this.lang.tra('tc_tw_twtdar_col_published_id'), field:'test_design_id', width:110},
      { headerName: this.lang.tra('tc_tw_twtdar_col_reg_override_pub_id'), field:'tqr_ovrd_td_id', width:110},
      { headerName: 'n_items', field:'n_items', width:100},
      { headerName: 'n_item_numbers', field:'n_item_numbers', width:100},
      { headerName: 'total_item_score_points', field:'total_item_score_points', width:100},
      { headerName: this.lang.tra('tc_tw_twtdar_col_session_sel_order'), field:'selection_order', width:100},
      { headerName:'Slug', field:'slug' },
      { headerName: this.lang.tra('tc_tw_twtdar_col_comp_code'), field:'component_code' },
      { headerName: this.lang.tra('tc_tw_twtdar_col_is_field_test'), field:'is_field_test' },
      { headerName: this.lang.tra('tc_tw_twtdar_col_bank_id'), field:'source_item_set_id', width:110 },
      { headerName: this.lang.tra('tc_tw_twtdar_col_bank_name'), field:'qs_name' },
      { headerName: this.lang.tra('tc_tw_twtdar_col_order'), field:'order', width:100},
      { headerName: this.lang.tra('tc_tw_twtdar_col_panels_forms'), field:'num_forms', width:100},
      { headerName: this.lang.tra('tc_tw_twtdar_col_active'), field:'is_active'},
      { headerName: 'Active for Auths?', field:'is_active_for_auth'},
      { headerName: 'Active for QA', field:'is_active_for_qa'},
      { headerName: this.lang.tra('tc_tw_twtdar_col_superceded'), field:'isNotLatestTd', width:100 },
      { headerName: this.lang.tra('tc_tw_twtdar_col_published_by'), field:'td_created_by'},
      { headerName: this.lang.tra('tc_tw_twtdar_col_published_on'), field:'td_created_on' },
      { headerName: this.lang.tra('tc_tw_twtdar_col_assigned_on'), field:'td_assigned_on' },
      { headerName: this.lang.tra('tc_tw_twtdar_col_assigned_by'), field:'td_assigned_by' },
      { headerName:'close_on', field:'hard_close_on' },
      { headerName:'is_date_restricted', field:'is_date_restricted' },
      { headerName:'is_school_allowed_strict', field:'is_school_allowed_strict' },
      { headerName:'is_active_for_qa', field:'is_active_for_qa' },
      { headerName: this.lang.tra('tc_tw_twtdar_col_secured'), field:'is_secured' },
      // todo:DB_DATA_MODEL todo:WHITELABEL activate based on envrionment
      // { headerName:'Is Excluded from Psych Pipeline?', field:'is_pipeline_exclude' },
      // { headerName:'Subsession Meta', field:'subsession_meta' },
      // { headerName:'Accommodations User Meta Constraint', field:'accomm_user_meta_constraint' },
      // { headerName:'Common Form?', field:'is_classroom_common_form', width:110  },
      // { headerName:'Questionnaire?', field:'is_questionnaire', width:110 },
      // { headerName:'User Metas Filter', field:'user_metas_filter' },
      // { headerName:'max_condition_on_option', field:'max_condition_on_option' },
      // { headerName:'Can Credential?', field:'can_credential' },
      { headerName:'Constrain Lang?', field:'req_sd_lang' },
      { headerName:'Constrain Lang (ante)?', field:'req_sd_lang_not' },
      { headerName:'Marking Req?', field:'is_marking_req' },
      { headerName:'Print?', field:'is_print' },
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };

  tdRecords = [];
  tdGridOptions:any = {
    columnDefs: [
      { headerName:'Alloc?', field:'isAllocated', width:80 },
      { headerName:'Published ID', field:'id', width:110, checkboxSelection:true },
      { headerName:'Name', field:'name' },
      { headerName:'Panels/Forms', field:'num_forms', width:100 },
      { headerName:'Date', field:'created_on' },
      { headerName:'Published by', field:'td_created_by' },
      { headerName:'Language', field:'lang', width:100 },
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };
  invalidatedTestAttempts: boolean = false;
  invalidatedtaRecords = [];
  invalidatedtaGridOptions:any = {
    columnDefs: [
      { headerName:'Test Attempt Id', field:'ta_id', width:110 },
      { headerName:'Test Session Id', field:'ts_id', width:110 },
      { headerName:'Type Slug', field:'type_slug', width:110 },
      { headerName:'Sample School?', field:'is_sample_school', width:80},
      { headerName:'School Name', field:'school_name', width:250 },
      { headerName:'Test Design Id', field:'test_design_id', width:110  },
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };
  constructor(
    private loginGuard: LoginGuardService, //
    private router:Router,
    private route:ActivatedRoute,
    private breadcrumbsService:BreadcrumbsService,
    private scrollService:ScrollService,
    public lang:LangService,
    private auth:AuthService,
    private routes:RoutesService,
    public myCtrlOrg:MyCtrlOrgService,
    private whitelabel:WhitelabelService,
  ) { }

  ngOnInit(): void 
  {  
  }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes.testWindowId)
    {
      this.twarRecords = [];
      this.tdRecords = [];
      this.loadTwTdAr();
      this.initDates();
    }; 
  }

  initDates()
  {
    this.nowDate = this.formatDateTimeForDatePicker(null, true);
    this.TWStartDate = this.formatDateTimeForDatePicker(this.myCtrlOrg.getTestWindowSummary().date_start, false);
    this.TWEndDate = this.formatDateTimeForDatePicker(this.myCtrlOrg.getTestWindowSummary().date_end, false);
    this.minDate = 
    this.isTestWindowStartDateAfterCurrentDate(this.nowDate, this.TWStartDate) ? this.TWStartDate : this.nowDate; 
    this.disableDatePicker = this.isTestWindowEndDateBeforeCurrentDate(this.nowDate, this.TWEndDate);
    
    console.log("Now: ", this.nowDate, "TWEndsOn/Max Date Allowed: ", this.TWEndDate, 
    "TWStartsOn: ", this.TWStartDate, "Min Date Allowed: ", this.minDate);
  }

  datePickerValueToDisplay()
  {
    if (this.selectedTwTdAr.__raw.no_test_date_start)
    {
      return this.minDate;
    }

    return this.selectedTwTdAr.test_date_start;
  }

  isEditingSelectedTwtdar()
  {
    const t = this.selectedTwTdAr;
    if (t)
    {
      return t.__isEditing;
    }

    return false;
  }  

  clearTwtarSelection(){
    this.selectedTwTdAr = null;
    const nodes = this.twarGridOptions.api.getSelectedNodes()
    if (nodes){
      nodes.forEach(node => {
        node.setSelected(false, true);
      })
    }
  }

  async createTwTdAr(){
    const jsonConfig = prompt('Post JSON configuration (optional)');
    let config;
    try {
      config = jsonConfig ? JSON.parse(jsonConfig) : {}
    }
    catch (e){
      alert('INVALID JSON')
      throw new Error(e);
    }
    const insertData = {};
    for (let prop of twtdarPatchFields){
      const val = config[prop];
      if (val !== undefined && val !== null){
        insertData[prop] = val
      }
    }
    if (confirm(`Are you sure you want to create a record with the following starting fields? \n\n ${JSON.stringify(insertData, null, 2)}`)){
      this.clearTwtarSelection()
      const record = await this.myCtrlOrg.createTwTdAr(insertData);
      this.twarRecords.push(this.sanitizeTwtdar(record));
      this.reloadTwTdarGridChanges()
      // this.selectedTwTdAr = record;
      this.clearJsonPreview()
    }
  }
  async removeTwTdArForAuthors(){

  }
  async removeTwTdAr(){
    const t = this.selectedTwTdAr;
    if (prompt(`To confirm the removal of this TWTDAR record, please enter "REMOVE" followed by the ID.`) === `REMOVE${t.id}`){
      await this.myCtrlOrg.removeTwTdAr(t.id)
      alert('The record was removed.');
      await this.loadTwTdAr();
      this.clearJsonPreview();
      this.reloadTwTdarGridChanges();
    }
    else{
      alert('The record was NOT removed.');
    }
  }
  async invalidateTestAttempts(){
    const res = await this.myCtrlOrg.invalidateTestAttempts();
    this.invalidatedtaRecords = res
    this.loginGuard.quickPopup(`Invalidated ${res.length} test attempts. ${res.length > 0 ? 'You can export the invalidated attempts at buttom of the page.': ''}`)
    if(res.length > 0){
      this.invalidatedTestAttempts = true;
    }
  }
  reloadTwTdarGridChanges(){
    this.twarGridOptions.api.setRowData(this.twarRecords);
    // this.twarGridOptions.api.redrawRows();
    // this.twarGridOptions.api.refreshCells();
  }
  editStart(){
    // console.log(this.myCtrlOrg.selectedWindow);
    // console.log(this.formatDateForDatePicker(this.myCtrlOrg.getTestWindowSummary().date_start, true));
    const t = this.selectedTwTdAr;
    t.__isEditing = true;
    t.__preEdit = {
      ... t.__raw,
    }
    if (!t.test_design_id){
      t.__isTdOverrideActive = true;
    }
  }

  isSlugCopyMode(){
    return this.whitelabel.getSiteFlag('IS_TWTAR_SLUG_COPY');
  }
  applySlugCopy(){
    if (this.isSlugCopyMode()){
      this.selectedTwTdAr.__raw.slug = this.selectedTwTdAr.__raw.type_slug
    }
  }

  isRawPropNumericChecked(propName:string){
    return (this.selectedTwTdAr.__raw[propName] == 1)
  }
  setRawPropCheckedNumeric(propName:string, val:number){
    this.selectedTwTdAr.__raw[propName] = val
  }
  toggleRawPropCheckedNumeric(propName:string){
    if (this.isRawPropNumericChecked(propName)){
      this.selectedTwTdAr.__raw[propName] = 0 
    }
    else {
      this.selectedTwTdAr.__raw[propName] = 1
    }
  }

  addExceptionDate(): void {
    if (this.newExceptionDate) {
      const date = moment(this.newExceptionDate).format('YYYY-MM-DD');
      this.selectedTwTdAr.__raw["exception_dates"].push(date);
      this.newExceptionDate = ''; // Clear the input field
    }
  }

  removeExceptionDate(index: number): void {
    this.selectedTwTdAr.__raw["exception_dates"].splice(index, 1);
  }

  
  editCancel() {
    if (this.selectedTwTdAr.__preEdit){
      for (let prop of twtdarPatchFields){
        this.selectedTwTdAr[prop] = this.selectedTwTdAr.__preEdit[prop]; 
        this.selectedTwTdAr.__raw[prop] = this.selectedTwTdAr[prop];
      }
    }

    this.selectedTwTdAr.__isEditing = false;
    this.clearJsonPreview();
  }

  timeOptions = initTimeOptions();
  datetimeColumns = {
    'test_date_start': {
      field: 'test_date_start',
      dateControl: new FormControl(),
      timeControl: {caption: '', val: ''},
      enableField: 'is_date_restricted'
    },
    'test_date_end': {
      field: 'test_date_end',
      dateControl: new FormControl(),
      timeControl: {caption: '', val: ''},
      enableField: 'is_schedule_range'
    },
    'perusal_date_start': {
      field: 'perusal_date_start',
      dateControl: new FormControl(),
      timeControl: {caption: '', val: ''},
    },
    'perusal_date_end': {
      field: 'perusal_date_end',
      dateControl: new FormControl(),
      timeControl: {caption: '', val: ''},
    },
  }

  isReqFieldMissing(){
    const record = this.selectedTwTdAr.__raw;
    const isMissingPerusalOffset = [PERUSAL_TYPES.RELATIVE_FIRST_STUDENT_START, PERUSAL_TYPES.RELATIVE_SESSION_START].includes(record.perusal_type) && record.perusal_offset_hours == undefined;
    const missingPerusalDurHours = record.perusal_end_type == PERUSAL_END_TYPES.DURATION_HOURS && !record.perusal_duration_hours;
    const missingPerusalStartTime = record.perusal_type == PERUSAL_TYPES.STATIC_TIME && !this.datetimeColumns.perusal_date_start.dateControl.value;
    const missingPerusalEndTime = record.perusal_end_type == PERUSAL_END_TYPES.STATIC_TIME && !this.datetimeColumns.perusal_date_end.dateControl.value;
    
    return isMissingPerusalOffset || missingPerusalDurHours || missingPerusalStartTime || missingPerusalEndTime
  }

  async editSave(isPostRefreshSuppressed:boolean = false){
    const t = this.selectedTwTdAr;
    const patchData: any = {};
    console.log('t', t.__raw)
    
    for (let prop of twtdarPatchFields) {
      // If the field is a date field, convert the date and time to a single string
      const dateProp = this.datetimeColumns[prop];
      if(dateProp) {
        if (dateProp.dateControl.value){
          const date:string = dateAndTimeToDbDate(dateProp.dateControl.value, dateProp.timeControl.val, 'HH:mm');
          patchData[prop] = date;
        }
        // If the date field is empty, reset the date field to null
        if(!dateProp.dateControl.value) {
          patchData[prop] = null;
        }
        // If associated enable prop field is configured and is disabled, reset the date field to null
        if(dateProp.enableField !== undefined && t.__raw[dateProp.enableField] != 1) {
          patchData[prop] = null;
        }
        continue;
      }
      patchData[prop] = t.__raw[prop];
    }

    // Remove any perusal configrations not used by the perusal type (for clarity)
    if (patchData.perusal_type == ""){
      patchData.perusal_type = null;
    }
    if (patchData.perusal_end_type == ""){
      patchData.perusal_end_type = null;
    }
    if (patchData.perusal_type !== PERUSAL_TYPES.STATIC_TIME){
      patchData.perusal_date_start = null
    }
    if (patchData.perusal_end_type !== PERUSAL_END_TYPES.STATIC_TIME){
      patchData.perusal_date_end = null
    }
    if (![PERUSAL_TYPES.RELATIVE_SESSION_START, PERUSAL_TYPES.RELATIVE_FIRST_STUDENT_START].includes(patchData.perusal_type)){
      patchData.perusal_offset_hours = null
    }
    if (patchData.perusal_end_type !== PERUSAL_END_TYPES.DURATION_HOURS){
      patchData.perusal_duration_hours = null
    }

    // limit the creation of resp_config only to scanned assessments
    const isScanAssessment = t.__raw.question_requiring_scan > 0
    if(isScanAssessment) {
      patchData['isScanAssessment'] = isScanAssessment;
    }
    try {
      t.__isSaving = true;
      await this.myCtrlOrg.patchTwTdAr(t.id, patchData);
      if (!isPostRefreshSuppressed){
        await this.updateEditedTwtdarRow(t.id);
      }
    }
    catch (e) {
      alert('Save failed');
      this.editCancel();
    }

    t.__isSaving = false;
    this.clearJsonPreview();
  }

  renderDateTime(dateStr:string){
    if (dateStr === null) {
      return "No date set"
    }
    return mtz(dateStr).format(this.lang.tra('datefmt_dashboard_long'))
  }

  getSelectedRowNode(){
    const selectedNodes = this.twarGridOptions.api.getSelectedNodes();
    if (selectedNodes.length > 0) {
      return selectedNodes[0];
    }
    return null;
  }
  selectTwtarPrev(){
    const selectedNode = this.getSelectedRowNode();
    if (selectedNode) {
      const previousRow = selectedNode.previousSibling;
      if (previousRow) {
        previousRow.setSelected(true);
        selectedNode.setSelected(false);
      }
    }
  }
  selectTwtarNext(){
    const selectedNode = this.getSelectedRowNode();
    if (selectedNode) {
      const nextRow = selectedNode.nextSibling;
      if (nextRow) {
        nextRow.setSelected(true);
        selectedNode.setSelected(false);
      }
    }
  }

  async updateEditedTwtdarRow(editedTwtdarRowId: number): Promise<void> {
    // update twtdar table itself
    await this.loadTwTdAr();
    this.twarGridOptions.api.refreshCells();

    // update selected twtdar
    let updatedTwtdarRowIdx = this.twarRecords.findIndex(record => record.id === editedTwtdarRowId);
    if (updatedTwtdarRowIdx == -1)
    {
      console.log("In function editSave(), no twtdar row idx was found to select.");
      this.selectedTwTdAr = null;
    }
    
    else {
      this.selectedTwTdAr = this.twarRecords[updatedTwtdarRowIdx];
    }

    await this.loadCurrentTwTdAr();

    if (this.selectedTwTdAr != null) {
      this.selectedTwTdAr.__isEditing = false;
    }
    
  }

  activeSelectedTwtar(){
    // todo: pre-activation checks!
    this.selectedTwTdAr.__raw.is_active = 1
  }

  initTwtarStartDate(){
    this.selectedTwTdAr.__raw.is_date_restricted = 1
  }
  removeTwtarStartDate(){
    this.selectedTwTdAr.__raw.is_date_restricted = 0
  }

  // updateEditedTwtdarRow(newData: any)
  // {
  //   // note: 
  //   // allows updating of selected twtdar row and twtdarRecord modified in the ag-grid directly
  //   // might be good to use this function in editSave()
  //   // ... if loading the data from the API on each edit is too expensive
  //   // but for simplicity, I am commenting this out for now
  
  //   // newData is an object
  //   const updatedTwtdarRowIdx = this.twarRecords.findIndex(record => record.id === newData.id);
  //   if (updatedTwtdarRowIdx !== -1)
  //   {
  //     console.error("In function updateEditedTwtdarRow(), no twtdar row idx was found.");
  //   }

  //   for (let key of Object.keys(newData))
  //   {
  //     if (key in this.selectedTwTdAr)
  //     {
  //       this.selectedTwTdAr[key] = newData[key];
  //       // modify raw and preedit here
  //     }

  //     if (updatedTwtdarRowIdx !== -1 && key in this.twarRecords[updatedTwtdarRowIdx])
  //     {
  //       this.twarRecords[updatedTwtdarRowIdx][key] = newData[key];
  //     }
  //   }
  // }

  isTestWindowEndDateBeforeCurrentDate(currDate: string, TWEndDate: string): boolean
  {
    return moment(TWEndDate).isBefore(moment(currDate));
  }

  isTestWindowStartDateAfterCurrentDate(currDate: string, TWStartDate: string): boolean
  {
    return moment(TWStartDate).isAfter(moment(currDate));
  }

  isTestDesignLocked(){
    const t = this.selectedTwTdAr;
    if (!t.__isEditing){
      return true;
    }
    if (t.test_design_id && !t.__isTdOverrideActive){
      return true;
    }
    return false;
  }
  unlockTestDesignEdit(){
    const t = this.selectedTwTdAr;
    if (confirm('You should not directly the Published Test Design ID from this viw once it has been set. If you are using a more recent published version, please scroll down to make a selection.')){
      t.__isTdOverrideActive = true;
    }
  }

  jsonPreview:string;
  clearJsonPreview(){
    this.jsonPreview = null
  }
  renderJson(){
    const t = this.selectedTwTdAr;
    const jsonData = {};
    for (let prop of twtdarPatchFields){
      jsonData[prop] = t.__raw[prop]
    }
    this.jsonPreview = JSON.stringify(jsonData, null, 2)
    return this.jsonPreview
  }
  onCopyJson(){
    alert(`Copied assessment configuration for (${this.selectedTwTdAr.id}) ${this.selectedTwTdAr.name}`)
  }
  
  sanitizeTwtdar(record){
    const numericBooleans = [
      'isNotLatestTd',
      'is_active',
      'is_secured',
      'is_questionnaire',
      'is_sample',
      'is_outside_window',
      'is_scheduled',
      'is_classroom_common_form',
      'can_credential',
      'is_date_restricted',
      'is_pipeline_exclude',
      'req_sd_lang_not',
      'is_field_test',
      'req_sd_lang',
      'is_fi'
    ]
    const sanitizedRecord = 
    {
      ... record,
      __raw: record,
    }
    
    // todo: remove code that is adjusting date
    // if (sanitizedRecord.__raw.test_date_start == null)
    // {
    //   sanitizedRecord.__raw.no_test_date_start = true;
    //   sanitizedRecord.__raw.test_date_start = this.formatDateTimeForDatePicker(null, true);
    // }

    // else 
    // {
    //   sanitizedRecord.__raw.no_test_date_start = false;
    //   sanitizedRecord.__raw.test_date_start = this.formatDateTimeForDatePicker(sanitizedRecord.__raw.test_date_start, false);
    //   // console.log(sanitizedRecord.__raw.test_date_start);
    // }


    for (let prop of numericBooleans)
    {
      sanitizedRecord[prop] = (record[prop]==1) ? 'Yes' : 'No';
    }
    
    sanitizedRecord.long_name = sanitizedRecord.long_name || `{${sanitizedRecord.qs_name}}`;
    sanitizedRecord.td_created_on = this.auth.formatDateTimeForLocalTimezone(this.parseDate(sanitizedRecord.td_created_on)); 
    sanitizedRecord.td_assigned_on = this.auth.formatDateTimeForLocalTimezone(this.parseDate(sanitizedRecord.td_assigned_on)); 
    // todo: remove code that is adjusting date
    // sanitizedRecord.test_date_start = this.auth.formatDateTimeForLocalTimezone(this.parseDate(sanitizedRecord.test_date_start));
    return sanitizedRecord;
  }

  public parseDate(datetime: string): null | moment.Moment
  {
    return datetime == null ? null : moment(datetime);
  }

  async loadTwTdAr(){
    if (this.testWindowId){
      this.selectedTd = null;
      this.agGridShowLoadingOverlay(this.twarGridOptions.api);
      this.selectedTwTdAr = null;
      let records = await this.myCtrlOrg.getTwTdAr();
      const sanitizedRecords = [];
      records = this.hideInactiveTwtdarIfNeeded(records);
      records = this.hideFieldTestTwtdarIfNeeded(records);
      records = this.hideSampleTwtdarIfNeeded(records);
      for (let record of records){
        sanitizedRecords.push(this.sanitizeTwtdar(record));
      }
      this.twarRecords = sanitizedRecords;
      this.timeOptions = initTimeOptions();
      // setTimeout(() => {
      //   this.applyOperationalOnlyFilter();
      // }, 500)
    }
  }

  applyOperationalOnlyFilter(){
    const filterIsSample = this.twarGridOptions.api.getFilterInstance('is_sample'); 
    filterIsSample.setModel({
      filter: "yes",
      filterType: "text",
      type: "notContains",
    });
    this.twarGridOptions.api.onFilterChanged();
  }

  toggleRightPanel()
  {
    this.isRightPanelVisible = !this.isRightPanelVisible;
  }

  getToggleRightPanelBtnText(): string {
    return this.isRightPanelVisible ? this.lang.tra("abed_test_ctrl_panel_toggle_hide_btn") 
    : this.lang.tra("abed_test_ctrl_panel_toggle_show_btn");
  }

  exportTwarCsv(){
    this.twarGridOptions.api.exportDataAsCsv();
  }

  exportInvalidatedTestAttemptsCsv(){
    this.invalidatedtaGridOptions.api.exportDataAsCsv();
  }

  isEditOnSelect:boolean;
  handleSaveOnChange(){
    if (this.selectedTwTdAr && this.selectedTwTdAr.__isEditing){
      if (this.isEditOnSelect){
        this.editSave(true)
      }
      else {
        if (confirm('Save your changes?')){
          this.editSave(true)
        }
        else {
          this.editCancel()
        }
      }
    }
  }

  clearDateAndTimeEdit(){
    for(const column of Object.values(this.datetimeColumns)) {
      column.dateControl.setValue(null);
      column.timeControl.val = '';
    }
  }

  /** Initialize date and time controls based on their respective values from twtdar */
  initDateTimeEdit() {
    for(const column of Object.values(this.datetimeColumns)) {
      if(!this.selectedTwTdAr[column.field]) {
        continue;
      }

      const datetime: any = moment(this.selectedTwTdAr[column.field]).utc();
      const localDateTime = mtz(datetime);
      const time = localDateTime.format('HH:mm');
      column.dateControl.setValue(localDateTime.format('YYYY-MM-DD'));
      column.timeControl = {val: time, caption: time};
    }
  }

  /**
   * Initialize perusal times, defaults to:
   *  - start time: 1 hour after the test start time
   *  - end time: 3 hours after the test start time
   * (If no test time, do relative to current time as a placeholder)
   */
  initPerusalTime(isEnd: boolean = false) {
    const testStart = this.selectedTwTdAr.test_date_start ? moment(this.selectedTwTdAr.test_date_start) : moment();
    const perusalStart = testStart.add(1, 'hour');
    if (isEnd){
      const perusalEnd = perusalStart.add(3, 'hours');
      this.datetimeColumns['perusal_date_end'].dateControl.setValue(perusalEnd.format('YYYY-MM-DD'));
      this.datetimeColumns['perusal_date_end'].timeControl = {val: perusalEnd.format('HH:mm'), caption: perusalEnd.format('HH:mm')};
    } else {
      this.datetimeColumns['perusal_date_start'].dateControl.setValue(perusalStart.format('YYYY-MM-DD'));
      this.datetimeColumns['perusal_date_start'].timeControl = {val: perusalStart.format('HH:mm'), caption: perusalStart.format('HH:mm')};
    }
  }

  /** Unset perusal start or end time */
  resetPerusalTime(isEnd: boolean = false) {
    if (isEnd){
      this.datetimeColumns['perusal_date_end'].dateControl.setValue(null);
      this.datetimeColumns['perusal_date_end'].timeControl = {val: '', caption: ''};
    } else {
      this.datetimeColumns['perusal_date_start'].dateControl.setValue(null);
      this.datetimeColumns['perusal_date_start'].timeControl = {val: '', caption: ''};
    }
  }

  get renderPerusalWarning() {
    const isDateScrict = !this.selectedTwTdAr.perusal_configs?.is_date_unstrict
    return isDateScrict ? 'Perusal disabled, set the configuration to allow' : 'Perusal allowed at any time, set configuration to restrict'
  }

  async onTwarSelected($event: RowNodeEvent){
    const selectedRows = this.twarGridOptions.api.getSelectedRows();
    this.handleSaveOnChange()
    if (selectedRows.length > 0){
      const twTdAr = selectedRows[0];
      this.selectedTwTdAr = twTdAr;
      this.clearDateAndTimeEdit()
      this.initDateTimeEdit()

      await this.loadCurrentTwTdAr();
      if (this.isEditOnSelect){
        this.editStart()
      }
    }
    else {
      this.selectedTwTdAr = null;
    }
  }

  onTdSelected($event: RowNodeEvent){
    const selectedRows = this.tdGridOptions.api.getSelectedRows();
    if (selectedRows.length > 0){
      this.selectedTd = selectedRows[0];
    }
    else {
      this.selectedTd = null;
    }
  }

  async loadCurrentTwTdAr(){
    const twTdAr = this.selectedTwTdAr;
    if (twTdAr) {
      let qsSetID;
      this.selectedTd = null;
      if (this.isTdRestrictedToPrevQSet)
      {
        const {source_item_set_id} = twTdAr
        qsSetID = source_item_set_id;
      }

      this.tdRecords = await this.myCtrlOrg.getTwTdArTestDesigns(qsSetID);
      this.cleanUpTdData();
      this.markCurrentTdAlloc();
    }
  }

  cleanUpTdData()
  {
    this.tdRecords.forEach(row => 
    row.created_on = this.auth.formatDateTimeForLocalTimezone(this.parseDate(row.created_on)));
  }

  markCurrentTdAlloc(){
    const twTdAr = this.selectedTwTdAr;
    this.tdRecords.forEach(record => {
      record.isAllocated = null;
      if (record.id == twTdAr.test_design_id){
        record.isAllocated = 'x';
      }
    })
  }

  async assignTestDesign(twTdAr:any, test_design_id:number)
  {
    this.isAssigning = true;
    await this.myCtrlOrg.assignTwTdAr(twTdAr.id, test_design_id);
    this.isAssigning = false;
    twTdAr.test_design_id = test_design_id;
    this.markCurrentTdAlloc();
    this.twarGridOptions.api.refreshCells({})
    this.tdGridOptions.api.refreshCells({})
  }

  public convertDateForDB(date: string)
  {
    // assuming date is in the format that is returned by the function `formatDateForDatePicker()`
    let inputMoment = moment(date).tz(this.whitelabel.getTimeZone());
    let utcMoment = moment.utc(inputMoment);
    // console.log(utcMoment.format("YYYY-MM-DD HH:mm:ss"));
    return utcMoment.format("YYYY-MM-DD HH:mm:ss"); // e.g. return 2023-03-13 19:00:00
  }

  public formatDateTimeForDatePicker(dateTime: string, useCurrentDatetime: boolean = false): string
  {
    // assuming input "date" is always in UTC time
    // useCurrentTime ignores date (1st parameter) and uses current time and formats it

    // console.log(date, moment(date));
    if (dateTime == null && !useCurrentDatetime)
    {
      return "";
    }

    const momentToDateTime = (date: moment.Moment) => 
    {
      return date.year() + "-" + 
      String((+date.month()+1)).padStart(2, "0") + "-" + 
      String((+date.date())).padStart(2, "0") + "T" +
      String(+date.hour()).padStart(2, "0") + ":" +
      String(+date.minute()).padStart(2, "0");
    }

    let utcInputDate = useCurrentDatetime ? moment.utc() : moment.utc(dateTime);
    // console.log(utcInputDate.format(), utcInputDate.tz(this.whitelabel.getTimeZone()).format());
    return momentToDateTime(utcInputDate.tz(this.whitelabel.getTimeZone()));
  }

  reloadTwtdars()
  {
    this.loadTwTdAr();
  }

  agGridShowLoadingOverlay(agGridTableApi: any): void {
    if (agGridTableApi != null)
    {
      console.log('Loading table...');
      agGridTableApi.showLoadingOverlay();
    }
  }

  hideInactiveTwtdarIfNeeded(records: any[]) { 
    return records.filter(row =>{
      let isActive = (row.is_active == 1)
      let isActiveForAuth = (row.is_active_for_auth == 1)
      if (!this.showInactiveTwtdars && !isActive){
        return false
      }
      if (!this.showActiveTwtdars){
        if (isActive){
          return false
          // if (this.showActiveForAuthTwtdars && isActiveForAuth){
          //   // this is okay
          // }
          // else {
          //   return false
          // }
        }
      }
      return true;       
    });
  }

  hideSampleTwtdarIfNeeded(records: any[]) { 
    return records.filter(row =>{
      if (!this.showSampleTwtdars && (row.is_sample == 1)){
        return false
      }
      if (!this.showSecureTwtdars && (row.is_secured == 1)){
        return false
      }
      return true;       
    });
  }

  hideFieldTestTwtdarIfNeeded(records: any[]) { 
    return records.filter(row => this.showFieldTestTwtdars || row.is_field_test == 0);
  }

  get TwarHasWarning(){
    return +this.selectedTwTdAr.__raw.tqr_ovrd_td_id < +this.selectedTwTdAr.__raw.test_design_id
  }
}
