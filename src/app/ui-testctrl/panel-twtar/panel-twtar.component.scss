.ag-clip-height { 
    max-height: 15em;
}

.test-designs {
    display: flex;
    flex-direction: row;
    .selection-panel {
        width: 22em;
        &.is-wide {
            width: 48em;
        }
        padding:2em;
        max-height:70vh;
        overflow: auto;
    }
    .grid-container {
        flex-grow: 1;
    }
}
.test-attempts{
    margin-top: 2em;
}

.notification.is-warning {
    ul {
        margin-top: 0;
        margin-left: 1em;
    }
}
