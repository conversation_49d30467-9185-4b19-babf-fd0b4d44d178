<div class="test-designs">


    <div class="grid-container">
        <div style="margin:1em 0em">
            <div style="margin-bottom:1em;">


                <div style="display:flex; flex-direction:row; gap:1em; margin-bottom:1em;">
                    <button class="button is-small is-info" (click)="createTwTdAr()">
                        <tra slug="tc_tw_twtdar_creat_new"></tra>
                    </button>
                    <div *ngIf="!selectedTwTdAr">
                        <tra slug="tc_tw_twtdar_creat_new_or"></tra>
                    </div>
                </div>

                <div class="filter-toggle-set">
                    <div style="margin-bottom:2em">
                        <div>
                            <mat-slide-toggle [(ngModel)]="isEditOnSelect" > 
                                <strong><tra slug="tc_tw_twtdar_edit_mode"></tra> {{isEditOnSelect ? lang.tra('auth_on') : lang.tra('auth_off')}}</strong>
                            </mat-slide-toggle>
                        </div>
                        <div>
                            <mat-slide-toggle [(ngModel)]="isAdvancedEditing" > 
                                <strong>Advanced Editing Options {{isAdvancedEditing ? 'On' : 'Off'}}</strong>
                            </mat-slide-toggle>
                        </div>
                    </div>
                    <div>
                        <mat-slide-toggle 
                            [(ngModel)]="showActiveTwtdars" 
                            (change)="reloadTwtdars()"
                            color="primary"
                            [disabled]="false"
                        > 
                            
                            <span *ngIf="lang.c() === 'en'">Include Active (<code>is_active</code> = 1) Assessments?</span>
                            <span *ngIf="lang.c() === 'fr'">Inclure les évaluations actives (<code>is_active</code> =1)?</span>
                        </mat-slide-toggle>
                    </div>
                    <!-- <div>
                        <mat-slide-toggle 
                            [(ngModel)]="showActiveForAuthTwtdars" 
                            (change)="reloadTwtdars()"
                            color="primary"
                            [disabled]="false"
                        > 
                            
                            Include Inactive if Active for Authors (<code>is_active_for_auth</code> = 1) Assessments?
                        </mat-slide-toggle>
                    </div> -->
                    <div>
                        <mat-slide-toggle 
                            [(ngModel)]="showInactiveTwtdars" 
                            (change)="reloadTwtdars()"
                            color="primary"
                            [disabled]="false"
                        > 
                            <span *ngIf="lang.c() === 'en'">Include Inactive (<code>is_active</code> = 0) Assessments?</span>
                            <span *ngIf="lang.c() === 'fr'">Inclure les évaluations inactives (<code>is_active</code> = 0)?</span>
                        </mat-slide-toggle>
                    </div>
                    <div>
                        <mat-slide-toggle 
                        [(ngModel)]="showFieldTestTwtdars" 
                        (change)="reloadTwtdars()"
                        color="primary"
                        [disabled]="false"
                        > 
                        <span *ngIf="lang.c() === 'en'">Include Field Test (<code>is_field_test</code> = 1) Assessments?</span>
                        <span *ngIf="lang.c() === 'fr'">Inclure les évaluations d’essais (<code>is_field_test</code> =1)?</span>
                    </mat-slide-toggle>
                    </div>
                    <div>
                        <mat-slide-toggle 
                            [(ngModel)]="showSampleTwtdars" 
                            (change)="reloadTwtdars()"
                            color="primary"
                            [disabled]="false"
                        >  
                            <span *ngIf="lang.c() === 'en'">Include Practice (<code>is_sample</code> = 1) Assessments?</span>
                            <span *ngIf="lang.c() === 'fr'">Inclure les évaluations pratiques (<code>is_sample</code> =1)?</span>
                        </mat-slide-toggle>
                    </div>
                    <div>
                        <mat-slide-toggle 
                            [(ngModel)]="showSecureTwtdars" 
                            (change)="reloadTwtdars()"
                            color="primary"
                            [disabled]="false"
                        > 
                            <span *ngIf="lang.c() === 'en'">Include Secured (<code>is_secured</code> = 1) Assessments?</span>
                            <span *ngIf="lang.c() === 'fr'"> Inclure des évaluations sécurisées (<code>is_secured</code> =1)?</span>
                        </mat-slide-toggle>
                    </div>
                </div>
            </div>
            <!-- [class.ag-clip-height]="selectedTwTdAr" -->
            <ag-grid-angular
                class="ag-theme-alpine ag-grid-fullpage"
                [rowData]="twarRecords"
                [gridOptions]="twarGridOptions"
                (selectionChanged)="onTwarSelected($event)"
                [enableCellTextSelection]="true"
            ></ag-grid-angular>
        </div>
        <div style="display:flex; flex-direction:row; gap:1em; margin-bottom:1em;">
        <button class="button is-small" (click)="exportTwarCsv()">
            <tra slug="tc_tw_twtdar_export"></tra>
        </button>
        <button *ngIf="false" class="button is-small is-warning" (click)="invalidateTestAttempts()">
            <tra slug="tc_tw_twtdar_inval_attempts"></tra>
        </button>
        </div>
    </div>

    <div class="selection-panel is-wide" *ngIf="selectedTwTdAr">
        <div class="notification is-warning" *ngIf="TwarHasWarning">
            <ul>
                <li *ngIf="+this.selectedTwTdAr.__raw.tqr_ovrd_td_id < +this.selectedTwTdAr.__raw.test_design_id">
                    Ensure the TQR override is more recent than the allocated test design. 
                </li>
            </ul>
        </div>
        <div>
            <div class="space-between">
                <div>
                    <ng-container *ngIf="!isEditingSelectedTwtdar()">
                        <button (click)="editStart()" class="button is-small">Edit</button>                    
                    </ng-container>
                    <ng-container *ngIf="isEditingSelectedTwtdar()">
                        <button (click)="editCancel()" [disabled]="selectedTwTdAr.__isSaving" class="button is-small">Cancel</button>
                        <button (click)="editSave()"   [disabled]="selectedTwTdAr.__isSaving || isReqFieldMissing()" class="button is-small">Save</button>
                    </ng-container>
                </div>
                <button 
                    class="button is-small  has-icon" 
                    ngxClipboard 
                    [cbContent]="renderJson()"
                    (cbOnSuccess)="onCopyJson()"
                  >
                    <span class="icon"> <i class="fa fa-copy"></i> </span>
                    <span>Copy Assessment Config (JSON)</span>
                </button>
            </div>
            <div *ngIf="selectedTwTdAr.is_active==0 && selectedTwTdAr.__raw.is_active==1" class="notification is-warning" style="margin:0.5em">
                Don't forget to save to reflect the activation.
            </div>
            <div *ngIf="selectedTwTdAr.__raw.is_active==0" style="padding:0.5em">
                <div class="notification is-danger is-small">
                    This assessment design allocation is currently inactive.
                    <button 
                        class="button" 
                        [disabled]="!selectedTwTdAr.__isEditing"
                        (click)="activeSelectedTwtar()"
                    >
                        Activate for All Teacher Dropdowns
                    </button>
                    <div style="margin-top:1em;">
                        While this form is in-active, it is possible to make it available to sample schools for QA purposes.
                        <div>
                            <check-toggle 
                                [isChecked]="isRawPropNumericChecked('is_active_for_qa')"
                                (toggle)="toggleRawPropCheckedNumeric('is_active_for_qa')"
                                [disabled]="!selectedTwTdAr.__isEditing"
                            ></check-toggle>
                            Active for QA?
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="false">
                <h4>Checklist:</h4>
                <div>
                    Expecting all assessments in principal kit windows to be secured. Not sample or field test.
                </div>
                <div>
                    Expecting dates to be set for all secure assessment
                </div>
                <div>
                    Need a type slug
                </div>
                <div>
                    School allowed strict only expected if it is being applid at the test window level
                </div>
            </div>
            <table>
                <tr>
                    <td>Assessment Allocation ID</td>
                    <td>
                        <div class="space-between">
                            <code>{{selectedTwTdAr.id}}</code>
                            <div *ngIf="false">
                                <button (click)="selectTwtarPrev()" class="button is-small is-light">
                                    Prev. Design
                                </button>
                                <button (click)="selectTwtarNext()" class="button is-small is-light">
                                    Next Design
                                </button>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Assessment Start Date & Time</td>
                    <!-- <td><input [(ngModel)]="selectedTwTdAr.__raw.test_date_start" [disabled]="!selectedTwTdAr.__isEditing"></td> -->
                    <td>
                        <span *ngIf="selectedTwTdAr.__raw.is_scheduled && !selectedTwTdAr.__raw.is_date_restricted">
                            <span class="tag is-danger">Need Scheduled Date</span>
                            <span>You do not yet have a date set for this scheduled. Please scroll up to set one.</span>
                        </span>

                        <span *ngIf="!selectedTwTdAr.__isEditing">{{renderDateTime(selectedTwTdAr.__raw.test_date_start)}}</span>
                        <ng-container *ngIf="selectedTwTdAr.__isEditing">
                            <ng-container *ngIf="!selectedTwTdAr.__raw.is_date_restricted">
                                <button (click)="initTwtarStartDate()" class="button">Activate Date Restriction</button>
                            </ng-container>
                            <ng-container *ngIf="selectedTwTdAr.__raw.is_date_restricted">
                                <div style="display: flex; flex-direction: row;">
                                    <dob-input 
                                        [formControlModel]="datetimeColumns.test_date_start.dateControl" 
                                        [isDateHidden]="true"
                                        [yearsFutureExtension]="3"
                                        style="width: 17em;"
                                    ></dob-input>&nbsp;
                                    <filterable-dropdown 
                                        label="Time"
                                        [(ngModel)]="datetimeColumns.test_date_start.timeControl"
                                        [onLoadValue]="datetimeColumns.test_date_start.timeControl" 
                                        [options]="timeOptions"
                                        inputWidth="8em"
                                    ></filterable-dropdown>
                                </div>
                                <div>
                                    <button class="is-small is-warning" (click)="removeTwtarStartDate()">
                                        Remove Date Control
                                    </button>
                                </div>
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>
                <tr>
                    <td>Assessment End Date & Time</td>
                    <td>
                        <span *ngIf="!selectedTwTdAr.__isEditing">{{renderDateTime(selectedTwTdAr.__raw.test_date_end)}}</span>
                        <ng-container *ngIf="selectedTwTdAr.__isEditing">
                            <ng-container *ngIf="!selectedTwTdAr.__raw.is_schedule_range">
                                <button (click)="selectedTwTdAr.__raw.is_schedule_range = 1" class="button">Activate End Time</button>
                            </ng-container>
                            <ng-container *ngIf="selectedTwTdAr.__raw.is_schedule_range">
                                <div style="display: flex; flex-direction: row;">
                                    <dob-input 
                                        [formControlModel]="datetimeColumns.test_date_end.dateControl" 
                                        [isDateHidden]="true"
                                        [yearsFutureExtension]="3"
                                        style="width: 17em;"
                                    ></dob-input>&nbsp;
                                    <filterable-dropdown 
                                        label="Time"
                                        [(ngModel)]="datetimeColumns.test_date_end.timeControl"
                                        [onLoadValue]="datetimeColumns.test_date_end.timeControl" 
                                        [options]="timeOptions"
                                        inputWidth="8em"
                                    ></filterable-dropdown>
                                </div>
                                <div>
                                    <button class="is-small is-warning" (click)="selectedTwTdAr.__raw.is_schedule_range = 0">
                                        Remove End Date Control
                                    </button>
                                </div>
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>
                <tr>
                    <td>Perusal Start Configuration</td>
                    <td>
                        <span class="tag is-warning" *ngIf="!selectedTwTdAr.__raw.is_perusal_allow">Perusal Disabled for Assesment</span>
                        <ng-container *ngIf="selectedTwTdAr.__raw.is_perusal_allow && !selectedTwTdAr.__raw.perusal_type">
                            {{ renderPerusalWarning }}
                        </ng-container>

                        <ng-container *ngIf="!selectedTwTdAr.__isEditing && (selectedTwTdAr.__raw.is_perusal_allow)">
                          {{renderPerusalCaption(selectedTwTdAr.__raw.perusal_type)}}
                        </ng-container>

                        <ng-container *ngIf="selectedTwTdAr.__isEditing && (selectedTwTdAr.__raw.is_perusal_allow)">
                          <div class="select">
                            <select [(ngModel)]="selectedTwTdAr.__raw.perusal_type">
                              <option value="">---(None)---</option>
                              <option *ngFor="let option of perusalTypeOptions" [value]="option.value">
                                {{option.caption}}
                              </option>
                            </select>
                          </div>
                        </ng-container>
                    </td>
                </tr>
                <tr *ngIf="(selectedTwTdAr.__raw.is_perusal_allow) && selectedTwTdAr.__raw.perusal_type == PERUSAL_TYPES.STATIC_TIME">
                  <td>Perusal Start Time</td>
                  <td>
                    <ng-container *ngIf="selectedTwTdAr.__isEditing && datetimeColumns.perusal_date_start.dateControl.value">
                        <div style="display: flex; flex-direction: row;">
                            <dob-input 
                                [formControlModel]="datetimeColumns.perusal_date_start.dateControl" 
                                [isDateHidden]="true"
                                [yearsFutureExtension]="3"
                                style="width: 17em;"
                            ></dob-input>&nbsp;
                            <filterable-dropdown 
                                label="Time"
                                [(ngModel)]="datetimeColumns.perusal_date_start.timeControl"
                                [onLoadValue]="datetimeColumns.perusal_date_start.timeControl" 
                                [options]="timeOptions"
                                inputWidth="8em"
                            ></filterable-dropdown>
                        </div>
                        <div>
                            <button class="is-small is-warning" (click)="resetPerusalTime()">
                                Remove Perusal Time
                            </button>
                        </div>
                    </ng-container>
                    <div *ngIf="selectedTwTdAr.__isEditing && !datetimeColumns.perusal_date_start.dateControl.value">
                        <button (click)="initPerusalTime()" class="button">Set to default</button>
                    </div>
                    <ng-container *ngIf="!selectedTwTdAr.__isEditing && selectedTwTdAr.__raw.perusal_date_start">
                      <span>{{renderDateTime(selectedTwTdAr.__raw.perusal_date_start)}}</span>
                    </ng-container>
                  </td>
                </tr>
                
                <tr *ngIf="selectedTwTdAr.__raw.is_perusal_allow && (selectedTwTdAr.__raw.perusal_type == PERUSAL_TYPES.RELATIVE_SESSION_START || selectedTwTdAr.__raw.perusal_type == PERUSAL_TYPES.RELATIVE_FIRST_STUDENT_START)">
                  <td>Offset hours after relative date when perusal should start</td>
                  <td>
                    <ng-container *ngIf="selectedTwTdAr.__isEditing">
                      <input [(ngModel)]="selectedTwTdAr.__raw.perusal_offset_hours" class="input is-small" type="number" min="0" step="0.05" placeholder="Enter hours" />
                    </ng-container>
                    <ng-container *ngIf="!selectedTwTdAr.__isEditing">
                      {{selectedTwTdAr.__raw.perusal_offset_hours}}
                    </ng-container>
                  </td>
                </tr>

                <tr>
                  <td>Perusal End Configuration</td>
                  <td>
                      <span class="tag is-warning" *ngIf="!selectedTwTdAr.__raw.is_perusal_allow">Perusal Disabled for Assesment</span>
                      <!-- <ng-container *ngIf="selectedTwTdAr.__raw.is_perusal_allow && !selectedTwTdAr.__raw.perusal_type">
                          {{ renderPerusalWarning }}
                      </ng-container> -->

                      <ng-container *ngIf="!selectedTwTdAr.__isEditing">
                        {{renderPerusalEndCaption(selectedTwTdAr.__raw.perusal_end_type)}}
                      </ng-container>

                      <ng-container *ngIf="selectedTwTdAr.__isEditing">
                        <div class="select">
                          <select [(ngModel)]="selectedTwTdAr.__raw.perusal_end_type">
                            <option value="">---(None)---</option>
                            <option *ngFor="let option of perusalEndOptions" [value]="option.value">
                              {{option.caption}}
                            </option>
                          </select>
                        </div>
                      </ng-container>
                  </td>
                </tr>
                <tr *ngIf="selectedTwTdAr.__raw.is_perusal_allow && selectedTwTdAr.__raw.perusal_end_type == PERUSAL_END_TYPES.DURATION_HOURS">
                  <td>Perusal duration hours</td>
                  <td>
                    <ng-container *ngIf="selectedTwTdAr.__isEditing">
                      <input [(ngModel)]="selectedTwTdAr.__raw.perusal_duration_hours" class="input is-small" type="number" min="1" step="0.05" placeholder="Enter hours" />
                    </ng-container>
                    <ng-container *ngIf="!selectedTwTdAr.__isEditing">
                      {{selectedTwTdAr.__raw.perusal_duration_hours}}
                    </ng-container>
                  </td>
                </tr>
                <tr *ngIf="(selectedTwTdAr.__raw.is_perusal_allow) && selectedTwTdAr.__raw.perusal_end_type == PERUSAL_END_TYPES.STATIC_TIME">
                  <td>Perusal End Time</td>
                  <td>
                    <ng-container *ngIf="selectedTwTdAr.__isEditing && datetimeColumns.perusal_date_end.dateControl.value">
                        <div style="display: flex; flex-direction: row;">
                            <dob-input 
                                [formControlModel]="datetimeColumns.perusal_date_end.dateControl" 
                                [isDateHidden]="true"
                                [yearsFutureExtension]="3"
                                style="width: 17em;"
                            ></dob-input>&nbsp;
                            <filterable-dropdown 
                                label="Time"
                                [(ngModel)]="datetimeColumns.perusal_date_end.timeControl"
                                [onLoadValue]="datetimeColumns.perusal_date_end.timeControl" 
                                [options]="timeOptions"
                                inputWidth="8em"
                            ></filterable-dropdown>
                        </div>
                        <div>
                            <button class="is-small is-warning" (click)="resetPerusalTime(true)">
                                Remove Perusal Time
                            </button>
                        </div>
                    </ng-container>
                    <div *ngIf="selectedTwTdAr.__isEditing && !datetimeColumns.perusal_date_end.dateControl.value">
                        <button (click)="initPerusalTime(true)" class="button">Set to default</button>
                    </div>
                    <ng-container *ngIf="!selectedTwTdAr.__isEditing && selectedTwTdAr.__raw.perusal_date_end">
                      <span>{{renderDateTime(selectedTwTdAr.__raw.perusal_date_end)}}</span>
                    </ng-container>
                  </td>
                </tr>

                <ng-container *ngIf="selectedTwTdAr.__raw.question_requiring_scan > 0">
                    <tr>
                        <td>Days before Printing is enabled for scheduled session</td>
                        <td><input [(ngModel)]="selectedTwTdAr.__raw.print_before_days" [disabled]="!selectedTwTdAr.__isEditing"></td>
                    </tr>
                    <tr>
                        <td>Days after Upload is disabled for scheduled session</td>
                        <td><input [(ngModel)]="selectedTwTdAr.__raw.upload_until_days" [disabled]="!selectedTwTdAr.__isEditing"></td>
                    </tr>
                    <tr>
                        <td>Exclude weekends from Print/Upload days</td>
                        <td>
                            <check-toggle 
                            [isChecked]="selectedTwTdAr.__raw.is_exclude_weekends !== undefined ? isRawPropNumericChecked('is_exclude_weekends') : true"
                            (toggle)="toggleRawPropCheckedNumeric('is_exclude_weekends')"
                            [disabled]="!selectedTwTdAr.__isEditing"
                            ></check-toggle>
                        </td>
                    </tr>
                    <tr>
                        <td>Exception Dates to consider in the print/upload days.(dates given here will not be counted as a business day)</td>
                        <td>
                            <input type="date" [(ngModel)]="newExceptionDate" [disabled]="!selectedTwTdAr.__isEditing">
                            <button (click)="addExceptionDate()" [disabled]="!selectedTwTdAr.__isEditing">Add Date</button>
                            <ul>
                            <li *ngFor="let date of selectedTwTdAr.__raw.exception_dates; let i = index">
                                {{ date | date: 'yyyy-MM-dd' }}
                                <button (click)="removeExceptionDate(i)" [disabled]="!selectedTwTdAr.__isEditing">Remove</button>
                            </li>
                            </ul>
                        </td>
                    </tr>
                </ng-container>
                <tr>
                    <td>Name</td>
                    <td><input [(ngModel)]="selectedTwTdAr.__raw.long_name" [disabled]="!selectedTwTdAr.__isEditing"></td>
                </tr>
                <tr>
                    <td>Assessment (type_slug)</td>
                    <td>
                        <input  [(ngModel)]="selectedTwTdAr.__raw.type_slug" (change)="applySlugCopy()" [disabled]="!selectedTwTdAr.__isEditing"></td>
                        <!-- <div class="select is-small">
                            <select [(ngModel)]="selectedTwTdAr.__raw.type_slug" (change)="applySlugCopy()">
                                <option></option>
                            </select>
                        </div> -->
                </tr>
                <tr *ngIf="!isSlugCopyMode() && isAdvancedEditing">
                    <td>Assessment Component (slug)</td>
                    <td><input [(ngModel)]="selectedTwTdAr.__raw.slug" [disabled]="!selectedTwTdAr.__isEditing"></td>
                </tr>
                <tr>
                    <td>Form Code</td>
                    <td><input [(ngModel)]="selectedTwTdAr.__raw.form_code" [disabled]="!selectedTwTdAr.__isEditing"></td>
                </tr>
                <tr>
                    <td>Assessment Duration</td>
                    <td><input [(ngModel)]="selectedTwTdAr.__raw.test_duration" [disabled]="!selectedTwTdAr.__isEditing"></td>
                </tr>
                <tr>
                    <td>Published Test Design ID</td>
                    <td>
                        <input [(ngModel)]="selectedTwTdAr.__raw.test_design_id" [disabled]="isTestDesignLocked()">
                        <button *ngIf="selectedTwTdAr.__isEditing" (click)="unlockTestDesignEdit()">OVERRIDE</button>
                        <ng-container *ngIf="selectedTwTdAr.test_design_id && selectedTwTdAr.source_item_set_id">
                            <ng-container *ngIf="+selectedTwTdAr.__raw.tqr_ovrd_td_id < +selectedTwTdAr.__raw.test_design_id">
                                <p  style="color: red">Override ID is Older than Publish ID Please Update</p>
                            </ng-container>
                            <div *ngIf="selectedTwTdAr.td_assigned_by">Assigned by {{ selectedTwTdAr.td_assigned_by}} on {{ selectedTwTdAr.td_assigned_on}}</div>
                            <!-- <input [(ngModel)]="selectedTwTdAr.__raw.long_name" [disabled]="!selectedTwTdAr.__isEditing"> -->
                            <div [ngSwitch]="selectedTwTdAr.qs_is_test_design==1">
                                <a *ngSwitchCase="false" class="button is-fullwidth is-info is-inverted" target="_blank" href="/#/en/test-auth/item-set-editor/{{ selectedTwTdAr.source_item_set_id}}">Open Item Bank</a>
                                <a *ngSwitchCase="true"  class="button is-fullwidth is-info is-inverted" target="_blank" href="/#/en/test-auth/framework/1/{{ selectedTwTdAr.source_item_set_id}}">Open Test Design</a>
                            </div>
                        </ng-container>
                    </td>
                </tr>
                
                <tr *ngIf="isAdvancedEditing">
                    <td>Item Register Override Publish ID</td>
                    <td><input [style.color]="+selectedTwTdAr.__raw.tqr_ovrd_td_id < +selectedTwTdAr.__raw.test_design_id ? 'red' : 'initial'" [(ngModel)]="selectedTwTdAr.__raw.tqr_ovrd_td_id" [disabled]="!selectedTwTdAr.__isEditing"></td>
                </tr>
                <tr *ngIf="isAdvancedEditing">
                    <td>Session Selection Order</td>
                    <td><input [(ngModel)]="selectedTwTdAr.__raw.selection_order" [disabled]="!selectedTwTdAr.__isEditing"></td>
                </tr>
                <tr *ngIf="isAdvancedEditing">
                    <td>is_field_test?</td>
                    <td>
                        <check-toggle 
                            [isChecked]="isRawPropNumericChecked('is_field_test')"
                            (toggle)="toggleRawPropCheckedNumeric('is_field_test')"
                            [disabled]="!selectedTwTdAr.__isEditing"
                        ></check-toggle>
                    </td>
                </tr>
                <tr *ngIf="isAdvancedEditing">
                    <td>is_date_restricted?</td>
                    <td>
                        <check-toggle 
                            [isChecked]="isRawPropNumericChecked('is_date_restricted')"
                            (toggle)="toggleRawPropCheckedNumeric('is_date_restricted')"
                            [disabled]="!selectedTwTdAr.__isEditing"
                        ></check-toggle>
                    </td>
                </tr>
                <tr *ngIf="isAdvancedEditing">
                    <td>is_school_allowed_strict?</td>
                    <td>
                        <check-toggle 
                            [isChecked]="isRawPropNumericChecked('is_school_allowed_strict')"
                            (toggle)="toggleRawPropCheckedNumeric('is_school_allowed_strict')"
                            [disabled]="!selectedTwTdAr.__isEditing"
                        ></check-toggle>
                    </td>
                </tr>
                <tr *ngIf="isAdvancedEditing">
                    <td>Order</td>
                    <td><input [(ngModel)]="selectedTwTdAr.__raw.order" [disabled]="!selectedTwTdAr.__isEditing"></td>
                </tr>
                <tr>
                    <td>Language</td>
                    <td>
                        <input [(ngModel)]="selectedTwTdAr.__raw.lang" [disabled]="!selectedTwTdAr.__isEditing">
                        <div>Source Lang: <code>{{ selectedTwTdAr.qs_languages}}</code></div>
                    </td>
                </tr>
                <tr>
                    <td>Constrain by School Board Language</td>
                    <td>
                        <div>
                            <check-toggle 
                                [isChecked]="isRawPropNumericChecked('req_sd_lang')"
                                (toggle)="toggleRawPropCheckedNumeric('req_sd_lang')"
                                [disabled]="!selectedTwTdAr.__isEditing"
                            ></check-toggle>
                            &nbsp; only {{selectedTwTdAr.__raw.lang}}
                        </div>
                        <div>
                            <check-toggle 
                                [isChecked]="isRawPropNumericChecked('req_sd_lang_not')"
                                (toggle)="toggleRawPropCheckedNumeric('req_sd_lang_not')"
                                [disabled]="!selectedTwTdAr.__isEditing"
                            ></check-toggle>
                            &nbsp; anything but {{selectedTwTdAr.__raw.lang}} 
                        </div>
                        <div *ngIf="selectedTwTdAr.__raw.req_sd_lang_not && selectedTwTdAr.__raw.req_sd_lang" class="notification is-danger">
                            You have a contradiction in your selection. Please fix.
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Secured?</td>
                    <td>
                        <check-toggle 
                            [isChecked]="isRawPropNumericChecked('is_secured')"
                            (toggle)="toggleRawPropCheckedNumeric('is_secured')"
                            [disabled]="!selectedTwTdAr.__isEditing"
                        ></check-toggle>
                    </td>
                </tr>
                <tr>
                    <td>Scheduled?</td>
                    <td>
                        <check-toggle 
                            [isChecked]="isRawPropNumericChecked('is_scheduled')"
                            (toggle)="toggleRawPropCheckedNumeric('is_scheduled')"
                            [disabled]="!selectedTwTdAr.__isEditing"
                        ></check-toggle>
                        <span>
                            <span *ngIf="!selectedTwTdAr.__raw.is_scheduled && selectedTwTdAr.__raw.is_secured">
                                <span class="tag is-warning">Secured should be Scheduled</span>
                                <span>Secure assessments should be scheduled to avoid starting immediately</span>
                            </span>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td>Marked? (by humans)</td>
                    <td>
                        <check-toggle 
                            [isChecked]="isRawPropNumericChecked('is_marking_req')"
                            (toggle)="toggleRawPropCheckedNumeric('is_marking_req')"
                            [disabled]="!selectedTwTdAr.__isEditing"
                        ></check-toggle>
                    </td>
                </tr>
                <tr>
                    <td>Sample?</td>
                    <td>
                        <check-toggle 
                            [isChecked]="isRawPropNumericChecked('is_sample')"
                            (toggle)="toggleRawPropCheckedNumeric('is_sample')"
                            [disabled]="!selectedTwTdAr.__isEditing"
                        ></check-toggle>
                    </td>
                </tr>
                <tr>
                    <td>Includes Print Version?</td>
                    <td>
                        <check-toggle 
                            [isChecked]="isRawPropNumericChecked('is_print')"
                            (toggle)="toggleRawPropCheckedNumeric('is_print')"
                            [disabled]="!selectedTwTdAr.__isEditing"
                        ></check-toggle>
                    </td>
                </tr>
                <tr *ngIf="isAdvancedEditing">
                    <td>Questionnaire?</td>
                    <td>
                        <check-toggle 
                            [isChecked]="isRawPropNumericChecked('is_questionnaire')"
                            (toggle)="toggleRawPropCheckedNumeric('is_questionnaire')"
                            [disabled]="!selectedTwTdAr.__isEditing"
                        ></check-toggle>
                    </td>
                </tr>
                <tr *ngIf="isAdvancedEditing">
                    <td>Class Form Lock?</td>
                    <td>
                        <check-toggle 
                            [isChecked]="isRawPropNumericChecked('is_classroom_common_form')"
                            (toggle)="toggleRawPropCheckedNumeric('is_classroom_common_form')"
                            [disabled]="!selectedTwTdAr.__isEditing"
                        ></check-toggle>
                    </td>
                </tr>
                <tr *wlCtx="'IS_TWTAR_CRED'">
                    <td>Can Credential?</td>
                    <td>
                        <check-toggle 
                            [isChecked]="isRawPropNumericChecked('can_credential')"
                            (toggle)="toggleRawPropCheckedNumeric('can_credential')"
                            [disabled]="!selectedTwTdAr.__isEditing"
                        ></check-toggle>
                    </td>
                </tr>
                <tr *wlCtx="'IS_TWTAR_PIPELINE_EXCLUSION'">
                    <td>Is Excluded from Psych Pipeline?</td>
                    <td>
                        <check-toggle 
                            [isChecked]="isRawPropNumericChecked('is_pipeline_exclude')"
                            (toggle)="toggleRawPropCheckedNumeric('is_pipeline_exclude')"
                            [disabled]="!selectedTwTdAr.__isEditing"
                        ></check-toggle>
                    </td>
                </tr>

                <ng-container *wlCtx="'IS_TWTAR_USER_METAS'">
                    <tr>
                        <td>Subsession Meta</td>
                        <td><input [(ngModel)]="selectedTwTdAr.__raw.subsession_meta" [disabled]="!selectedTwTdAr.__isEditing"></td>
                    </tr>
                    <tr>
                        <td>User Metas Filter</td>
                        <td><input [(ngModel)]="selectedTwTdAr.__raw.user_metas_filter" [disabled]="!selectedTwTdAr.__isEditing"></td>
                    </tr>
                    <tr>
                        <td>Accommodations User Meta Constraint?</td>
                        <td>
                            <check-toggle 
                                [isChecked]="isRawPropNumericChecked('accomm_user_meta_constraint')"
                                (toggle)="toggleRawPropCheckedNumeric('accomm_user_meta_constraint')"
                                [disabled]="!selectedTwTdAr.__isEditing"
                            ></check-toggle>
                        </td>
                    </tr>
                </ng-container>
                <tr *ngIf="isAdvancedEditing">
                    <td>max_condition_on_option</td>
                    <td><input [(ngModel)]="selectedTwTdAr.__raw.max_condition_on_option" [disabled]="!selectedTwTdAr.__isEditing"></td>
                </tr>
                <tr *ngIf="isAdvancedEditing">
                    <td>Component Code</td>
                    <td><input [(ngModel)]="selectedTwTdAr.__raw.component_code" [disabled]="true"></td>
                </tr>
                <tr>
                    <td>Exclude Session Duration</td>
                    <td><input [(ngModel)]="selectedTwTdAr.__raw.is_session_duration_excluded" [disabled]="!selectedTwTdAr.__isEditing"></td>
                </tr>
                <tr>
                    <td>Exclude Session Name</td>
                    <td><input [(ngModel)]="selectedTwTdAr.__raw.is_session_name_excluded" [disabled]="!selectedTwTdAr.__isEditing"></td>
                </tr>

            </table>
            <div>
                <ng-container *ngIf="isEditingSelectedTwtdar()">
                    <button (click)="editCancel()" [disabled]="selectedTwTdAr.__isSaving" class="button is-small">Cancel</button>
                    <button (click)="editSave()"   [disabled]="selectedTwTdAr.__isSaving || isReqFieldMissing()" class="button is-small">Save</button>
                </ng-container>
            </div>
            <div style="margin-top:2em;">
                <em>If using the buttons below, please make sure to save in between</em>
                <div class="buttons"> 
                    <button 
                        (click)="setRawPropCheckedNumeric('is_active_for_auth', 0 )" 
                        class="button" 
                        [class.is-danger]="isRawPropNumericChecked('is_active')"
                        [disabled]="!selectedTwTdAr.__isEditing || (isRawPropNumericChecked('is_active') || !isRawPropNumericChecked('is_active_for_auth'))"
                    >
                        Hide for Authors
                    </button>
                    <button 
                        (click)="setRawPropCheckedNumeric('is_active_for_auth', 1)" 
                        class="button" 
                        [class.is-success]="!isRawPropNumericChecked('is_active_for_auth')"
                        [disabled]="!selectedTwTdAr.__isEditing || (isRawPropNumericChecked('is_active_for_auth'))"
                    >
                        Show for Authors
                    </button>
                </div>
            </div>
            <div style="margin-top:2em;">
                <button 
                    (click)="removeTwTdAr()" 
                    class="button" 
                    [class.is-danger]="isRawPropNumericChecked('is_active')"
                    [disabled]="!isRawPropNumericChecked('is_active')"
                >
                    De-activate Design
                </button>
            </div>

        </div>
    </div>
</div>
<div *ngIf="selectedTwTdAr" style="margin-top: 2em;">
    <div class="test-designs">
        <div class="grid-container">
            <ag-grid-angular
                class="ag-theme-alpine ag-grid-fullpage"
                [rowData]="tdRecords"
                [gridOptions]="tdGridOptions"
                [enableCellTextSelection]="true"
                (selectionChanged)="onTdSelected($event)"
            ></ag-grid-angular>
        </div>
        <div class="selection-panel">
            <ng-container *ngIf="!selectedTd">
                <em>Select a published test design from the adjacent list.</em>
            </ng-container>
            <ng-container *ngIf="selectedTd">
                <div style="margin-bottom:1em;">
                    <div *ngIf="selectedTd.isAllocated">
                        This published test design is currently available for students.
                    </div>
                    <button 
                        *ngIf="!selectedTd.isAllocated"
                        class="button"
                        [disabled]="isAssigning"
                        (click)="assignTestDesign(selectedTwTdAr, selectedTd.id)"
                    >Allocate to Admin. Window</button>
                </div>
                <table>
                    <ng-container *ngFor="let col of tdGridOptions.columnDefs">
                        <tr *ngIf="col.field != 'isAllocated' ">
                            <td><strong>{{col.headerName}}</strong></td>
                            <td>{{selectedTd[col.field]}}</td>
                        </tr>
                        
                    </ng-container>
                </table>
            </ng-container>
        </div>
    </div>
</div>
<div *ngIf="invalidatedTestAttempts" style="margin-top: 2em;">
    <h2>Invalidated Test Attempts for Superceded Forms</h2>
    
    <div class="test-attempts">
        <ag-grid-angular
                class="ag-theme-alpine ag-grid-fullpage"
                [rowData]="invalidatedtaRecords"
                [gridOptions]="invalidatedtaGridOptions"
                [enableCellTextSelection]="true"
                (selectionChanged)="onTdSelected($event)"
            ></ag-grid-angular>
    </div>
</div>