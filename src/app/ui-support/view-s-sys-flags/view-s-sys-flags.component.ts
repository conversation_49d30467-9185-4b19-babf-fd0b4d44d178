import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../api/auth.service';
import { RoutesService } from '../../api/routes.service';
import { AccountType } from '../../constants/account-types';
import { LoginGuardService } from '../../api/login-guard.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { Router } from '@angular/router';
import { PageModalService, PageModalController} from 'src/app/ui-partial/page-modal.service';

export enum SysFlagModal {
  PREVIEW_MARKDOWN = "PREVIEW_MARKDOWN",
}

interface ISysFlag {
  id: number;
  key: string;
  value?: number | string;
  value_en?: string,
  value_fr?: string,
  is_binary?: number;
  description?: string;
  max?: number;
  min?: number;
  isSending?: boolean;
  isSendingEn?: boolean;
  isSendingFr?: boolean;
}

enum SysFlagType {
  NUMERIC = 'numeric',
  STRING = 'string',
  MD_TRANSLATIONS = 'md_translations'
}
@Component({
  selector: 'view-s-sys-flags',
  templateUrl: './view-s-sys-flags.component.html',
  styleUrls: ['./view-s-sys-flags.component.scss']
})
export class ViewSSysFlagsComponent implements OnInit {

  constructor(
    private auth: AuthService,
    private routes: RoutesService,
    private loginGuard: LoginGuardService, 
    private breadcrumbsService: BreadcrumbsService,
    private lang: LangService,
    private router: Router,
    private pageModalService: PageModalService,
  ) { }

  isLoaded: boolean;
  isLoadFailed: boolean;
  breadcrumb = [];
  systemFlagsNumeric: ISysFlag[] = [];
  systemFlagsString: ISysFlag[] = [];
  SysFlagType = SysFlagType;
  pageModal: PageModalController;
  SysFlagModal = SysFlagModal;

  ngOnInit() {
    this.loginGuard.activate([AccountType.SUPPORT]);
    this.breadcrumb = [
      this.breadcrumbsService.SUPPORT_DASHBOARD(),
      this.breadcrumbsService._CURRENT(this.lang.tra('System Flags'), this.router.url),
    ]
    this.loadReq();
    this.pageModal = this.pageModalService.defineNewPageModal();
  }

  isSending:boolean;
  isSent:boolean;
  isFailed:boolean;
  failReason:string;

  loadReq(){
    this.auth
      .apiFind(
        this.routes.SUPPORT_SYS_FLAGS,
        {}
      )
      .then(res => {
        const {systemFlagsNumeric, systemFlagsString, systemFlagsMdTranslations} = res;
        this.systemFlagsNumeric = systemFlagsNumeric;
        this.systemFlagsString = systemFlagsString;
        this.isLoaded = true;
      })
  }

  updateFlag(flag: ISysFlag, value:number|string, type = SysFlagType.NUMERIC, lang?: string){

    // Enforce max and min
    if (type == SysFlagType.NUMERIC){
      if (flag.max && value as number > flag.max){
        return this.loginGuard.quickPopup(`Error: value cannot be above ${flag.max}.`)
      } else if (flag.min && value as number < flag.min) {
        return this.loginGuard.quickPopup(`Error: value cannot be below ${flag.min}.`)
      }
    }

    //Handle translation type change
    let value_en, value_fr;
    if (type == SysFlagType.MD_TRANSLATIONS){
      if (lang == "en"){
        value_en = value;
        flag.isSendingEn = true;
      } else if (lang == "fr") {
        value_fr = value;
        flag.isSendingFr = true;
      }
      value = undefined;
    }

    this.isSent = false;
    this.isSending = true;
    flag.isSending = true;
    this.auth
      .apiPatch(
        this.routes.SUPPORT_SYS_FLAGS,
        flag.id,
        { 
          type,
          value, 
          value_en,
          value_fr,
          key: flag.key, 
        }
      )
      .then(res => {
        flag.value = value;
        this.isSent = true;
      })
      .catch(e =>{
        alert('failed to update')
      })
      .finally(()=>{
        this.isSending = false;
        flag.isSending = false;
        flag.isSendingEn = false;
        flag.isSendingFr = false;
      })
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }
  cmc() { return this.cModal().config; }
  launchMarkdownPreviewModal(flag: ISysFlag){
    this.pageModal.newModal({
      type: SysFlagModal.PREVIEW_MARKDOWN,
      config: {flag},
      finish: () => {}}
    );
  }

}
