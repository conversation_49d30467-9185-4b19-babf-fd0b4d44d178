<div class="page-body">
  <div>
    <header
      [breadcrumbPath]="breadcrumb"
    ></header>
    <div class="page-content is-fullpage">
      <div *ngIf="!isLoaded && !isLoadFailed">
        Loading
      </div>
      <div *ngIf="isLoadFailed">
        You do not have the required roles to access this page.
      </div>
      <div *ngIf="isLoaded" style="padding:3em;">
        <p>
          This page is simple but controls a couple of important things. Most of these system flags are used for initial feature roll outs only, so you shouldn't be using this page too much. The page is made available specifically for the SHOW_APPLICANT_RESULTS toggle.
        </p>
        <hr/>
        <div class="columns">
          <div class="column">
            <h2>Numeric constants</h2>
            <table style="width:100%;">
              <tr *ngFor="let flag of systemFlagsNumeric">
                <td>{{flag.id}}</td>
                <td>
                  <p class="flag-key"><code>{{flag.key}}</code></p>
                  <p>
                    <span *ngIf="flag.description" class="is-size-7 has-text-grey">{{flag.description}} </span>
                    <span *ngIf="flag.min" class="is-size-7 has-text-grey">The minimum is <b>{{flag.min}}</b>. </span>
                    <span *ngIf="flag.max" class="is-size-7 has-text-grey">The maximum is <b>{{flag.max}}</b>. </span>
                  </p>
                </td>
                <td>

                  <div *ngIf="flag.is_binary">
                    <button *ngIf="flag.value == 1" (click)="updateFlag(flag, 0)" class="button is-info" [class.is-loading]="flag.isSending">On</button>
                    <button *ngIf="flag.value == 0" (click)="updateFlag(flag, 1)" class="button" [class.is-loading]="flag.isSending">Off</button>
                  </div>

                  <div class="field has-addons" *ngIf="!flag.is_binary">
                    <div class="control">
                      <input 
                        type="number" 
                        [(ngModel)]="flag.value" 
                        class="input"
                        [min]="flag.min"
                        [max]="flag.max"
                      />
                    </div>
                    <div class="control">
                      <button (click)="updateFlag(flag, flag.value)" class="button is-info" [class.is-loading]="flag.isSending">
                        <i class="fas fa-check"></i>
                      </button>
                    </div>
                  </div>

                </td>
              </tr>
            </table>
          </div>
          <div class="column">
            <h2>String constants</h2>
            <table style="width:100%;">
              <tr *ngFor="let flag of systemFlagsString">
                <td>{{flag.id}}</td>
                <td>
                  <p class="flag-key"><code>{{flag.key}}</code></p>
                  <p><span *ngIf="flag.description" class="is-size-7 has-text-grey">{{flag.description}}</span></p>
                </td>
                <td>
                  <div class="field has-addons">
                    <div class="control is-fullwidth">
                      <input type="string" [(ngModel)]="flag.value" class="input"/>
                    </div>
                    <div class="control">
                      <button (click)="updateFlag(flag, flag.value, SysFlagType.STRING)" class="button is-info" [class.is-loading]="flag.isSending">
                        <i class="fas fa-check"></i>
                      </button>
                    </div>
                  </div>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
      <div [ngSwitch]="cModal().type">
          <div *ngSwitchCase="SysFlagModal.PREVIEW_MARKDOWN" style="width: 75em;">
              <h2><code>{{cmc().flag.key}}</code></h2>
              <p>{{cmc().flag.description}}</p>
              <div class="columns" style="margin-top: 1.5em">
                <div class="column">
                  <h3><b>English</b></h3>
                  <div class="notification is-light">
                    <tra-md [slug]="cmc().flag.value_en"></tra-md>
                  </div>
                </div>
                <div class="column">
                  <h3><b>French</b></h3>
                  <div class="notification is-light">
                    <tra-md [slug]="cmc().flag.value_fr"></tra-md>
                  </div>
                </div>
              </div>
              <modal-footer [pageModal]="pageModal" [confirmButton]="false" closeMessage="btn_close"></modal-footer>
          </div>
      </div>
  </div>
</div>