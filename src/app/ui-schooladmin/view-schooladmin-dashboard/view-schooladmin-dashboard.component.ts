import { LangService } from '../../core/lang.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { Subscription } from 'rxjs';
import { DomSanitizer, Title } from '@angular/platform-browser';
import { memo } from '../../ui-testrunner/element-render-video/element-render-video.component';
import { AccountType } from '../../constants/account-types';
import { G9DemoDataService, assessmentSessionStatuses, sessionsCountLabel } from '../g9-demo-data.service';
import { SidepanelService } from '../../core/sidepanel.service';
import { SchoolAdminView, SCHOOL_ADMIN_VIEWS, BC_FSA_SCHOOL_ADMIN_VIEWS, BC_GRAD_SCHOOL_ADMIN_VIEWS, BCFSASchoolAdminView, BCGradSchoolAdminView, NBED_SCHOOL_ADMIN_VIEWS, NBEDSchoolAdminView, ABED_SCHOOL_ADMIN_VIEWS ,SCHOOL_ADMIN_VIEWS_OPTIONAL, SCHL_BOARDS_SCHOOL_ADMIN_VIEWS } from './data/views';
import { ICheckListItem } from '../sa-tech-readiness/data/types';
import { ClassFilterId, MySchoolService, SchoolType } from '../my-school.service';
import { boardTechReadinessCheck, techReadiGoMode } from '../sa-tech-readiness/data/checklist';
import { composeChecklist } from "../sa-tech-readiness/data/util";
import { AuthService } from '../../api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { BcHeaderLayoutComponent } from '../../ui-partial/bc-header-layout/bc-header-layout.component';
import { Component, OnInit, ViewEncapsulation, ChangeDetectorRef } from '@angular/core';
import { IStudentAccount, ConfidentialityAgreement } from '../data/types';
import { IView, AttestAgreement, SchoolAdminModal, ProfResponsibilityAgreement, IAgreementConfirmData, IAgreementConfirmConfig} from './data/types';
import { SchoolTeacherAdminQuestionnaireService, ITypeSlug} from '../../ui-questionnaire/school-teacher-admin-questionnaire.service'
import { PageModalService, PageModalController } from 'src/app/ui-partial/page-modal.service';
import moment from 'moment';
import { I, T } from '@angular/cdk/keycodes';
import { formatDate } from '@angular/common';
import { AssessmentsService } from 'src/app/assessments.service';


const checklist = {};
const ATTEST_CONFIRMED = 'ATTEST_CONFIRMED'

@Component({
  selector: 'view-schooladmin-dashboard',
  templateUrl: './view-schooladmin-dashboard.component.html',
  styleUrls: ['./view-schooladmin-dashboard.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ViewSchooladminDashboardComponent implements OnInit {
  primaryStudents: IStudentAccount[];
  juniorStudents: IStudentAccount[];
  ossltStudents: IStudentAccount[];
  g9Students: IStudentAccount[];
  ConfidentialityAgreement = ConfidentialityAgreement;
  hasAttestation: boolean;

  constructor(
    public mySchool: MySchoolService,
    public lang: LangService,
    private loginGuard: LoginGuardService, //
    private breadcrumbsService: BreadcrumbsService,
    private router: Router,
    private sidePanel: SidepanelService,
    private g9DemoData: G9DemoDataService,
    private route: ActivatedRoute,
    public sanitizer: DomSanitizer,
    private auth: AuthService,
    private routes: RoutesService,
    private whiteLabelService: WhitelabelService,
    private taQuestionnaire: SchoolTeacherAdminQuestionnaireService,
    private changeDetector: ChangeDetectorRef,
    private pageModalService: PageModalService,
    private assessmentService: AssessmentsService
  ) {
  }

  public breadcrumb = [];
  // `breadcrumbProxy` is passed to components to allow easy changes to the breadcrumb
  public breadcrumbProxy;
  public schoolData;
  public schools = [];
  public schoolGroupId;

  // list:ICheckListItem[] = SCHOOL_ADMIN_TECH_READ_CHECKLIST;
  // If the same component is used for both FSA and Grad, it may be better to put it in `SchoolAdminView`,
  //  but so far no such case exists.
  SchoolAdminView = SchoolAdminView;
  BCFSASchoolAdminView = BCFSASchoolAdminView
  BCGradSchoolAdminView = BCGradSchoolAdminView
  selectedView: SchoolAdminView | BCFSASchoolAdminView | BCGradSchoolAdminView; // = SchoolAdminView.CLASSROOMS;
  selectedBcFsaView = BCFSASchoolAdminView;
  selectedBcGradView = BCGradSchoolAdminView;
  schoolType = this.route.snapshot.data['schoolType'];

  views: IView<SchoolAdminView>[] = [];
  viewsOptional: IView<SchoolAdminView>[] = [];
  routeSub: Subscription;
  subs: Subscription[] = [];
  sanitizedUrls = new Map();
  isInited: boolean;

  // "isLoading" is used when we are loading this page for the second time.
  // example: clicking on the "invigilate" button, and then pressing the back button
  isLoading: boolean = false;

  isPrivateSchool: boolean;
  noRole = false;
  // checklist;
  checklist = checklist;
  primary_checklist_completed = true;
  junior_checklist_completed = true;
  g9_checklist_completed = true;
  osslt_checklist_completed = true;
  abedChecklistCompleted = true;
  checklistLoaded = false;

  ATTEST_CONFIRMED = 'confirmed'
  SchoolAdminModal = SchoolAdminModal;
  techReadinessConfirmed: boolean = true;
  boardTechReadinessConfirmed: boolean;
  warnBoardTechReadiness: boolean;
  currentClassFilter: ClassFilterId;
  ClassFilterId = ClassFilterId;
  currentQueryParams
  g9StudentRecordHasError ;
  g10StudentRecordHasError ;
  // Questionnaire
  questionnaireSessionId: number;
  questionnaireDueOn: string
  // Payment Agreement
  showPaymentAgreement: boolean = false;
  showPaymentAgreementAlreadyPrompted: boolean = false;
  unpaidClassFilters: string[] = []

  isPaymentModuleEnabled: boolean = false;
  isSchoolAccessView: boolean = false;

  haveQuestionnaireSession = false;
  pageModal: PageModalController;

  ngOnInit() {
    this.pageModal = this.pageModalService.defineNewPageModal();
    if(this.mySchool.getIsFromBoardLed()){
      this.isInited = false;
    }

    this.breadcrumbProxy = {
      clear: () => {
        this.breadcrumb = [];
      },
      push: item => {
        this.breadcrumb.push(item);
      }
    };

    if (this.schoolType === 'BC_FSA') {
      this.views = BC_FSA_SCHOOL_ADMIN_VIEWS.map(view => Object({
        ...view,
        caption: this.lang.tra(view.caption),
        description: this.lang.tra(view.description),
      }))
    }
    else if (this.schoolType === 'BC_GRAD') {
      this.views = BC_GRAD_SCHOOL_ADMIN_VIEWS.map(view => Object({
        ...view,
        caption: this.lang.tra(view.caption),
        description: this.lang.tra(view.description),
      }))
    }
    else if (this.schoolType === 'NBED'){
      this.views = NBED_SCHOOL_ADMIN_VIEWS.map(view => Object({
        ...view,
        caption: this.lang.tra(view.caption),
        description: this.lang.tra(view.description),
      }))
    }
    else {
      this.views = SCHOOL_ADMIN_VIEWS.map(view => Object({
        ...view,
        caption: this.lang.tra(view.caption),
        description: this.lang.tra(view.description),
      }))
     this.viewsOptional = SCHOOL_ADMIN_VIEWS_OPTIONAL.map(view => Object({
        ...view,
        caption: this.lang.tra(view.caption),
        description: this.lang.tra(view.description),
      }))
    }
    this.loginGuard.activate();
    this.loginGuard.runHealthCheck();
    this.sidePanel.activate();
    this.sidePanel.unexpand();
    this.updateBreadcrumb();

    //this.updateSchoolInfo(true); // TEMP

    // this.loadChecklists()
    // this.views = SCHOOL_ADMIN_VIEWS.map(view => Object({
    //   ... view,
    //   caption: this.lang.tra(view.caption),
    //   description: this.lang.tra(view.description),
    // }))
    // this.subs = this.subs.concat([
    //   this.mySchool.sub().subscribe(this.updateSchoolInfo),
    //   this.mySchool.fail.subscribe(this.onRoleFail),
    // ]);
    // this.views = SCHOOL_ADMIN_VIEWS.map(view => Object({
    //   ... view,
    //   caption: this.lang.tra(view.caption),
    //   description: this.lang.tra(view.description),
    // }))
    // this.views = SCHOOL_ADMIN_VIEWS.map(view => Object({
    //   ...view,
    //   caption: this.lang.tra(view.caption),
    //   description: this.lang.tra(view.description),
    // }))
    // if (this.isPrivateSchool) {
    //   this.setPrivateSchoolChecklist();
    // }

    //  Decide which view to use #MERGE_20220524 : REWROTE
    this.views = this.getAdminViews().map(view => Object({
      ...view,
      caption: this.lang.tra(view.caption),
      description: this.lang.tra(view.description),
    }))
    this.route.queryParams.subscribe(async (queryParams) => {
      // todo: generalize to all props
      this.currentQueryParams = {
        uid: queryParams['uid'],
        school: queryParams['school']
      }
      const schoolGroupId = queryParams['school'];
      this.schoolGroupId = schoolGroupId;
      if (schoolGroupId){
        this.isInited = false;
      }
      await this.mySchool.selectSchool(schoolGroupId);
    });
    // Reload when returning from invigilation view back to school admin via breadcrumbs or back button
    const isFromInvig = this.g9DemoData.getIsFromInvig();
    if (isFromInvig){
      this.updateSchoolInfo(this.mySchool);
    }
    this.g9DemoData.setIsFromInvig(false);

    this.subs = this.subs.concat([
      this.mySchool.sub().subscribe(this.updateSchoolInfo),
      this.mySchool.fail.subscribe(this.onRoleFail),
    ]);

    this.mySchool.sub().subscribe( (_:any) =>{
      const schlGroupId = this.mySchool.getCurrentSchoolGroupId();
      if(schlGroupId){
        this.initQuestionnaire();
        if(this.isABED()) this.haveUserAgreedOnConfidentiality();
      }
    })


  }

  ngOnDestroy(){
    //this.mySchool.sub().unsubscribe()
    this.subs.forEach(sub => {
      sub.unsubscribe();
    })
  }

  changeViewsBasedOnSchoolLang(schoolData: any) {
    // console.log(schoolData);

    if (schoolData == null) { return; }

    let schoolLang = this.lang.c();
    // let schoolLang = schoolData.lang.toLocaleLowerCase(); // creates silent error
    // this.lang.setCurrentLanguage(schoolLang);

    this.views = ABED_SCHOOL_ADMIN_VIEWS
    .filter(view => !view.isBlocked)
    .map(view => Object({
      ...view,
      caption: this.lang.tra(view.caption, schoolLang),
      description: this.lang.tra(view.description, schoolLang),
    }));

    this.views = this.views.filter((view: any) => {
      if (view.whiteLabelContextFlag) {
        return !!this.whiteLabelService.getSiteFlag(view.whiteLabelContextFlag)
      }
      return true;
    });
  }


  loadChecklists() {
    this.mySchool
      .loadChecklistStates(true)
      .then(state => {
        this.checklist = state;
        this.verifyChecklistCompletion()
        this.checklistLoaded = true;
      })
  }

  isVEA(){
    return this.whiteLabelService.getSiteFlag('IS_VEA')
  }

  reset_flag:boolean = true
  detectChanges(){
    this.reset_flag = false
    this.changeDetector.detectChanges();
    this.reset_flag = true;
  }

  initQuestionnaire = () => {
    this.taQuestionnaire
    .findQuestionnaireSession(AccountType.SCHOOL_ADMIN, ITypeSlug.PRINCIPAL_QUESTIONNAIRE, this.mySchool.getCurrentSchoolGroupId())
    .then((session) => {
      if(session){
        // there will be only one session for all the educators
        this.questionnaireSessionId = session[0].id
        this.questionnaireDueOn = moment(session[0].due_on).format('LL')
        this.detectChanges()
        this.haveQuestionnaireSession = true
      }
    })
  }

  onClickQuestionnaire = () => {

    if(!this.questionnaireSessionId){
      alert("No Questionnaire session found");
      return;
    }

    this.router.navigate([this.getQuestionnaireRouteLink(this.questionnaireSessionId)]);

  }

  getQuestionnaireDueDate = () => this.questionnaireDueOn ? this.questionnaireDueOn : ''

  getQuestionnaireRouteLink = (test_session_id) => {
    return `/${this.lang.c()}/school-admin/questionnaire/${test_session_id}`
  }

  isEQAO() {
    return this.whiteLabelService.getSiteFlag('IS_EQAO');
  }
  isBced() {
    return this.whiteLabelService.getSiteFlag('IS_BCED');
  }

  isPrimary() {
    return this.currentClassFilter == ClassFilterId.Primary;
  }

  isJunior() {
    return this.currentClassFilter == ClassFilterId.Junior;
  }

  isG9() {
    return this.currentClassFilter == ClassFilterId.G9;
  }

  isOsslt() {
    return this.currentClassFilter == ClassFilterId.OSSLT;
  }

  showGeneralDashBoardViews = () => this.isEQAO() || this.whiteLabelService.isNBED() || this.whiteLabelService.isMBED() || this.whiteLabelService.isABED()

  isNotG9AndNotOsslt() {
    return this.currentClassFilter != ClassFilterId.G9 && this.currentClassFilter != ClassFilterId.OSSLT;
  }

  isNotPJAndNotG9AndNotOsslt() {
    return this.currentClassFilter != ClassFilterId.Primary && this.currentClassFilter != ClassFilterId.Junior && this.currentClassFilter != ClassFilterId.G9 && this.currentClassFilter != ClassFilterId.OSSLT;
  }

  onRoleFail = (failed: boolean) => {
    if (failed) {
      this.isInited = false;
      this.noRole = true;
      console.log('no school admin role');
    }
  }

  updateSchoolInfo = (schoolInfo: any) => {
    console.log('updateSchoolInfo')
    const FromBoardLed = this.mySchool.getIsFromBoardLed()
    const newSchoolLoaded = this.mySchool.getNewSchoolLoaded()
    const schoolData = this.g9DemoData.schoolData;
    if(schoolData && schoolData.group_id != this.schoolGroupId){
      schoolInfo = null;
    }
    const isFromInvig = this.g9DemoData.getIsFromInvig();
    if (schoolInfo && !this.isInited && (isFromInvig || !FromBoardLed  || (FromBoardLed && newSchoolLoaded))) {
      this.isInited = true;
      this.noRole = false;
      this.schoolData = this.g9DemoData.schoolData;
      this.isPrivateSchool = !!this.schoolData["is_private"];
      this.addSchoolIdtoUrl(this.schoolData.group_id)
      this.loadChecklists()
      this.initRouteSub();
      this.primaryStudents = this.g9DemoData.schoolAdminStudents.list.filter(student =>{
        if (student.eqao_is_g3 == 1) return student;
      });
      this.juniorStudents = this.g9DemoData.schoolAdminStudents.list.filter(student =>{
        if (student.eqao_is_g6 == 1) return student;
      });
      this.g9Students = this.g9DemoData.schoolAdminStudents.list.filter(student =>{
        if (student.eqao_is_g9 == 1) return student;
      });
      this.ossltStudents = this.g9DemoData.schoolAdminStudents.list.filter(student =>{
        if (student.eqao_is_g10 == 1) return student;
      });

      if (this.isPrivateSchool && !this.isABED()) {
        this.checkForUnpaidSessions();
        this.showPaymentAgreement = this.g9DemoData.showPaymentAgreement;
        if(this.showPaymentAgreement){
          if (!this.showPaymentAgreementAlreadyPrompted){
            this.displayPaymentAgreement();
          }
        } 
        else {
          this.displayAgreement(AttestAgreement, (_) => {});
        }
      }
      if(this.showResponsibilityAgreement()) {
        this.checkRespAgreement(ProfResponsibilityAgreement)
      }
      if (this.g9DemoData.schoolData && !isFromInvig) {
        // this case will happen when we enter the school admin page right after logging in or after refreshing the page
        this.isLoading = false;
      } 
    }
    else if (!schoolInfo) {
      this.isLoading = true;
      this.isInited = false;
    } 
    else {
      this.schools = [this.g9DemoData.schoolData] // for now
      this.isLoading = false;
    }

  }

  checkForUnpaidSessions() {
    const assessmentsGroupIDsDictionary = [];
    this.g9DemoData.schoolSessions.forEach(session => {
      if(session.is_closed == 0 && session.sc_is_inactive == 0 && (session.slug == 'G9_OPERATIONAL' || session.slug == 'OSSLT_OPERATIONAL' || session.slug == 'JUNIOR_OPERATIONAL' || session.slug == 'PRIMARY_OPERATIONAL') && !assessmentsGroupIDsDictionary[session.group_id]) {
        assessmentsGroupIDsDictionary[session.group_id] = session;
      }
    });

    const filterStudent = (student) => {
      if(assessmentsGroupIDsDictionary[student.class_group_id]) {
        return student;
      }
    }

    const { payment_req_pj, payment_req_g9, payment_req_osslt } = this.g9DemoData.schoolData;
    if(payment_req_pj) {
      const primaryStudentsWithSession = this.primaryStudents.filter(filterStudent);
      const juniorStudentsWithSession = this.juniorStudents.filter(filterStudent);
      const unpaidPrimaryStudent = primaryStudentsWithSession.find(student => student.isPaid == 0 && student.altPaymentStatus == 0)
      const unpaidJuniorStudent = juniorStudentsWithSession.find(student => student.isPaid == 0 && student.altPaymentStatus == 0)

      if(unpaidPrimaryStudent) {
        this.addToUnpaidClassFilters('lbl_primary');
      }
      if(unpaidJuniorStudent) {
        this.addToUnpaidClassFilters('lbl_junior');
      }
    }

    if(payment_req_g9) {
      const g9StudentsWithSession = this.g9Students.filter(filterStudent);
      const unpaidG9Student = g9StudentsWithSession.find(student => student.isPaid == 0 && student.altPaymentStatus == 0)

      if(unpaidG9Student) {
        this.addToUnpaidClassFilters('txt_g9_math');
      }
    }

    if(payment_req_osslt) {
      const ossltStudentsWithSession = this.ossltStudents.filter(filterStudent);
      const unpaidOssltStudent = ossltStudentsWithSession.find(student => student.isPaid == 0 && student.altPaymentStatus == 0)

      if(unpaidOssltStudent) {
        this.addToUnpaidClassFilters('lbl_osslt');
      }
    }
  }

  configureQueryParams(){
    return { query: {schl_group_id: this.mySchool.getCurrentSchoolGroupId()}}
  }

  acceptAllAgreements() {
    this.auth.apiCreate(this.routes.SCHOOL_ADMIN_RESPONSIBILITY_AGREEMENTS, this.g9DemoData.totalNonAcceptedRecords, this.configureQueryParams());
    this.g9DemoData.totalNonAcceptedRecords = [];
  }

  checkRespAgreement(agreement: IAgreementConfirmConfig, callback?: (agreement: IAgreementConfirmConfig) => void) {
    const nonAcceptedWindows = this.g9DemoData.totalNonAcceptedRecords;

    if(this.isPrivateSchool || nonAcceptedWindows.length <= 0) {
      return;
    }

    this.resolveAgreement(agreement, () => {this.acceptAllAgreements()});
  }

  displayAgreement(agreement: IAgreementConfirmConfig, callback?: (agreement: IAgreementConfirmConfig) => void) {
    const agreementConfirm: IAgreementConfirmData = JSON.parse(localStorage.getItem(agreement.storageKey));
    if(agreementConfirm && this.is24HrWindow(agreementConfirm.dateTime)) {
      return
    }
    this.resolveAgreement(agreement, callback);
  }

  resolveAgreement(agreement: IAgreementConfirmConfig, callback?: (agreement: IAgreementConfirmConfig) => void) {
    this.confirmAgreement(agreement, callback)
    .then(_ => {
      const confirmationData: IAgreementConfirmData = {
        isConfirmed: true,
        dateTime: new Date().toISOString()
      }
      localStorage.setItem(agreement.storageKey, JSON.stringify(confirmationData))
    })
  }

  confirmAgreement(agreement: IAgreementConfirmConfig, callback = (agr) => {}): Promise<any> {
    return new Promise<void>((resolve, reject) => {
      this.showPaymentAgreementAlreadyPrompted = true;
      const caption = this.lang.tra(agreement.captionSlug);
      const checkBoxText = this.lang.tra(agreement.checkboxSlug);
      this.loginGuard.confirmationReqActivate({
        caption,
        requireCheckboxInput: { checkboxCaption: checkBoxText },
        btnProceedConfig: {
          caption: this.lang.tra(agreement.btnSlug)
        },
        btnCancelConfig: {
          hide: true
        },
        width:'43em',
        confirm: () => {
          // Insert a payment agreement record into table 'school_payment_agreements'
          // this.mySchool.createPaymentAgreement()
          callback(agreement);
          resolve();
        },
      })
    })
  }

  async confirmPaymentAgreement(event) {
    this.showPaymentAgreement = false;
    this.pageModal.closeModal();
    this.displayAgreement(AttestAgreement, (_) => {});
  }

  async displayPaymentAgreement() {
    const config = {}
    this.showPaymentAgreementAlreadyPrompted = true;
    console.log('displayPaymentAgreement', this.showPaymentAgreementAlreadyPrompted);
    this.pageModal.newModal({
      type: SchoolAdminModal.VIEW_PAYMENT_AGREEMENT,
      config,
      finish: () => {}
    })
  }

  getUnpaidClassFilters(): string {
    if (!this.unpaidClassFilters || this.unpaidClassFilters.length === 0) {
      return null;
    }
    const unpaidClass = this.unpaidClassFilters.map(classFilter => this.lang.tra(classFilter))
    return unpaidClass.join(', ');
  }

  addToUnpaidClassFilters(filter: string): void {
    if(this.unpaidClassFilters && this.unpaidClassFilters.indexOf(filter) == -1) {
      this.unpaidClassFilters.push(filter)
    }
  }

  removeFromUnpaidClassFilters(filter: string): void {
    if(this.unpaidClassFilters) {
      const index = this.unpaidClassFilters.indexOf(filter);
      if(index !== -1){
        this.unpaidClassFilters.splice(index, 1);
      }
    }
  }

  addSchoolIdtoUrl(id){
    let params: Params = {
      ...this.route.snapshot.queryParams,
    };
    params.school = id;
    this.router.navigate(
      [],
      {
        relativeTo: this.route,
        queryParams: params
      });
  }

  initRouteSub() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
    this.routeSub = this.route.params.subscribe(e => this.onRouteChange(e));
  }

  getAccountType() {
    return AccountType.SCHOOL_ADMIN;
  }

  getBaseRoute() {
    return `/${this.lang.c()}/${this.getAccountType()}`;
  }
  getViewRoute(viewSlug: SchoolAdminView | BCFSASchoolAdminView | BCGradSchoolAdminView) {
    if (this.schoolType === 'BC_FSA') {
      return this.getBaseRoute() + '/bc-fsa/' + viewSlug
    } else if (this.schoolType === 'BC_GRAD') {
      return this.getBaseRoute() + '/bc-grad/' + viewSlug
    } else {
      return this.getBaseRoute() + '/' + viewSlug
    }
  }
  switchViews(viewRout: any){
    let queryParams = this.route.snapshot.queryParams;
    this.router.navigate([viewRout], {
      relativeTo: this.route,
      queryParams
    });
  }
  updateBreadcrumb() {
    let schoolAdmin = this.lang.tra('ABED_sa_dashboard_school_admin');
    if(this.mySchool.getIsFromBoardLed()){
      const boardLead = this.lang.tra('bl_dashboard_board_lead');
      this.breadcrumb = [
        this.breadcrumbsService._CURRENT(boardLead, `/${this.lang.c()}/${AccountType.SCHOOL_DISTRICT_CURRI}`),
        this.breadcrumbsService._CURRENT(schoolAdmin, this.getViewRoute(SchoolAdminView.DASHBOARD), this.currentQueryParams)
      ];
    }else{
      this.breadcrumb = [
        this.breadcrumbsService._CURRENT(schoolAdmin, this.getViewRoute(SchoolAdminView.DASHBOARD), this.currentQueryParams)
      ];
    }
    if (this.selectedView) {
      let currentView = this.views.find(view => view.id === this.selectedView)
      if(!currentView) {
        currentView = this.views[0]
        this.selectView(currentView.id)
      }
      const viewCaption = currentView.caption;
      this.breadcrumb.push(
        this.breadcrumbsService._CURRENT(viewCaption, this.getViewRoute(this.selectedView), this.currentQueryParams),
      )
      // this.breadcrumbsService.
    }
    if(this.isSchoolAccessView){
      const viewCaption = this.lang.tra('sa_school_access_panel');
      this.breadcrumb.push(
        this.breadcrumbsService._CURRENT(viewCaption, this.getViewRoute(SchoolAdminView.SCHOOL_ACCESS_PANEL), this.currentQueryParams),
      )
    }
    /*
    if (!this.selectedView) {
      this.breadcrumb.push(
        this.breadcrumbsService._CURRENT(this.lang.tra('title_dashboard'), this.getViewRoute(SchoolAdminView.DASHBOARD))
      )
    }
    */
  }

  getUrl(url: string) {
    return memo(this.sanitizedUrls, url, url => this.sanitizer.bypassSecurityTrustResourceUrl(url));
  }

  onRouteChange(routeParams: any) {
    window.scrollTo(0, 0);
    const targetView: SchoolAdminView = routeParams['view'];
    if (targetView === SchoolAdminView.DASHBOARD) { //  || targetView === SchoolAdminView.DASHBOARDZ
      this.selectedView = null;
      this.isSchoolAccessView = false
      this.loadChecklists();
    }
    else if(targetView === SchoolAdminView.SCHOOL_ACCESS_PANEL){
      this.isSchoolAccessView = true
    }
    else {
      this.isSchoolAccessView = false
      this.selectedView = targetView;
    }
    this.updateBreadcrumb();
    // this.baseRoute = this.breadcrumbPre[this.breadcrumbPre.length - 1].route + '/'; // could be called just once, not on each route change
  }

  getTechnicalReadinessCaption(view: IView<SchoolAdminView>) {
    if(this.isPrimary() || this.isJunior()){
      return this.lang.tra("pj_caption_admin_checklist")
    } else {
      return view.caption;
    }
  }

  getMessageProps(id: SchoolAdminView | BCFSASchoolAdminView | BCGradSchoolAdminView) {
    switch (id) {
      case SchoolAdminView.TEACHERS: return {
        NUM_TEACHERS: this.g9DemoData.teachers.list.length
      }
      case SchoolAdminView.STUDENTS:
        var num_students = this.g9DemoData.schoolAdminStudents.list.length;
        if (this.whiteLabelService.isABED()) {
          if (this.currentClassFilter != null) {
            num_students = this.g9DemoData.schoolAdminStudents.list.
            filter(student => student?.course?.GroupType === this.currentClassFilter ||
              +student?.course?.[this.currentClassFilter] === 1).length;
          }

          else {
            // this logic for ABED helps ensure if no filter is selected, only ENABLED filters
            // are summed up in the default counts, for clearer number (students).
            let enabledClassFilters = this.assessmentService.getEnabledAssessmentDefs().
            map(assDef => assDef.assessment_slug);

            const studentIsInEnabledClassFilter = (student): boolean => {
              for (let classFilter of enabledClassFilters) {
                if (+student?.course?.[classFilter] === 1) {
                  return true;
                }
              }
              return false;
            }

            num_students = this.g9DemoData.schoolAdminStudents.list.
            filter(student => enabledClassFilters.includes(student?.course?.GroupType) ||
            studentIsInEnabledClassFilter(student)).length;
          }

        }
        if (this.currentClassFilter == ClassFilterId.Primary) {
          num_students = this.primaryStudents.length;
        } else if (this.currentClassFilter == ClassFilterId.Junior) {
          num_students = this.juniorStudents.length;
        } else if (this.currentClassFilter == ClassFilterId.G9) {
          num_students = this.g9Students.length;
        } else if (this.currentClassFilter == ClassFilterId.OSSLT) {
          num_students =  this.ossltStudents.length;
        }
        return {
          NUM_STUDENTS: num_students
        }
      case SchoolAdminView.SESSIONS:
        let num_scheduled_sessions = this.g9DemoData.scheduledSessionsCount;
        let num_sessions = this.g9DemoData.activeSessionsCount;
        let num_submitted_sessions = this.g9DemoData.submittedSessionsCount;

        if (this.whiteLabelService.isABED()) {
          if (this.currentClassFilter != null) {
            const sessionCounts = this.g9DemoData.abedSessionsCounts.
            find(sessionCountInfo => sessionCountInfo.classFilter === this.currentClassFilter);
            if (sessionCounts != null) {
              num_sessions = sessionCounts[assessmentSessionStatuses[0] + sessionsCountLabel];
              num_scheduled_sessions = sessionCounts[assessmentSessionStatuses[1] + sessionsCountLabel];
              num_submitted_sessions = sessionCounts[assessmentSessionStatuses[2] + sessionsCountLabel];
            }
          }

          else {
            // this logic for ABED helps ensure if no filter is selected, only ENABLED filters
            // are summed up in the default counts, for clearer numbers (assessment sessions).
            num_sessions = 0;
            num_scheduled_sessions = 0;
            num_submitted_sessions = 0;
            this.g9DemoData.abedSessionsCounts.forEach(sessionCountInfo => {
            num_sessions += sessionCountInfo[assessmentSessionStatuses[0] + sessionsCountLabel];
            num_scheduled_sessions += sessionCountInfo[assessmentSessionStatuses[1] + sessionsCountLabel];
            num_submitted_sessions +=  sessionCountInfo[assessmentSessionStatuses[2] + sessionsCountLabel];
            });
          }

        } else if (this.currentClassFilter == ClassFilterId.Primary) {
          num_scheduled_sessions = this.g9DemoData.primaryScheduledSessionsCount;
          num_sessions = this.g9DemoData.primaryActiveSessionsCount;
        } else if (this.currentClassFilter == ClassFilterId.Junior) {
          num_scheduled_sessions = this.g9DemoData.juniorScheduledSessionsCount;
          num_sessions = this.g9DemoData.juniorActiveSessionsCount;
        } else if (this.currentClassFilter == ClassFilterId.G9) {
          num_scheduled_sessions = this.g9DemoData.g9ScheduledSessionsCount;
          num_sessions = this.g9DemoData.g9ActiveSessionsCount;
        } else if (this.currentClassFilter == ClassFilterId.OSSLT) {
          num_scheduled_sessions = this.g9DemoData.ossltScheduledSessionsCount;
          num_sessions = this.g9DemoData.ossltActiveSessionsCount;
        }

        let props = {
          NUM_SCHEDULED_SESSIONS: num_scheduled_sessions,
          NUM_SESSIONS: num_sessions
        };

        if (!this.whiteLabelService.isABED()) {
          return props;
        }

        else {
          let modProp = {
            ...props,
          NUM_SUBMITTED_SESSIONS: num_submitted_sessions
          };

          return modProp;
        }

      case SchoolAdminView.QUESTIONNAIRE:
        return { DUE_DATE : this.getQuestionnaireDueDate() }
    }
  }

  isFSA(): boolean {
    return this.route.snapshot.data['schoolType'] === 'BC_FSA'
  }

  selectView(id: SchoolAdminView)
  {
    if (id !== SchoolAdminView.STUDENTS)
    {
      // do not persist grouping filter unless selecting a grouping from Groupings page
      this.groupFilterValuePreset = "";
    }

    console.log("Selected view: ", id)
    if (this.selectedView === id)
    {
      // already on the target route
    }

    else
    {
      this.router.navigate(
        [this.getViewRoute(id)],
        {
          queryParams: this.currentQueryParams
        }
      );
      //this.currentQueryParams = {};
    }
    // this.updateBreadcrumb();
  }

  groupFilterValuePreset: string;
  clickGroupName($event) {
    this.selectView(SchoolAdminView.STUDENTS);
    this.groupFilterValuePreset = $event.class_code;
    console.log("groupFilterValuePreset: ", this.groupFilterValuePreset)
  }

  // verifyChecklistCompletion = () => {
  //   let isAnyUnchecked = false;
  //   this.list.forEach(listEntry => {
  //     //console.log(listEntry)
  //     //console.log(this.checklist)
  //     if (!this.checklist[listEntry.id]){
  //       isAnyUnchecked = true;
  //     }
  //   })
  //   this.techReadinessConfirmed = !isAnyUnchecked; // todo, this is still poorly named
  // }

  verifyChecklistCompletion = () => {

    try {

      this.primary_checklist_completed = true;
      this.junior_checklist_completed = true;
      this.g9_checklist_completed = true;
      this.osslt_checklist_completed = true;
      this.abedChecklistCompleted = true;
      let primary_list;
      let junior_list;
      let g9_list;
      let osslt_list;
      let abedList;
  
      if (this.isPrivateSchool) {
        primary_list = composeChecklist(require('../sa-tech-readiness/data/by-filter/primary_private.json'), true);
        junior_list = composeChecklist(require('../sa-tech-readiness/data/by-filter/junior_private.json'), true);
        g9_list = composeChecklist(require('../sa-tech-readiness/data/by-filter/g9_private.json'), true);
        osslt_list = composeChecklist(require('../sa-tech-readiness/data/by-filter/osslt_private.json'), true);
      }
      else {
        primary_list = composeChecklist(require('../sa-tech-readiness/data/by-filter/primary.json'), false);
        junior_list = composeChecklist(require('../sa-tech-readiness/data/by-filter/junior.json'), false);
        g9_list = composeChecklist(require('../sa-tech-readiness/data/by-filter/g9.json'), false);
        osslt_list = composeChecklist(require('../sa-tech-readiness/data/by-filter/osslt.json'), false);
      }
  
      //revisit when abed is implementing private school logic.
      abedList = composeChecklist(require('../sa-tech-readiness/data/by-filter/abed.json'), false);
  
      const isKioskApproved = this.g9DemoData.schoolData["is_kiosk_approved"];
      if (this.isPrivateSchool && !isKioskApproved) {
        let configIndG9 = g9_list.findIndex((config) => config.id === 'tech_redi_device_1');
        let configIndOSSLT = osslt_list.findIndex((config) => config.id === 'tech_redi_device_1_osslt');
        g9_list[configIndG9].id = 'tech_redi_device_g9';
        osslt_list[configIndOSSLT].id = 'tech_redi_device_osslt';
      }
  
      if (this.checklist[boardTechReadinessCheck] || this.whiteLabelService.isABED()) {
        // ABED does not have school board functionality yet; so for ABED,
        // make board tech readiness confirmed automatically
        this.boardTechReadinessConfirmed = true;
        this.warnBoardTechReadiness = false;
      }
  
      else if (this.checklist[techReadiGoMode]) {
        this.warnBoardTechReadiness = true;
        this.boardTechReadinessConfirmed = false;
      }
      primary_list.forEach(listEntry => {
        if (!this.checklist[listEntry.id]) {
          this.primary_checklist_completed = false;
        }
      })
      junior_list.forEach(listEntry => {
        if (!this.checklist[listEntry.id]) {
          this.junior_checklist_completed = false;
        }
      })
      g9_list.forEach(listEntry => {
        if (!this.checklist[listEntry.id]) {
          this.g9_checklist_completed = false;
        }
      })
      osslt_list.forEach(listEntry => {
        if (!this.checklist[listEntry.id]) {
          this.osslt_checklist_completed = false;
        }
      })
  
      abedList.forEach(listEntry => {
        if (!this.checklist[listEntry.id]) {
          this.abedChecklistCompleted = false;
        }
      })
  
      const school_type = this.mySchool.getCurrentSchoolType();
  
      if (!this.currentClassFilter) {
        switch(school_type){
          case SchoolType.ELEMENTARY:
            this.techReadinessConfirmed = this.primary_checklist_completed && this.junior_checklist_completed
            break
          case SchoolType.ELEMENTARY:
            this.techReadinessConfirmed = this.g9_checklist_completed && this.osslt_checklist_completed
            break
          case SchoolType.K12:
          default:
            this.techReadinessConfirmed = this.primary_checklist_completed && this.junior_checklist_completed && this.g9_checklist_completed && this.osslt_checklist_completed
          break
        }
      } else if (this.currentClassFilter === ClassFilterId.Primary) {
        this.techReadinessConfirmed = this.primary_checklist_completed
      } else if (this.currentClassFilter === ClassFilterId.Junior) {
        this.techReadinessConfirmed = this.junior_checklist_completed
      } else if (this.currentClassFilter === ClassFilterId.G9) {
        this.techReadinessConfirmed = this.g9_checklist_completed
      } else if (this.currentClassFilter === ClassFilterId.OSSLT) {
        this.techReadinessConfirmed = this.osslt_checklist_completed
      }
      else if (this.whiteLabelService.isABED()) {
        this.techReadinessConfirmed = this.abedChecklistCompleted;
      }
    }
    catch(e){
      console.warn('Cannot validate checklist completion')
    }
  }

  setTechReadiness(event) {
    if (!event) {
      this.warnBoardTechReadiness = false;
    }
    this.techReadinessConfirmed = event;
  }

  pageUnavailable(e: Event) {
    if (e) {
      (e.target as HTMLElement).blur();
    }
    this.loginGuard.quickPopup('This page is currently unavailable.')
  }
  setClassFilter(filterId) {
    this.currentClassFilter = filterId;

    if (this.isPrivateSchool) {
      const {payment_req_g9, payment_req_osslt, payment_req_pj} = this.schoolData;
      const isPurchasesDisplayed = this.views.find(view => view.id == SchoolAdminView.PURCHASES);

      if (payment_req_pj && (this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior)) {
        if (!isPurchasesDisplayed) {
          this.isPaymentModuleEnabled = true;
          this.views.push(this.viewsOptional.find(view => view.id == SchoolAdminView.PURCHASES))
        }
      } else if (payment_req_osslt && this.currentClassFilter === ClassFilterId.OSSLT) {
        if (!isPurchasesDisplayed) {
          this.isPaymentModuleEnabled = true;
          this.views.push(this.viewsOptional.find(view => view.id == SchoolAdminView.PURCHASES))
        }
      } else if (payment_req_g9 && this.currentClassFilter === ClassFilterId.G9) {
        if (!isPurchasesDisplayed) {
          this.isPaymentModuleEnabled = true;
          this.views.push(this.viewsOptional.find(view => view.id == SchoolAdminView.PURCHASES))
        }
      } else {
        this.isPaymentModuleEnabled = false;
        this.views = this.views.filter(view => view.id !== SchoolAdminView.PURCHASES);
      }
    }

    this.verifyChecklistCompletion();
  }

  is24HrWindow(dateStr) {
    const confirmDate = new Date(dateStr);
    const timeStamp = Math.round(new Date().getTime() / 1000);
    const timeStampYesterday = timeStamp - (24 * 3600);
    return confirmDate.getTime() >= new Date(timeStampYesterday * 1000).getTime();
  }

  studentRecordHasError(){
    if(this.currentClassFilter == ClassFilterId.G9){
      if(this.g9StudentRecordHasError == undefined){
        this.g9StudentRecordHasError = this.g9DemoData.schoolAdminStudents.list.find(student=> (student.errMsg ? JSON.parse(student.errMsg).length > 0 : false) || student.SDC_conflict_g9 != undefined ) != undefined
      }
      return this.g9StudentRecordHasError
    }
    else if(this.currentClassFilter == ClassFilterId.OSSLT){
      if(this.g10StudentRecordHasError == undefined){
        this.g10StudentRecordHasError = this.g9DemoData.schoolAdminStudents.list.find(student=> (student["_g10_errMsg"] ? JSON.parse(student["_g10_errMsg"]).length > 0 : false) || student.SDC_conflict_g10 != undefined) != undefined
      }
      return this.g10StudentRecordHasError
    }
  }

  getDashboardSchoolAdminSlug(){
    if (this.whiteLabelService.isNBED() || this.whiteLabelService.isMBED()) {
      return "sa_dashboard_school_admin_nbed";
    } else if (this.whiteLabelService.isABED()) {
      return "abed_schoolad";
    } else {
      return "sa_dashboard_school_admin";
    }
  }

  getAdminViews = () =>  {
    let views;

    // TODO: DB-DATA-MODEL
    if(this.whiteLabelService.isNBED() || this.whiteLabelService.isMBED()){
      views = NBED_SCHOOL_ADMIN_VIEWS
    }
    else if(this.whiteLabelService.isSchlBoards()){ //Temp Fix for abed-fall24. uncomment school-boards from whitelabel if working on Multiplex.
      views = SCHL_BOARDS_SCHOOL_ADMIN_VIEWS
    }
    else if(this.whiteLabelService.isABED()){
      views = ABED_SCHOOL_ADMIN_VIEWS.filter(view => !view.isBlocked)
    }
    else {
      views = SCHOOL_ADMIN_VIEWS
    }

    return views.filter(view => {
      if (view.whiteLabelContextFlag){
        return !!this.whiteLabelService.getSiteFlag(view.whiteLabelContextFlag)
      }
      return true;
    })
  }

  navigateToSessions(): void {
    this.switchViews(this.getViewRoute(SchoolAdminView.SESSIONS))
  }

  navigateToManageMySchool(){
    this.switchViews(this.getBaseRoute() + '/'+SchoolAdminView.SCHOOL_ACCESS_PANEL)
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }

  showResponsibilityAgreement() {
    const showRespAgreementForSlugs = ['IS_EQAO']
    const isVisible = showRespAgreementForSlugs?.some(slug => this.whiteLabelService.getSiteFlag(slug));
    return !this.isPrivateSchool && isVisible;
  }

  getSchoolGroupId = () => this.g9DemoData.getSchoolGroupId()

  async haveUserAgreedOnConfidentiality(){
    if (this.whiteLabelService.getSiteFlag('IS_SCH_CONFAGR')){
      const agreementStatus = await this.g9DemoData.haveUserAgreedOnConfidentiality(this.isABED());
      console.log("agreementStatus: ", agreementStatus);
      if(agreementStatus.userHaveAccepted == false){
        let confidentialityName = '';
        let confidentialityDate = formatDate(new Date(), 'yyyy-MM-dd', 'en-US')
        const config = {
          confidentialityChecks: this.g9DemoData.getConfidentialityChecks(),
          confidentialityName,
          confidentialityDate
        };
        this.pageModal.newModal({
          type: ConfidentialityAgreement.HAVE_NOT_AGREED,
          config,
          isProceedOnly: true,
          confirmationCaption: "Send Attestations",
          finish: (e) => this.acceptConfidentialityAgreement(e)
        });
      }
    }
  }

  async acceptConfidentialityAgreement(config){
    try{
      await this.g9DemoData.acceptConfidentialityAgreement(this.isABED(), config.confidentialityName, config.confidentialityDate)
      await this.haveUserAgreedOnConfidentiality();
    }catch(e){
      alert("error");
    }
  }

  disableConfidentiality(){
    return this.g9DemoData.disableConfidentiality();
  }

  getTechReadyDoneSlug(){
    return this.isABED() ? 'abed_txt_tech_readi_done' : 'txt_tech_readi_done';
  }
  getGuideSlug() {
    return this.isABED() ? 'abed_tech_guides_public' : 'abed_tech_guides_logged_in'
  }
  getClickText() {
    return "abed_login_click_txt"
  }

  cmc() { return this.cModal().config; }

  isABED(){
    return this.whiteLabelService.isABED();
  }


}
