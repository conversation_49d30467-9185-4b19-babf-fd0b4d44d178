import { PERUSAL_END_TYPES, PERUSAL_TYPES } from './perusal';

export enum StuAsmtStatus {
  REGISTERED = 'REGISTERED',
  SUBMITTED = 'SUBMITTED',
  ABSENT = 'ABSENT',
  EXCUSED = 'EXCUSED',
  TRANSFERRED = 'TRANSFERRED',
}

export type IStudent = {
  uid:number,
  first_name :string,
  last_name :string,
  lang :string,
  dob:string,
  student_gov_id:string,
  asmt_status?: {
    [asmt_type_slug:string]: {
      status_code?: StuAsmtStatus,
      is_expected: boolean,
      is_in_ts: boolean,
      is_submitted: boolean,
      is_absent: boolean,
      is_excused: boolean,
      is_transferred?: boolean,
      is_unexpected_submission?: boolean,
      report_num: number,
    }
  }
  isAtLeastOneStatus?: boolean,
}

type IAsmtComponent = {
  id:number,
  asmt_type_slug:string, 
  caption_short:string, 
  meta:string, 
  isFrOnly?:boolean,// todo:DB_DATA_MODEL 
  is_perusal_allow: boolean,
  perusal_configs: any,
  perusal_date_start: string,
  perusal_date_end: string,
  perusal_type: PERUSAL_TYPES,
  perusal_end_type: PERUSAL_END_TYPES,
  perusal_offset_hours: number,
  perusal_duration_hours: number,
  ts_date_time_start: string,
  first_ta_started_on: string,
  latest_ts_date_time_start: string,
  latest_first_ta_started_on: string,
  tw_date_start: string,
  tw_date_end: string,
  test_date_end: string,
  is_tw_current: boolean,
  test_design_id: number,
  sample_test_session_id?: number,
  source_item_set_id: number
}

export type IReportIssue = {
  id: number,
  description: string,
}

export type ISchlTwSignOff = {
  overall_issues_no: boolean,
  overall_issues_yes: boolean,
  overall_issues_notes: string,
  is_submitted?: boolean,
}

export class TwStatement {

  constructor (
    public schl_group_id: number,
    public test_window_id: number,
    public asmtComponents:IAsmtComponent[],
    public students:IStudent[],
    public flaggedCaseRecords:{uid: number, twtar_type_slug: string}[],
    public schl_tw_sign_off:{id:number, meta:ISchlTwSignOff},
  ){
    this.refreshStats();
    console.log('new TwStatement:: flaggedCaseRecords', flaggedCaseRecords.length)
  }

  public isSaving:boolean = false
  public studentWithStatusRef:Map<number, boolean> = new Map()

  public statRows = [
    {prop:'n_reg', caption: 'abed_sa_pk_registrations'},
    {prop:'n_sub', caption: 'abed_sa_pk_submissions'},
    {prop:'n_abs', caption: 'abed_sa_pk_absences'},
    {prop:'n_exc', caption: 'abed_sa_pk_excused'},
    {prop:'n_trn', caption: 'abed_sa_pk_transferred'},
    {prop:'n_gap', caption: 'abed_sa_pk_to_clarify'},
  ]

  public assessmentComponentStats:{
    [asmt_type_slug:string]: {
      n_reg: number,
      n_sub: number,
      n_abs: number,
      n_exc: number,
      n_trn: number,
      n_gap: number,
    }
  } = {};

  refreshStats(){
    this.assessmentComponentStats = {};
    for (let ac of this.asmtComponents){
      let n_reg = 0;
      let n_sub = 0;
      let n_abs = 0;
      let n_exc = 0;
      let n_trn = 0;
      let n_gap = 0;
      for (let student of this.students){
        const asmt_status = student.asmt_status[ac.asmt_type_slug];
        let isAtLeastOneStatus = false;
        if (asmt_status){
          if (asmt_status.is_expected || asmt_status.is_unexpected_submission){
            isAtLeastOneStatus = true;
            n_reg ++;
            if (asmt_status.is_absent){
              asmt_status.status_code = StuAsmtStatus.ABSENT;
              n_abs ++;
            }
            else if (asmt_status.is_excused) {
              asmt_status.status_code = StuAsmtStatus.EXCUSED;
              n_exc ++;
            }
            else if (asmt_status.is_transferred) {
              asmt_status.status_code = StuAsmtStatus.TRANSFERRED;
              n_trn ++;
            }
            else if (asmt_status.is_submitted){
              asmt_status.status_code = StuAsmtStatus.SUBMITTED;
              n_sub ++;
            }
            else {
              asmt_status.status_code = StuAsmtStatus.REGISTERED;
              n_gap ++;
            }
          }
        }
        if (isAtLeastOneStatus){
          this.studentWithStatusRef.set(+student.uid, true)
        }
      }
      this.assessmentComponentStats[ac.asmt_type_slug] = {
        n_reg,
        n_sub,
        n_abs,
        n_exc,
        n_trn,
        n_gap,
      }
    }
  }
  
}
