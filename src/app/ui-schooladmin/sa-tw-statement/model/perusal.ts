import * as moment from 'moment-timezone';
import { LangService } from 'src/app/core/lang.service';
import { renderDateRangeLocal } from  'src/app/core/util/date';

export enum PERUSAL_TYPES {
  STATIC_TIME = "STATIC_TIME",
  RELATIVE_SESSION_START = "RELATIVE_SESSION_START",
  RELATIVE_FIRST_STUDENT_START = "RELATIVE_FIRST_STUDENT_START",
  NO_LIMIT = "NO_LIMIT"
}

export enum PERUSAL_END_TYPES {
  NO_LIMIT = "NO_LIMIT",
  ASSESSMENT_END = "ASSESSMENT_END",
  DURATION_HOURS = "DURATION_HOURS",
  STATIC_TIME = "STATIC_TIME"
}

export interface ISessionInfo {
  ts_date_time_start: string,
  first_ta_started_on: string,
  latest_ts_date_time_start?: string,
  latest_first_ta_started_on?: string,
  test_date_end: string,
  tw_date_start: string, 
  tw_date_end: string
}

export interface IPerusalConfig {
  is_available_admin?: boolean,
  is_available_teacher?: boolean,
  is_summary_comment?: boolean,
  is_date_unstrict?: boolean
}

export interface IPerusalSettings {
  is_perusal_allow: boolean,
  perusal_configs: IPerusalConfig,
  perusal_type: PERUSAL_TYPES,
  perusal_end_type: PERUSAL_END_TYPES,
  perusal_offset_hours: number,
  perusal_duration_hours: number,
  perusal_date_start: string, 
  perusal_date_end: string
}

export function isPerusalAvailableForRole (perusalSettings: IPerusalSettings, sessionInfo: ISessionInfo, is_tw_current: boolean, isTeacher: boolean = false) : boolean {
  const {is_perusal_allow, perusal_configs, perusal_type, perusal_offset_hours, perusal_duration_hours, perusal_date_start, perusal_date_end} = perusalSettings;

  // If perusals are not allowed, they are always disabled
  if (!is_perusal_allow || !is_tw_current) {
    return false
  }

  const role_setting = isTeacher ? 'is_available_teacher' : 'is_available_admin'


  // If date is restricted, find actual start date (absolute or relative)
  const {startDate, endDate} = calculatePerusalDateRange(perusalSettings, sessionInfo, isTeacher)

  if (!startDate || !endDate){
    return false;
  }

  // check if current date is within the range
  const now = moment();
  const isNowInRange = now.isBetween(startDate, endDate);
  if(isNowInRange) {
    return !!perusal_configs[role_setting]
  }
  return false;
}

export function calculatePerusalDateRange(perusalSettings: IPerusalSettings,  sessionInfo: ISessionInfo, isTeacher: boolean = false){
  const {ts_date_time_start, first_ta_started_on, latest_ts_date_time_start, latest_first_ta_started_on, test_date_end, tw_date_start, tw_date_end} = sessionInfo;
  const {perusal_configs, perusal_type, perusal_end_type, perusal_offset_hours, perusal_duration_hours, perusal_date_start, perusal_date_end} = perusalSettings;

  let startDate, endDate, startDateUseForEnd;

  switch(perusal_type){
    case PERUSAL_TYPES.STATIC_TIME:
      if (perusal_date_start){
        startDate = moment(perusal_date_start)
      }
      break;
    case PERUSAL_TYPES.RELATIVE_SESSION_START:
      if (ts_date_time_start && perusal_offset_hours !== undefined){
        startDate = moment(ts_date_time_start).add(perusal_offset_hours, 'hours');
        startDateUseForEnd = latest_ts_date_time_start ? moment(latest_ts_date_time_start) : startDate;
      }
      break;
    case PERUSAL_TYPES.RELATIVE_FIRST_STUDENT_START:
      if (first_ta_started_on && perusal_offset_hours !== undefined){
        startDate = moment(first_ta_started_on).add(perusal_offset_hours, 'hours');
        startDateUseForEnd = latest_first_ta_started_on ? moment(latest_first_ta_started_on) : startDate;
      }
      break;
    case PERUSAL_TYPES.NO_LIMIT:
      startDate = moment(tw_date_start);
      break;
    default:
      // If date is not strict on assessment level, allow even if any settings are missing
      if(perusal_configs?.is_date_unstrict) {
        startDate = moment(tw_date_start);
      };
  }

  switch (perusal_end_type){
    case PERUSAL_END_TYPES.STATIC_TIME:
      if (perusal_date_end){
        endDate = moment(perusal_date_end)
        break;
      }
    case PERUSAL_END_TYPES.DURATION_HOURS:
      if (perusal_duration_hours){
        const start = startDateUseForEnd || startDate;
        if (start){
          endDate = start.add(perusal_duration_hours, 'hours');
          break;
        }
      }
    case PERUSAL_END_TYPES.ASSESSMENT_END:
      if (test_date_end){
        endDate = moment(test_date_end);
        break;
      }
    case PERUSAL_END_TYPES.NO_LIMIT:
    default:
      endDate = moment(tw_date_end);
  }

  return {startDate, endDate}
}


export function renderPerusalDate(perusalSettings: IPerusalSettings,  sessionInfo: ISessionInfo, lang: LangService, isTeacher: boolean = false) {
  const {is_perusal_allow, perusal_configs, perusal_type, perusal_end_type, perusal_offset_hours, perusal_duration_hours, perusal_date_start, perusal_date_end} = perusalSettings;

  const role_setting = isTeacher ? 'is_available_teacher' : 'is_available_admin';
  if (!is_perusal_allow || !perusal_configs[role_setting]){
    return;
  }

  const {startDate, endDate} = calculatePerusalDateRange(perusalSettings, sessionInfo)

  let startOvrdString, endOvrdString
  if (!startDate) {
    if (perusal_type == PERUSAL_TYPES.RELATIVE_SESSION_START){
      startOvrdString = `${perusal_offset_hours || 0} hours after the start of the session`
    }
    if (perusal_type == PERUSAL_TYPES.RELATIVE_FIRST_STUDENT_START){
      startOvrdString = `${perusal_offset_hours || 0} hours after the first students starts writing`
    }
    if ([PERUSAL_TYPES.RELATIVE_FIRST_STUDENT_START, PERUSAL_TYPES.RELATIVE_SESSION_START].includes(perusal_type) && perusal_end_type == PERUSAL_END_TYPES.DURATION_HOURS && perusal_duration_hours){
      endOvrdString = `${perusal_duration_hours} hour later`
    }
  }

  return renderDateRangeLocal(startDate, endDate, lang, startOvrdString, endOvrdString)
}