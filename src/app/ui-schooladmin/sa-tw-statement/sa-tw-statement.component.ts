import { TW_TYPE_SLUGS_WITH_REG } from './model/temp-const';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LangService } from 'src/app/core/lang.service';
import { StuAsmtStatus, IStudent, TwStatement, ISchlTwSignOff, IReportIssue } from './model/tw-statement';
import { MySchoolService, ClassFilterId } from '../my-school.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { AuthService, getFrontendDomain } from '../../api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { DomSanitizer } from '@angular/platform-browser';
import { LoginGuardService } from '../../api/login-guard.service';
import { mtz } from 'src/app/core/util/moment';
import { renderDateRangeLocal } from  'src/app/core/util/date';
import { PERUSAL_TYPES, isPerusalAvailableForRole, calculatePerusalDateRange, PERUSAL_END_TYPES, renderPerusalDate} from './model/perusal'

@Component({
  selector: 'sa-tw-statement',
  templateUrl: './sa-tw-statement.component.html',
  styleUrls: ['./sa-tw-statement.component.scss']
})
export class SaTwStatementComponent implements OnInit {

  @Output() onSetClassFilter = new EventEmitter();

  ClassFilterId = ClassFilterId;
  StuAsmtStatus = StuAsmtStatus;
  currentClassFilter;
  currentTestWindow;
  _twStatement:TwStatement;
  isShowingUnregisteredStudents = false;
  isShowingOnlyFlaggedStudents = false;
  selectedAsmtTypeSlug:string = '__OVERVIEW__';
  includeFr: boolean = false;

  constructor(
    public schoolService:MySchoolService,
    public lang: LangService,
    public auth: AuthService,
    private route:ActivatedRoute,
    private routes: RoutesService,
    private whitelabel: WhitelabelService,
    public mySchool: MySchoolService,
    private sanitizer: DomSanitizer,
    private loginGuard: LoginGuardService,
    private router: Router,
  ) {}

  ngOnInit(): void {
    
  }

  twStatement(){
    return this._twStatement;
  }

  decideDataToShow(){
    if (this.twStatement().students && this.currentClassFilter){
      if (this.twStatement().students.length){
        return true;
      }
    }
    return false;
  }

  stickyStudents = new Map()
  selectAsmtTypeSlug(asmtTypeSlug:string){
    this.selectedAsmtTypeSlug = asmtTypeSlug;
    this.initMetaForTypeSlug()
    this.stickyStudents = new Map();
    for (let student of this.twStatement().students){
      try { 
        if (this.isStudentFlaggedFor(student, asmtTypeSlug)){
          this.stickyStudents.set(student.uid, true);
        }
      }
      catch(e){
        console.error('Could not set sticky students', e)
      }
    }
    // console.log('overallAsmtProp', this.overallAsmtProps(asmtTypeSlug) )
  }

  onSelectedAsmtTypeSlugChange(){
    this.selectAsmtTypeSlug(this.selectedAsmtTypeSlug);
  }

  isAsmtSubmitted(asmtTypeSlug:string){
    const asmtProps = this.overallAsmtProps(asmtTypeSlug);
    if (asmtProps){
      return this.overallAsmtProps(asmtTypeSlug).is_submitted
    }
    return false;
  }

  hasRegistrationOrAttempts(asmtTypeSlug:string){
    const stats = this.twStatement().assessmentComponentStats[asmtTypeSlug]
    return (stats.n_reg > 0 || stats.n_sub > 0 )
  }

  cOAsmtProps(){
    return this.overallAsmtProps(this.selectedAsmtTypeSlug)
  }
  
  overallAsmtProps(asmtTypeSlug:string){
    return this._twStatement.schl_tw_sign_off.meta[asmtTypeSlug]
  }

  initMetaForTypeSlug(){
    if (!this._twStatement.schl_tw_sign_off.meta[this.selectedAsmtTypeSlug]){
      this._twStatement.schl_tw_sign_off.meta[this.selectedAsmtTypeSlug] = {
        studentMeta: {}
      };
    }
    const meta = this._twStatement.schl_tw_sign_off.meta[this.selectedAsmtTypeSlug]
    for (let student of this.twStatement().students){
      if (!meta.studentMeta[student.uid]){
        meta.studentMeta[student.uid] = {};
      }
    }
  }

  getStudentMeta(student: IStudent, asmtTypeSlug?:string){
    asmtTypeSlug = asmtTypeSlug || this.selectedAsmtTypeSlug
    const meta = this._twStatement.schl_tw_sign_off.meta[asmtTypeSlug]
    if (meta){
      return meta.studentMeta[student.uid]
    }
    else {
      return {}
    }
  }

  isCurrentAsmtSlug(asmtTypeSlug:string){
    return asmtTypeSlug == this.selectedAsmtTypeSlug;
  }

  decideOverviewDisplay(){
    return this.selectedAsmtTypeSlug == '__OVERVIEW__';
  }

  twsForm():ISchlTwSignOff {
    return this._twStatement.schl_tw_sign_off.meta
  }

  async saveSchlTwSignoff(isSubmitting:boolean=false){
    this.cOAsmtProps().is_submitted = !!isSubmitting;
    this.cOAsmtProps().submitted_by = this.auth.myUID();
    for (const [student, studentValue] of Object.entries(this.cOAsmtProps().studentMeta)) {
        if (!studentValue.hasOwnProperty('is_excused')) {
          // Default value for is_excused to determine if it is submitted after is_excused implementation.
          studentValue['is_excused'] = false;
        }
    }

    const {id} = await this.mySchool.saveSchlTwSignoff(this._twStatement);
    if (!this._twStatement.schl_tw_sign_off.id){
      this._twStatement.schl_tw_sign_off.id = id
    }
    this.loginGuard.quickPopup('Principal Statement saved!'); // todo:TRANS
  }

  async silentSave(){
    const {id} = await this.mySchool.saveSchlTwSignoff(this._twStatement);
    if (!this._twStatement.schl_tw_sign_off.id){
      this._twStatement.schl_tw_sign_off.id = id
    }
  }

  setClassFilter(filterId){
    this.currentClassFilter = filterId
    this.onSetClassFilter.emit(filterId);
  }
  async setTestWindowFilter(tw){
    this.currentTestWindow = tw;
    if (tw){
      this._twStatement = await this.mySchool.initTwStatement(tw.id);
      this.isShowingOnlyFlaggedStudents = this._twStatement.flaggedCaseRecords.length > 0
      const asmtComponentSlugs = this._twStatement.asmtComponents.map(component => component.asmt_type_slug);
      if(!asmtComponentSlugs.includes(this.selectedAsmtTypeSlug)){
        // If assessment not exist after changing semester, clear view selected
        this.decideOverviewDisplay()
      }
      else{
        // Otherwise, confirm the assessment exist in the meta
        this.initMetaForTypeSlug()
      }
      // console.log('tw statement', this._twStatement)
    }
  }

  isExclusionsIncluded(){
    return !this.isRegistrationBasedWindow();
  }

  showAssessmentComponent(component:any){
    if (this.isShowingOnlyFlaggedStudents){
      if (this.assessmentComponentHasFlaggedStudents(component)){
        return true;
      }
      return false;
    }
    return true;
  }

  assessmentComponentHasFlaggedStudents(component:any){
    return (this.numFlaggedStudentsFor(component.asmt_type_slug) > 0)
  }
  
  isStudentFlagged(student: IStudent){
    return this.isStudentFlaggedFor(student, this.selectedAsmtTypeSlug)
  }

  isStudentFlaggedFor(student: IStudent, asmtTypeSlug:string){
    const uidFlagRef = this.getFlagStudentRef(asmtTypeSlug);
    if (uidFlagRef.get(student.uid)){
      const studentMeta = this.getStudentMeta(student, asmtTypeSlug)
      if (studentMeta){
        if (studentMeta['is_absent'] || studentMeta['is_excused'] || studentMeta['is_transferred'] || studentMeta['is_anomaly']){
          return false;
        }
      }
      return true;
    }
    return false;
  }

  getFlagStudentRef(asmtTypeSlug:string){
    const ref = new Map();
    for (let record of this.twStatement().flaggedCaseRecords){
      if (record.twtar_type_slug == asmtTypeSlug){
        ref.set(record.uid, true);
      }
    }
    return ref
  }

  numFlaggedStudentsFor(asmtTypeSlug:string){
    let n = 0;
    for (let student of this.twStatement().students){
      if (this.isStudentFlaggedFor(student, asmtTypeSlug)){
        n ++;
      }
    }
    return n
  }

  showStat(stat){
    if (stat.prop == 'n_reg'){
      if (!this.isRegistrationBasedWindow()){
        return false
      }
    }
    if (stat.prop == 'n_exc'){
      if (!this.isExclusionsIncluded()){
        return false;
      }
    }
    return true
  }

  // Show when not submitted or submitted and is_excused = true
  excusedImplemented(meta, asmt_type_slug) {
    if(!meta){
      return true;
    }
    for (const [asmtKey, asmt] of Object.entries(meta)) {
      if(asmtKey==asmt_type_slug){
        // If not is_submitted is found, show excused
        if (!asmt['is_submitted']) {
          return true;
        }
        else  { 
          // If any student has is_excused, show excused
          for (const [studentKey, student] of Object.entries(asmt['studentMeta'])) {
            if (student.hasOwnProperty('is_excused')) { 
              return true;
            }
          }
          // is_submitted + not is_excused, old submitted assessment, don't show excused
          return false;
        }
      }
    }
    // If object doesn't have the assesment, mean it never been submitted, show excused
    return true;
  }
  enabledExcused(){
    return true
    // if(this.cOAsmtProps()){
    //   if (!this.cOAsmtProps()['is_submitted']) {
    //     return true;
    //   }
    //   else  { 
    //     // is_submitted + not is_excused, old submitted assessment, don't show excused
    //     return false;
    //   }
    // }
    // return true;
  }
  checkStudent(student:IStudent){
    console.log(this._twStatement.studentWithStatusRef.get(+student.uid))
  }

  isRegistrationBasedWindow(){
    if (TW_TYPE_SLUGS_WITH_REG.includes(this.currentTestWindow.type_slug)){
      return true
    }
    return false;
  }

  isAtLeastOneStatus(student:IStudent, asmt_type_slug?:string){
    let isAtLeastOneStatus 
    if (asmt_type_slug){
      const hasStatusCode = !! student?.asmt_status[asmt_type_slug]?.status_code
      isAtLeastOneStatus = hasStatusCode
    }
    else {
      isAtLeastOneStatus = this._twStatement.studentWithStatusRef.get(+student.uid)
    }
    return isAtLeastOneStatus
  }

  showStudent(student:IStudent, asmt_type_slug?:string){
    if (this.isShowingOnlyFlaggedStudents){
      return this.isStudentFlaggedFor(student, asmt_type_slug) || this.stickyStudents.get(student.uid)
    }
    const isAtLeastOneStatus = this.isAtLeastOneStatus(student, asmt_type_slug);
    return isAtLeastOneStatus || (this.isShowingUnregisteredStudents || !this.isRegistrationBasedWindow())
  }

  async markAsAbsent(student: IStudent, asmt_type_slug:string){
    const {test_window_id, schl_group_id} = this.twStatement();
    await this.auth.apiPatch(
      'public/school-admin/student-tw/absence',
      student.uid,
      {},
      { 
        query: {
          test_window_id, 
          asmt_type_slug,
          schl_group_id
        }
      }
    )
    student.asmt_status[asmt_type_slug].is_absent = true;
    this.twStatement().refreshStats();
  }
  async unmarkAsAbsent(student: IStudent, asmt_type_slug:string){
    const {test_window_id, schl_group_id} = this.twStatement();
    await this.auth.apiRemove(
      'public/school-admin/student-tw/absence',
      student.uid,
      { 
        query: {
          test_window_id, 
          asmt_type_slug,
          schl_group_id
        }
      }
    )
    student.asmt_status[asmt_type_slug].is_absent = false;
    this.twStatement().refreshStats();
  }

  isExportingPrelim:boolean;
  async printReportPdfs() {
    try { 

      this.isExportingPrelim = true;

      const data = await this.auth.apiGet('/public/school-admin/reports-prelim', this.currentTestWindow.id, {
        query: {
          schl_group_id: this.mySchool.getCurrentSchoolGroupId(),
          lang: this.includeFr ? 'fr' : 'en'
        }
      })

      if (data.pdfBase64) {
        const byteCharacters = atob(data.pdfBase64);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
  
        // Create a link element and trigger download
        const link = document.createElement('a');
        link.href = url;
        link.download = 'preliminary-results.pdf';
        link.click();
  
        // Cleanup
        URL.revokeObjectURL(url);

        this.isExportingPrelim = false;

      } 
      else {
        console.error('PDF data not found');
        this.isExportingPrelim = false;
      }
    }
    catch (error) {
      console.error('Error fetching PDFs:', error);
      alert('Error when fetching preliminary results.')
      this.isExportingPrelim = false;
    }
  }
  issueDisplayMap : Map<number, Map<string, IReportIssue[]>> = new Map();
  showStudentIssues(student: IStudent, asmt_type_slug: string){
    const {test_window_id, schl_group_id} = this.twStatement();
    this.auth.apiFind('public/school-admin/tw-statement',{
      query: {
          action: 'reported-issue',
          uid: student.uid,
          schl_group_id: schl_group_id,
          test_window_id: test_window_id,
          asmt_type_slug: asmt_type_slug
      }
    }).then((res)=>{
      if(res.length > 0){
        if(!this.issueDisplayMap.get(student.uid)){
          this.issueDisplayMap.set(student.uid, new Map<string, IReportIssue[]>());
        }
        if(!this.issueDisplayMap.get(student.uid).get(asmt_type_slug)){
          this.issueDisplayMap.get(student.uid).set(asmt_type_slug, []);
        }
        const studentIssues = this.issueDisplayMap.get(student.uid).get(asmt_type_slug);
        studentIssues.push(...res);
      }
    }).catch((e)=>{
      this.loginGuard.quickPopup(e.message);
    })
  }
  
  getStudentIssue(student: IStudent, asmt_type_slug: string){
    if(!this.issueDisplayMap.get(student.uid) || !this.issueDisplayMap.get(student.uid).get(asmt_type_slug)){
      return [];
    }
    return this.issueDisplayMap.get(student.uid).get(asmt_type_slug);
  }

  clearIssues(student: IStudent, asmt_type_slug: string){
    this.issueDisplayMap.get(student.uid).set(asmt_type_slug, []);
  }

  get currentAsmtMap() {
    return this.twStatement().asmtComponents.find(asmt => asmt.asmt_type_slug == this.selectedAsmtTypeSlug)
  }

  get isPerusalAvailable() {
    const {is_tw_current, is_perusal_allow, perusal_configs, perusal_type, perusal_offset_hours, perusal_duration_hours, perusal_date_start, perusal_date_end, latest_ts_date_time_start, latest_first_ta_started_on, perusal_end_type, test_date_end, tw_date_start, tw_date_end} = this.currentAsmtMap
    const perusalSettings = {is_perusal_allow, perusal_configs, perusal_type, perusal_end_type, perusal_offset_hours, perusal_duration_hours, perusal_date_start, perusal_date_end}
    // Check whether perusal is enabled in at least one session by using the latest session start time or first attempt start time
    const sessionInfo = {ts_date_time_start: latest_ts_date_time_start, first_ta_started_on: latest_first_ta_started_on, test_date_end, tw_date_start, tw_date_end}
    return isPerusalAvailableForRole(perusalSettings, sessionInfo, !!is_tw_current, false)
  }

  get renderPerusalDate() {
    const {is_perusal_allow, perusal_configs, perusal_type, perusal_offset_hours, perusal_duration_hours, perusal_date_start, perusal_date_end, ts_date_time_start, first_ta_started_on, latest_ts_date_time_start, latest_first_ta_started_on, perusal_end_type, test_date_end, tw_date_start, tw_date_end} = this.currentAsmtMap
    const perusalSettings = {is_perusal_allow, perusal_configs, perusal_type, perusal_end_type, perusal_offset_hours, perusal_duration_hours, perusal_date_start, perusal_date_end}
    
    // Show full range where perusal is available for at least one session
    // If relative to session/first attempt start - use session with the earliest time for start, but session with the latest time to count the offset from
    const sessionInfo = {ts_date_time_start, first_ta_started_on, latest_ts_date_time_start, latest_first_ta_started_on, test_date_end, tw_date_start, tw_date_end}
    
    return renderPerusalDate(perusalSettings, sessionInfo, this.lang)
  }
}