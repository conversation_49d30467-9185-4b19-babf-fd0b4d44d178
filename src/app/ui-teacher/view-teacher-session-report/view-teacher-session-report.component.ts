import { ILocalMarkingFramework, tempGetLocalMarkingFramework } from './temp-frameworks';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, ParamMap, Router } from '@angular/router';
import { AuthService } from 'src/app/api/auth.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { ClassroomsService } from 'src/app/core/classrooms.service';
import { downloadStr } from 'src/app/core/download-string';
import { LangService } from 'src/app/core/lang.service';
import { IQuestionConfig } from 'src/app/ui-testrunner/models';
import { IItemAttemptMarkingData, IItemMarkingData, ILocalMarkingData, IScoreOptionMinimal, LocalScoringSaveAgg } from './model/agg';
import { mtz } from 'src/app/core/util/moment';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { AssessmentSlugs } from '../view-invigilate/types';
import { hasPrintBlocks } from 'src/app/ui-testrunner/services/util';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import {pdfRunner} from './../../ui-testrunner/element-render-upload/element-render-upload.component'
import { isPerusalAvailableForRole, renderPerusalDate  } from './../../ui-schooladmin/sa-tw-statement/model/perusal'

import { 
  QuestionId,
  IEntryIdRemapping,
  IEntryIdRemappingConfig,
  ISessionReportQuestion,
  IQuestion,
  ISessionReportStudent,
  IStudent,
  ISessionReportTestDesign,
  ISessionReportTestForm,
  ISessionReport,
  IMbap,
  IResponseGroup,
  IReportResponseRecord,
  IItemResponse,
  IEntryIdRemappingRef,
} from './model/types';

const IS_CR_MODE = true;

const MAX_FREQ_BAR_WIDTH_EM = 8;

export type SummaryReportData = Record<string, {
  'N/A': number,
  'At Risk': number,
  'Not At Risk': number,
  details: {
    students: Record<number, number | null>,
    cutOff: number,
    weight: number,
    description?: string,
  }
}>

enum ViewMode {
  ALL = 'ALL',
  QUESTION = 'QUESTION',
  SUMMARY = 'SUMMARY',
  INDIVIDUAL = 'INDIVIDUAL',
}

@Component({
  selector: 'view-teacher-session-report',
  templateUrl: './view-teacher-session-report.component.html',
  styleUrls: ['./view-teacher-session-report.component.scss']
})
export class ViewTeacherSessionReportComponent implements OnInit {
  
  public ViewMode = ViewMode;
  public IS_CR_MODE = IS_CR_MODE;
  public isExportEnabled = false // todo: make export dependent on response type
  public isBulkPrintEnabled = false;
  public isLocalScoreEnabled = false;
  public isPerusalEnabled = false;
  public perusalDate: string = '';
  public isHidePartialScore = false;

  public test_session_id:number
  public sc_id:number
  public test_design_id:number
  public students:IStudent[]
  public selectedStudent: IStudent;
  public questions:IQuestion[]

  public isLoading:boolean = true;
  public isNoResponses = false; // this gets set to true if it loads and finds responses
  public isPrintView = false;
  public isPrintViewSingle = false; // toggled through the UI
  public isPrintViewMulti = false; // toggled through the UI

  // public responses:any[]
  public currentViewMode:ViewMode = ViewMode.ALL;
  public currentQuestionId:number;
  public questionRef:Map<QuestionId, IQuestion>  
  public sectionRef:Map<any, any>  
  public studentRef:Map<number, IStudent> 
  public currentResponseState:any;
  public currentResponseKey:string | null;
  public isResettingQuestion:boolean;
  public pageModal: PageModalController;

  localMarkingData:ILocalMarkingData = {
    numItemsMarked: 0,
    itemSummary: {},
    itemMarks: {},
    studentScores:{},
  }
  isScoring = false 
  isScoreEntrySession = false;
  totalStudentScore = 0;
  totalCutScore = 0;
  summaryReport:SummaryReportData;
  localMarkingQuestions: IQuestion[];
  sessionReport:ISessionReport;
  
  constructor(
    private auth:AuthService,
    private route: ActivatedRoute,
    private router: Router,
    private loginGuard: LoginGuardService,
    private classroomService: ClassroomsService,
    private lang: LangService,
    private whiteLabel: WhitelabelService,
    private pageModalService: PageModalService,
    private sanitizer: DomSanitizer
  ) { }


  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      this.processParams(params)
    });
    this.pageModal = this.pageModalService.defineNewPageModal();
  }

  currentLang(){
    return this.lang.c()
  }

  sendResponseToPrint(){
    this.isPrintView = true
    this.isPrintViewSingle = true
  }
  openPrintMulti(){
    this.isPrintView = true
    this.isPrintViewMulti = true
  }
  exitPrintMulti(){
    this.isPrintView = false
    this.isPrintViewMulti = false
  }
  exitPrintSingle(){
    this.isPrintView = false
    this.isPrintViewSingle = false
  }

  isStudentNames(){
    return !this.classroomService.isNamesHidden
  }
  toggleStudentNames(){
    this.classroomService.isNamesHidden = !this.classroomService.isNamesHidden;
  }

  questionSelectorModal:{questions:{item_id:number, name:string}[]}
  showQuestionModal(){

  }

  isCurrentQuestionPrintMode(){
    return this.isQuestionPrintMode(this.getCurrentQuestion())
  }
  isQuestionPrintMode(q:IQuestion){
    return q?.isPrintMode
  }

  async processParams(params:ParamMap){
    if (params){
      const formId = +(params.get('formId') || -1)
      if(formId && formId !== -1){
        this.selectedTestDesign = formId;
      }
      const test_session_id = +(params.get('asmtSessionId') || -1);
      if (this.test_session_id != test_session_id){
        this.sc_id = +(params.get('classroomId')|| -1);
        this.test_session_id = test_session_id;
        await this.loadResults(this.test_session_id);
      }
      const question_id = +(params.get('questionId')|| -1);
      if (question_id && question_id !== -1){
        this.loadQuestion(question_id)
      }
      else {
        //we don't need this anymore as it was inteded to effect pwr assessments only and it did effect an operational assessment
        // if (this.questions.length == 1){
        //   this.viewQuestion(+this.questions[0].id)
        // }
        // else {
        this.renderTable()
        // }
      }
    }
  }

  getResponseStudentName(response:IReportResponseRecord){
    const student = this.studentRef.get(response.uid);
    if (student){
      // if(student.is_submitted){
      return student.first_name +' ' + student.last_name
      // }
      // else{
      //   return "Non Submitted Student"
      // }
    }
  }
  getResponseStuGovId(response:IReportResponseRecord){
    const student = this.studentRef.get(response.uid);
    if (student){
      return student.student_gov_id
    }
  }

  async loadResults(test_session_id: number){
    this.isLoading = true;
    this.isNoResponses = false;
    if (this.classroomService.currentTestSessionReport && this.classroomService.currentTestSessionReport.test_session_id == test_session_id){
      this.sessionReport = this.classroomService.currentTestSessionReport.payload;
    }
    else {
      try {
        this.sessionReport = await this.auth.apiGet('/public/educator/session-report', test_session_id, {})
        
        this.classroomService.currentTestSessionReport = {
          test_session_id,
          payload: this.sessionReport,
        }
      }
      catch(e){
        this.isNoResponses = true; // todo: should probably limit to e.message == NO_RESPONSE_DATA
        this.isLoading = false;
      }
    }
    this.sessionReport.responses = this.sessionReport.responses.filter(response => !!response.response_raw)
    await this.processResults(this.sessionReport, this.selectedTestDesign);
  }

  getLocalMarkingItemConfig(){
    return this.localMarkingFramework.items[this.currentQuestionId] || (<any> {})
  }
  getCurrLocalMarkingCaption(){
    return this.getLocalMarkingItemConfig().caption
  }
  getCurrLocalMarkingFlags(){
    return this.getLocalMarkingItemConfig().topFlags
  }
  getCurrLocalMarkingScales(){
    return this.getLocalMarkingItemConfig().scales
  }

  checkScoreOptions(caption){
    if(caption == 'NR' || caption == 'INS'){
      return false;
    }
    else{
      return true;
    }
  }
  
  private getAttemptCache(test_question_id:number, attempt_id:number){
    const itemMarks = this.localMarkingData.itemMarks
    if (!itemMarks[test_question_id]){
      itemMarks[test_question_id] = {attempts: {}}
    }
    if (!itemMarks[test_question_id].attempts[attempt_id]){
      itemMarks[test_question_id].attempts[attempt_id] = {
        flagScoreOption: null,
        scales: {}
      }
    }
    return itemMarks[test_question_id].attempts[attempt_id];
  }

  isSavingMark:boolean;

  async dbSaveMark(attempt_id:number, item_id:number, data:any){
    await this.auth.apiPatch('public/educator/local-scoring-temp', this.test_session_id, data, {query: {attempt_id, item_id}})
  }

  private async processSaveAgg(saveAgg:LocalScoringSaveAgg, options?:{isSkipNetworkSave?: boolean}){
    options = options || {}
    await Promise.all(
      saveAgg.preSaveCaches.map(async (saveCache) => {
        const {attempt_id, test_question_id, attempt, attemptCache} = saveCache;
        this.updateLocalMarkingAttemptCache(test_question_id, attempt)
        try {
          if (!options.isSkipNetworkSave){
            await this.dbSaveMark(attempt_id, test_question_id, attempt)
          }
        }
        catch(e){
          this.loginGuard.quickPopup('SAVE_ERROR')
          const attemptRestore = JSON.parse(attemptCache);
          Object.keys(attemptRestore).forEach(prop => {
            attempt[prop] = attemptRestore[prop];
          })
          this.updateLocalMarkingAttemptCache(test_question_id, attempt)
          // todo: rollback using props in attemptCache
        }
      })
    )
    this.isSavingMark = false;
    this.updateLocalMarkingCaches();
  }

  updateLocalMarkingAttemptCache(test_question_id:number, attempt:IItemAttemptMarkingData){
    if (attempt){
      // Get the scoring framework settings
      const itemSettings = this.localMarkingFramework.items[test_question_id]
      if(!itemSettings){
        return;
      }
      const scales = itemSettings.scales
      let score = 0;
      let numMarksApplied = 0;
      // For each scale, pulling the weight from the scoring framework settings and applying them to the scoreValue
      for (let scale of scales){
        const scaleResult = attempt.scales[scale.scaleCode]
        if (scaleResult){
          const weight = scale["weight"]
          const maxScore = scale["maxScoreValue"];
          const scoreOptions = scale["scoreOptions"]
          // Loop over the options and compare with the score code. If it matches, adjust the scoreValue
          for(const frameworkScoreOption of scoreOptions){
            if(frameworkScoreOption["scoreCode"] == scaleResult.scoreCode){
              scaleResult.scoreValue = frameworkScoreOption["scoreValue"] * weight / maxScore;
            }
          }
          score += scaleResult.scoreValue;
          if (scaleResult.scoreCode){
            numMarksApplied ++
          }
        }
      }
      const perc = score;
      attempt.cachedScore = +((perc).toFixed(2))
      attempt.cachedFullyMarked = (!!attempt.flagScoreOption) || (numMarksApplied == scales.length);
    }
  }

  getNumItemsMarked(){
    return this.localMarkingData?.numItemsMarked || 0;
  }

  isIndicatedLocalMarkingDisabled = false
  indicateLocalMarkingDisabled(){
    if (!this.isIndicatedLocalMarkingDisabled){
      this.isIndicatedLocalMarkingDisabled = true;
      console.error('Local Marking not configured for this assessment')
      alert('Local Marking not configured for this assessment.')
    }
  }
  updateLocalMarkingCaches(){
    // check for full question completion
    let numItemsMarked = 0;
    const itemSummary = this.localMarkingData.itemSummary
    for (let question of this.questions){
      const test_question_id = +question.id;
      let isMarkPending = false;
      for (let student of this.students){
        const {attempt_id} = student;
        if (attempt_id){
          const attempt = this.getAttemptCache(test_question_id, attempt_id);
          if (!attempt || !attempt.cachedFullyMarked){
            isMarkPending = true;
          }
        }
      }
      if (!isMarkPending){
        numItemsMarked ++;
      }
      if (!itemSummary[test_question_id]){
        itemSummary[test_question_id] = {};
      }
      itemSummary[test_question_id].isComplete = !isMarkPending
    }
    this.localMarkingData.numItemsMarked = numItemsMarked;

    // pick student attempts
    for (let student of this.students){
      const {attempt_id} = student;
      let score = 0;
      let scoreMax = 0;
      student.scoreComplete = true;
      try {
        for (let question of this.questions){
          
          const test_question_id = +question.id;
          const response = student.responseRef.get(test_question_id)
          const itemSettings = this.localMarkingFramework.items[test_question_id]
          if(itemSettings && this.isQuestionLocalScoreEnabled(+question.id)){
            scoreMax += itemSettings.weight
            if (attempt_id){
              const attempt = this.getAttemptCache(test_question_id, attempt_id);
              if (attempt?.cachedScore){
                score += attempt.cachedScore
              }
              if (response && !(attempt?.cachedFullyMarked)){
                student.scoreComplete = false;
              }
            }
          }
          // Supporting non local score questions
          else{
            if (question.maxWeight){
              scoreMax += +question.maxWeight;
            }
            const response = student.responseRef.get(+question.id)
            if (response && response.weight){
              if (response.score){
                score += +response.score
              }
            }
          }
        }
      }
      catch (e){
        console.error(e);
        this.indicateLocalMarkingDisabled()
      }
      if (scoreMax){
        student.percScore = this.formatPerc(score/scoreMax);
        
      }
      else {
        student.percScore = this.formatPerc(0)
      }
    }
  }

  async saveFlag(scoreOption:IScoreOptionMinimal){
    this.isSavingMark = true
    const responses = this.getCurrentResponses();
    const saveAgg = new LocalScoringSaveAgg();
    for (let response of responses){
      const {test_question_id, attempt_id} = response;
      const availableScales = this.localMarkingFramework.items[test_question_id].scales
      const attempt = this.getAttemptCache(test_question_id, attempt_id);
      saveAgg.add(attempt_id, test_question_id, attempt)
      if (attempt.flagScoreOption == scoreOption.scoreCode){
          attempt.flagScoreOption = null;
      }
      else {
        attempt.flagScoreOption = null;
        attempt.flagScoreOption = scoreOption.scoreCode;
        for (let scale of availableScales){
          if (attempt.scales[scale.scaleCode]){
            attempt.scales[scale.scaleCode] = null //todo:api
          }
        }
      }
    }
    await this.processSaveAgg(saveAgg);
  }

  async saveMark(scaleCode:string, scoreOption:IScoreOptionMinimal){
    this.isSavingMark = true;
    const responses = this.getCurrentResponses();
    const saveAgg = new LocalScoringSaveAgg();
    // console.log('responses', responses);
    for (let response of responses){
      const {test_question_id, attempt_id} = response;
      const attempt = this.getAttemptCache(test_question_id, attempt_id)
      saveAgg.add(attempt_id, test_question_id, attempt)
       if (attempt.flagScoreOption){
        attempt.flagScoreOption = null
       }
      if (attempt.scales[scaleCode]?.scoreCode == scoreOption.scoreCode){
        attempt.scales[scaleCode] = null
      }
      else {
        let weight;
        let maxScoreValue;
        const itemSettings = this.localMarkingFramework.items[test_question_id]
        const scales = itemSettings.scales
        for (let scale of scales){
        if (scale.scaleCode == scaleCode){
           weight = scale["weight"]
           maxScoreValue = scale["maxScoreValue"]
        }
      }
        attempt.scales[scaleCode] = {
          scoreCode:  scoreOption.scoreCode,  
          scoreValue: scoreOption.scoreValue * weight / maxScoreValue,
        }
      }
    }
    await this.processSaveAgg(saveAgg);
  }

  
  async updateLocalScoringCache(){
    for (const [questionId, question] of this.questionRef.entries()){
      if(!question.responseGroups){
        continue;
      }
      for(const responseGroup of question.responseGroups){
          const saveAgg = new LocalScoringSaveAgg();
          // console.log('responses', responses);
          for (let response of responseGroup.responses){
            const {test_question_id, attempt_id} = response;
            const attempt = this.getAttemptCache(test_question_id, attempt_id)
            saveAgg.add(attempt_id, test_question_id, attempt)
          }
          await this.processSaveAgg(saveAgg, {isSkipNetworkSave: true});
        
      }
    }
  }

  isFlagSelected(scoreOption:IScoreOptionMinimal){
    const responses = this.getCurrentResponses();
    // console.log('responses', responses);
    const response = responses[0];
    const {test_question_id, attempt_id} = response;
    const attempt = this.getAttemptCache(test_question_id, attempt_id)
    if (attempt.flagScoreOption == scoreOption.scoreCode){
      return true;
    }
    return false;
  }

  isMarkSelected(scaleCode:number, scoreOption:IScoreOptionMinimal){
    const responses = this.getCurrentResponses();
    const response = responses[0];
    const {test_question_id, attempt_id} = response;
    const attempt = this.getAttemptCache(test_question_id, attempt_id)
    if (attempt.scales[scaleCode]){
      return (attempt.scales[scaleCode]?.scoreCode == scoreOption.scoreCode)
    }
    return false;
  }

  getCurrentResponseGroups(){
    return this.getCurrentQuestion()?.responseGroups || []
  }

  isFullyMarkedCached(responseGroupIndex:number){
    const responseGroup = this.getCurrentResponseGroups()[responseGroupIndex]
    const response = responseGroup.responses[0];
    const {test_question_id, attempt_id} = response;
    const attempt = this.getAttemptCache(test_question_id, attempt_id)
    return attempt.cachedFullyMarked
  }

  getCurrentResponses(){
    const responses:any[] = [];
    for (let responseGroup of this.getCurrentResponseGroups()){
      if (this.currentResponseKey == responseGroup.responseKey){
        responses.push(... responseGroup.responses)
      }
    }
    return responses
  }

  private getCurrentResponseGroupsAndIndex(){
    const responseGroups = this.getCurrentResponseGroups();
    for (let i=0; i<responseGroups.length; i++){
      const responseGroup = responseGroups[i];
      if (this.isSelectedResponseGroup(responseGroup)){
        return {i, responseGroups}
      }
    }
    return {i:-1, responseGroups}
  }
  prevLocalMarking(){
    const {i, responseGroups} = this.getCurrentResponseGroupsAndIndex()
    if (i > 0){
      this.renderResponseGroup(responseGroups[i-1])
    }
  }
  nextLocalMarking(){
    const {i, responseGroups} = this.getCurrentResponseGroupsAndIndex()
    if (i < responseGroups.length - 1){
      this.renderResponseGroup(responseGroups[i+1])
    }
  }

  startLocalMarking(){
    this.isScoring = true;
    if (!this.getCurrentQuestion()){
      this.currentQuestionId = +this.questions[0].id
    }
    if (!this.currentResponseKey){
      // If not a local marking question, navigate to first local marking question
      if(!this.isQuestionLocalScoreEnabled(this.currentQuestionId) && this.localMarkingQuestions[0]){
        this.currentQuestionId = +this.localMarkingQuestions[0].id;
      }
      this.selectFirstResponse()
    }
  }
  selectFirstResponse(){
    this.renderResponseGroup(this.getCurrentResponseGroups()[0], true)
  }

  localMarkingFramework:ILocalMarkingFramework; //todo: apply types
  test_designs;
  selectedTestDesign;
  localMarkingScalesMap;
  selectForm(testFormRecord){
    const formId = testFormRecord.test_design_id
    // When switching between multiple form, clear current question and response
    this.currentQuestionId = null;
    this.currentResponseKey = null;
    this.processResults(this.sessionReport, formId);
    this.router.navigate([this.lang.c(), 'educator', 'session-report', this.sc_id, this.test_session_id, formId]);
  }

  processResultRemappings(sessionReport:ISessionReport){
    const {responses, entryIdRemappings} = sessionReport;
    const remappingsBySrcTd:IEntryIdRemappingRef = new Map();
    // align the mappings 
    for (let remapping of entryIdRemappings){
      const {td_id_response, td_id_layout, config} = remapping;
      if (!remappingsBySrcTd.has(td_id_response)){
        remappingsBySrcTd.set(td_id_response, new Map());
      }
      remappingsBySrcTd.get(+td_id_response).set(+td_id_layout, new Map()); // only expecting one remapping per src/target
      const byItem = remappingsBySrcTd.get(+td_id_response).get(+td_id_layout);
      for (let item of config.items){
        byItem.set(item.item_id, item);
      }
    }

    // apply the mappings
    for (let response of responses){
      const {test_design_id, response_test_design_id, response_raw} = response;
      const remappingBySrc = remappingsBySrcTd.get(response_test_design_id);
      if (remappingBySrc){
        const byItem = remappingBySrc.get(test_design_id);
        const itemRemappings = byItem.get(response.test_question_id);
        if (itemRemappings){
          const responseObj = JSON.parse(response_raw);
          const entryValues:{[entryId:string]:any} = {};
          // read 
          for (let entry of itemRemappings.entries){
            const {entry_id_from, entry_id_to} = entry;
            entryValues[entry_id_to] = responseObj[entry_id_from];
          }
          // write
          for (let entry of itemRemappings.entries){
            const {entry_id_from, entry_id_to} = entry;
            responseObj[entry_id_to] = entryValues[entry_id_to]
          }
          // 
          response.response_raw = JSON.stringify(responseObj);
        }
      }
    }
    
  }

  async processResults(sessionReport:ISessionReport, selectNewFormID?){
    this.isLoading = true;
    let testFormRecord = sessionReport.test_designs[0];
    if(selectNewFormID){
      testFormRecord = sessionReport.test_designs.find(td => td.test_design_id == selectNewFormID);
    }    
    this.selectedTestDesign = testFormRecord.test_design_id;
    this.test_designs = sessionReport.test_designs;
    this.questionRef = new Map();
    this.sectionRef = new Map()
    this.studentRef = new Map();

    this.isExportEnabled = (testFormRecord.is_download_results == 1)
    this.isLocalScoreEnabled = (testFormRecord.is_local_score == 1)
    this.isPerusalEnabled = this.isPerusalAvailable(testFormRecord)
    this.perusalDate = this.renderPerusalDate(testFormRecord)
    this.isBulkPrintEnabled = (testFormRecord.is_bulk_print == 1)

    try {
      this.processResultRemappings(sessionReport); // todo: consider making more efficient by not re-JSONing the response raw in this
    }
    catch (e){
      this.loginGuard.quickPopup('Error processing remappings')
    }

    if (this.isLocalScoreEnabled){
      this.localMarkingData.itemMarks = sessionReport.itemMarks;
      this.localMarkingFramework = null;
      try {
        const localScoringFrameworks = await this.auth.apiFind('public/educator/local-scoring/scoring-framework',{
          query: {
            ts_id: this.test_session_id,
            td_id: testFormRecord.test_design_id,
            slug: testFormRecord.slug,
          }
        })
        const totalWeight = localScoringFrameworks[0].totalWeight;
        this.localMarkingScalesMap = localScoringFrameworks[0].scalesMap;
        let items = {};
        for (const localScoringFramework of localScoringFrameworks){
          localScoringFramework.items.map(item => {items[item.item_id] = item})
        }
        this.getScoringGuide(localScoringFrameworks[0].items)
        this.localMarkingFramework = {totalWeight,items}
      }
        catch(e){
          console.log(e)
          this.localMarkingFramework = null;
        } 
      if (!this.localMarkingFramework){
        this.localMarkingFramework = tempGetLocalMarkingFramework(testFormRecord.slug); //temp, should come from the API request
      }
    }

    // sessionReport.test_form_id = testFormRecord.tf_id
    this.test_design_id = testFormRecord.test_design_id

    this.questions = [];
    for (let question of sessionReport.questions){
      this.questionRef.set(+question.id, question);
    }
    const {sections, questionDb} = testFormRecord.testForm
    for (let section of sections){
      // section.caption
      section.questions.forEach(question_id => {
        const question = this.questionRef.get(+question_id)
        if (question){
          this.sectionRef.set(section, section)
          // if (questionDb[+question_id]){
          //   question.itemDisplay = questionDb[+question_id]
          // }
          // else {
            try { // temp
              let questionConfig = JSON.parse(question.config);
              if (testFormRecord.lang == 'fr' && questionConfig.langLink){
                questionConfig = questionConfig.langLink
              }
              question.itemDisplay = questionConfig
              question.isPrintMode = this.hasPrintableVersion(questionConfig)
            } catch(e){}
          // }
          // console.log('question', question.id)
          this.questions.push(question)
        }
      })
    }
    const responses:IReportResponseRecord[] = sessionReport.responses.filter(r => r.test_design_id == testFormRecord.test_design_id);
    // this.students = sessionReport.students;
    // only displaying students who served a response 
    this.students = sessionReport.students.filter(s => responses.find(r => r.uid == s.uid));
    for (let student of this.students){
      this.studentRef.set(+student.uid, student);
      student.responseRef = new Map();
    }
    let q_auto_increment = 1
    for (let question of this.questions){
      question.auto_increment = 'Q'+q_auto_increment
      question.maxWeight = 0;
      question.responseGroupRef = new Map();
      question.responseGroups = [];
      q_auto_increment++ // todo:temp
    }
    for (let response of responses){
      response.responseKey =  response.score+';'+response.responseText
      const question = this.questionRef.get(+response.test_question_id);
      if (question){
        if (response.weight){
          question.maxWeight = Math.max(question.maxWeight, (+response.weight || 0))
        }
      }
    }
    for (let question of this.questions){
      question.maxWeight = Math.max(question.maxWeight, question.weight)
    }
    const ensureResponseGroup = (response:IReportResponseRecord, question:IQuestion) => {
      const responseText = response.responseText
      const responseKey = response.responseKey
      if (!question.responseGroupRef.has(responseKey)){
        const score = +response.score;
        const weight = +question.maxWeight
        const isCorrect:boolean = (!!weight) && (score == weight);
        let responseState:any = {}
        try {
          responseState = JSON.parse(response.response_raw);
        } catch(e){}
        if (responseState){
          responseState.updated_on = mtz(response.updated_on).format(this.lang.tra('datefmt_dashboard_short'));
        }
        const responseGroup:IResponseGroup = {
          responseKey,
          responseText,
          responseState,
          isCorrect,
          score,
          weight,
          responses: [],
        }
        question.responseGroups.push(responseGroup);
        question.responseGroupRef.set(responseKey, responseGroup);
      }
      return <IResponseGroup> question.responseGroupRef.get(responseKey)
    }
    for (let response of responses){
      const student = this.studentRef.get(+response.uid);
      const question = this.questionRef.get(+response.test_question_id);
      if (question){
        const responseGroup = ensureResponseGroup(response, question);
        responseGroup.responses.push(response);
      }
      if (student){
        student.attempt_id = response.attempt_id; // assume one attempt per student
        student.is_submitted = 1 // response.is_submitted;
        student.responseRef.set(+response.test_question_id, response)
      }
    }
    for (let student of this.students){
      let score = 0;
      let weightTotal = 0;
      let itemsSeen = 0
      let cutOff = 0;
      for (let question of this.questions){
        if (question.maxWeight){
          weightTotal += +question.maxWeight;
        }
        const response = student.responseRef.get(+question.id)
        if (response && response.weight){
          itemsSeen += 1
          if (response.score){
            score += +response.score
          }
        }
        if (this.isScoreEntrySession) {
          cutOff += +JSON.parse(question.config).meta.Cut
          // If score < cutOff, keep track of it within the question for showing overall
          if (!question.totalAtRisk) {
            question.totalAtRisk = 0;
          }
          if (!response || (response.score < cutOff)) {
            question.totalAtRisk++;
          }
        }
      }
      student.percScore = this.formatPerc(score / (weightTotal || 1))
      student.percComplete = this.formatPerc(itemsSeen / this.questions.length)
      
      if (this.isScoreEntrySession) {
        student.percCutOff = Math.round(cutOff / this.questions.length)
        this.totalStudentScore += this.formatPerc(score / (weightTotal || 1))
        this.totalCutScore += this.formatPerc(cutOff / (weightTotal || 1))
      }
      
    }
    for (let question of this.questions){
      question.responseGroups = question.responseGroups.sort((a,b) => {
        const sortCount = a.responses.length - b.responses.length;
        if (sortCount){ return (sortCount > 0) ? -1 : 1 }
        if (a.responseText > b.responseText){ return 1 }
        if (a.responseText < b.responseText){ return -1 }
        return 0;
      })
    }

    if (this.isLocalScoreEnabled){
      // Get the list of local score enabled questions
      const itemIdsWithLocalScoring = Object.keys(this.localMarkingFramework.items);
      this.localMarkingQuestions = this.questions.filter((question) => {
        return itemIdsWithLocalScoring.includes(""+question.id);
      });
      await this.updateLocalMarkingCaches();
      await this.updateLocalScoringCache()
    }

    this.isLoading = false;
  }

  async validateScoreCacheWeight(itemMarks: IItemMarkingData, frameworkQuestionItems: any[]){
    let currentCacheScoreValid = true;
    for (const [itemMarkKey, itemMarksValue] of Object.entries(itemMarks)) {
      // Filter questions from the framework to match the currently displayed item
      const frameworkQuestionItem = frameworkQuestionItems.filter((item) => item["item_id"] == itemMarkKey)[0];
      if(!frameworkQuestionItem){
        continue;
      }
      const cacheItemMarksValueAttempts: object = itemMarksValue.attempts;

      for (const [attemptKey, cacheAttemptValue] of Object.entries(cacheItemMarksValueAttempts)){
        // Initialize score values
        let totalScores = 0;
        let maxScore = 0;
        let scoreOptions = [];
        const cacheScales = cacheAttemptValue.scales;
        for (const [scoreKey, scoreValue] of  Object.entries(cacheScales)){
          if(scoreKey == null || scoreValue == null) continue;
          let weight = 0;
          // Match score values with the framework
          for (const frameworkScale of frameworkQuestionItem["scales"]){
            if(scoreKey == frameworkScale["scaleCode"] ){
              maxScore = frameworkScale["maxScoreValue"];
              weight = frameworkScale["weight"];
              scoreOptions = frameworkScale["scoreOptions"]
            }
          }
          if(scoreValue["scoreCode"] == null){
            continue;
          }
          // Update the cached score
          const cacheScoreCode = scoreValue["scoreCode"]
          for(const frameworkScoreOption of scoreOptions){
            if(frameworkScoreOption["scoreCode"] == cacheScoreCode){
              totalScores += frameworkScoreOption["scoreValue"] * weight;
            }
          }
      }
      if(cacheAttemptValue["cachedScore"] !== (totalScores /  maxScore)){
        currentCacheScoreValid = false;
        cacheAttemptValue["cachedScore"] = (totalScores /  maxScore).toFixed(1);
      }
    }
  }  
  // Future implementation, if it doesn't match database then update database
  // Return the new updated marks
  return itemMarks;
}

  
  selectedScoringGuide;
  scoringGuides: {id?: string, caption: string, url: string, src: SafeResourceUrl}[]= [];
  getScoringGuide(items: any[]){
    if(items.length > 0){
      for(const item of items){
        if(item.scales && item.scales.length > 0){
          for(const scale of item.scales){
            if(scale.scoreGuide_1){
              this.scoringGuides.push({
                url: scale.scoreGuide_1,
                caption: this.getScoringGuideName(scale.scoreGuide_1),
                src: this.getScoringGuideIframeSrc(scale.scoreGuide_1)
              })
            }
            if(scale.scoreGuide_2){
              this.scoringGuides.push({
                url: scale.scoreGuide_2,
                caption: this.getScoringGuideName(scale.scoreGuide_2),
                src: this.getScoringGuideIframeSrc(scale.scoreGuide_2)
              })
            }
          }
        }
      }
      this.scoringGuides.forEach((guide, index) => {
        guide.id = ''+index;
        if (index == 0){
          this.selectedScoringGuide = guide;
        }
      })
    }
  }
  getScoringGuideName(url: string){
    const path = url.split("/")
    const fileName = path[path.length - 1];
    return fileName.endsWith(".pdf") ? fileName.replace(/\.pdf$/, "") : fileName;
  }
  getScoringGuideIframeSrc(url: string){
    const urlRaw = `${pdfRunner}?url=${url}#zoom=100`
    const urlSrc: SafeResourceUrl = this.sanitizer.bypassSecurityTrustResourceUrl(urlRaw);
    return urlSrc;
  }
  selectScoringGuide(id: number){
    this.selectedScoringGuide = this.scoringGuides[+id]
  }

  displayScale(scoreCode: string){
    if(this.localMarkingScalesMap){
      return this.localMarkingScalesMap[scoreCode] || scoreCode
    }
    else {
      return scoreCode;
    }
  }
  getCurrentFormId(){
    for (let i = 0; i < this.test_designs.length; i++)
    if(this.selectedTestDesign == this.test_designs[i].test_design_id)
    {
      return i + 1
    }
  }
  responseFontSize:number = 1;
  fontSizeIncrSize = 0.2
  getCurrentFontSizePerc = () => Math.round(this.responseFontSize*100) + '%'
  getCurrentFontSize = () => this.responseFontSize;
  fontSizeIncr(){
    this.responseFontSize += this.fontSizeIncrSize
    this.responseFontSize = Math.max(0.5, this.responseFontSize)
  }
  fontSizeDecr(){
    this.responseFontSize -= this.fontSizeIncrSize
    this.responseFontSize = Math.max(0.5, this.responseFontSize)
  }

  renderTable(){
    this.currentViewMode = ViewMode.ALL;
  }

  viewTable(){
    this.isScoring = false;
    const formId = this.selectedTestDesign;
    return this.router.navigate([this.lang.c(), 'educator', 'session-report', this.sc_id, this.test_session_id, formId]);
  }
  viewQuestion(questionId:number, student?:IStudent){
    if (this.isScoreEntrySession) {
      return;
    }
    const queryParams:any = {}
    if (student){
      const response = student.responseRef.get(questionId);
      if(!response){
        this.loginGuard.quickPopup("No student responses found.")
      }
      queryParams.rgIndex = this.getResponseGroupIndexByResponse(response)
    }
    const formId = this.selectedTestDesign;
    return this.router.navigate([this.lang.c(), 'educator', 'session-report', this.sc_id, this.test_session_id, formId, questionId], {queryParams});
  }

  hasPrintableVersion(questionConfig:IQuestionConfig){
    return hasPrintBlocks(questionConfig.content);
  }
  
  loadQuestion(questionId:number){
    this.currentViewMode = ViewMode.QUESTION;
    this.currentQuestionId = +questionId;
    const question = this.getCurrentQuestion();
    this.currentResponseState = {};
    this.currentResponseKey = null;
  }

  isSelectedResponseGroup(responseGroup:IResponseGroup){
    return (this.currentResponseKey == responseGroup.responseKey)
  }
  isCurrentSelectedResponseGroup(){
    return !!this.currentResponseKey;
  }

  getResponseGroupIndexByResponse(response: IReportResponseRecord){
    const responseGroups = this.getQuestionById(response.test_question_id).responseGroups
    for (let i=0; i<responseGroups.length; i++){
      return i
    }
    return -1;
  }

  renderResponseGroup(responseGroup:IResponseGroup, switchQuestion: boolean = false){
    if(!responseGroup){
      this.currentResponseKey = null;
      this.currentResponseState = {};
      return;
    }
    this.isResettingQuestion = true;
    if (this.isSelectedResponseGroup(responseGroup) && !switchQuestion){
      this.currentResponseKey = null;
      this.currentResponseState = {};
    }
    else {
      // console.log('responseGroup.responseState', responseGroup.responseState)
      this.currentResponseKey = responseGroup.responseKey
      this.currentResponseState = responseGroup.responseState;
    }
    setTimeout(() => {
      this.isResettingQuestion = false;
    }, 100);
  }

  navQuestionPrev(){
    const i = Math.max(0, this.getCurrentQuestionIndex()-1)
    this.navQuestionIndex(i)
  }
  navQuestionNext(){
    const i = Math.min(this.questions.length-1, this.getCurrentQuestionIndex()+1)
    this.navQuestionIndex(i)
  }
  navQuestionIndex(i:number){
    const question = this.questions[i]
    console.log('navQuestionIndex', question)
    this.viewQuestion(+question.id);
  }
  getCurrentQuestionIndex(){
    const q = this.getCurrentQuestion()
    if (q){
      return this.questions.indexOf(q)
    }
    return -1
  }
  getCurrentQuestion(){
    return this.getQuestionById(+this.currentQuestionId)
  }
  getQuestionById(questionId:number){
    const question = this.questionRef.get(+questionId)
    return question;
  }

  private formatPerc(num:number){
    return Math.round(num*100)
  }

  exportResults(){
    let header = ['ASN','first_name','last_name', 'score']
    for (let question of this.questions){
      header.push(question.auto_increment);
    }
    let csv = header.join(',')+'\n'
    for (let student of this.students){
      const {student_gov_id, first_name, last_name, percScore} = student;
      let row:(number | string)[] = [student_gov_id, first_name, last_name ]
      
      if(!student.scoreComplete && this.isHidePartialScore){
        row.push("Score Not Completed")
      }
      if(!student.is_submitted && !this.isLocalScoreEnabled){
        row.push("Not Submitted")
      }
      else{
        row.push(percScore+ "%");
      }
        
      for (let question of this.questions){
        const response = student.responseRef.get(+question.id)
        if (this.isLocalScoreEnabled){
          if(this.isQuestionLocalScoreEnabled(+question.id)){
            // Displaying score
            row.push( this.lang.tra(this.getStudentQuestionScoreDisplay(student, question, true)) );
          }
          else if (response && response.weight && response.score){
            row.push( +response.score );
          } 
          else{
            row.push(0)
          }
        }
        else if (response && response.weight && response.score){
          // Displaying score percent
          row.push( +response.score / +question.maxWeight);
        }
        else {
          row.push(0)
        }
      }
      csv += row.join(',')+'\n'
    }
    downloadStr(csv, 'results.csv')
  }

  getFreqBarWidthEn(n_students:number){
    return MAX_FREQ_BAR_WIDTH_EM*(n_students/ this.students.length)
  }

  getQuestionPoints(question:IQuestion){
    const test_question_id = +question.id
    const itemSettings = this.localMarkingFramework.items[test_question_id]
    if(itemSettings){
      return "(" + itemSettings.weight + ' point' + ( itemSettings.weight == 1 ? 's' : '' ) + ")";
    }
  }
  
  getStudentQuestionMarked(student:IStudent, question:IQuestion){
    const test_question_id = +question.id
    const response = student.responseRef.get(test_question_id)
    if (response){
      const attempt = this.getAttemptCache(test_question_id, response.attempt_id);
      if (attempt && attempt.cachedFullyMarked){
        return true;
      }
    }
    return false
  }
  getStudentQuestionScoreDisplay(student:IStudent, question:IQuestion, isNumeric:boolean = false){
    const test_question_id = +question.id
    const response = student.responseRef.get(test_question_id)
    if (response){
      const attempt = this.getAttemptCache(test_question_id, response.attempt_id);
      if (attempt){
        if (attempt.flagScoreOption){
          return attempt.flagScoreOption
        }
        else {
          // const itemSettings = this.localMarkingFramework.items[test_question_id]
          return (attempt.cachedScore || 0) //  + ' / ' + itemSettings.weight
        }
      }
    }
    if (isNumeric){
      return 0;
    }
    if(!response){
      return '(No Response)'
    }
    else {
      return '(Not Marked)'
    }
  }

  getStudentQuestionCorrect(student:IStudent, question:IQuestion){
    const response = student.responseRef.get(+question.id)
    if (response && student.is_submitted){
      if (this.isScoreEntrySession) {
        const cutOff = +JSON.parse(question.config).meta.Cut
        if (cutOff && response.score >= cutOff){
          return 'YES'
        }
        return 'NO'
      }
      else if (student.is_submitted){
        if (response.weight && response.score == response.weight){
          return 'YES'
        }
        return 'NO'
      }
    }
    return 'NR'
  }
  getQuestionTotalCorrect(question:IQuestion) {
    if (question.totalAtRisk / this.students.length < 0.5) {
      return 'YES'
    }
    return 'NO'
  }
  getScoreTotalCorrect() {
    if (this.totalStudentScore >= this.totalCutScore) {
      return 'YES'
    }
    return 'NO'
  }

  getStudentIdent(){
    return this.whiteLabel.getSiteText('student_ident');
  }

  showFormSelection(){
    return this.test_designs.length > 1;
  }
  
  //in this function we want to make sure that we are not on the last page to print so we don't print out an empty page at the bottom every time.
  notOnLastPage(respIndex, respgroupIndex, questionIndex){
    //we need to combine the indexes and array lengths since the responses are rendered through 3 nested loops.
    const questionsEdge     = this.questions.length - 1;
    const responseGroupEdge = this.questions[questionIndex].responseGroups.length - 1;
    const responsesEdge     = this.questions[questionIndex].responseGroups[respgroupIndex].responses.length - 1;
    return (respIndex + respgroupIndex + questionIndex) < (questionsEdge + responseGroupEdge + responsesEdge);
  }
  cModal() {
    return this.pageModal.getCurrentModal();
  }
  cmc() { return this.cModal().config; }
  guideModalStart() {
    let config;
    
    this.pageModal.newModal({
      type: "GUIDE",
      config,
      finish: config => {}
    });
  }
  getCurrentAccommodationList(){
    let accommodationList = [];
    const currentResponses = this.getCurrentResponses();
    if(currentResponses){
      for(const currentResponse of currentResponses){
        const accommodation = this.studentRef.get(currentResponse.uid).accommodation.filter(accom => accom.value);
        accommodationList.push(...accommodation)
      }
      // Find unique accmmodation from the list
      let uniqueAccommodationList = [];
      for (const accommodation of accommodationList){
        let alreadyExist = false;
        for(const uniqueAccommodation of uniqueAccommodationList){
          if(uniqueAccommodation.name == accommodation.name){
            alreadyExist = true;
          }
        }
        if(!alreadyExist){
          uniqueAccommodationList.push(accommodation)
        }
      }
      return uniqueAccommodationList;
    }
    else{
      return [];
    }
  }
  // loadSummaryReport() {
  //   this.currentViewMode = ViewMode.SUMMARY;
  // }

  loadIndividualReport(student:IStudent) {
    this.selectedStudent = student;
    this.currentViewMode = ViewMode.INDIVIDUAL;
  }
  isQuestionLocalScoreEnabled(questionId :number){
    if(this.localMarkingQuestions){
      const localMarkingQuestionsIds = this.localMarkingQuestions.map(localMarkingQuestion => +localMarkingQuestion.id);
      return localMarkingQuestionsIds.includes(questionId)
    }
    else{
      return false;
    }
  }
  isCurrentQuestionLocalScoreEnabled(){
    return this.isQuestionLocalScoreEnabled(+this.currentQuestionId);
  }


  renderPerusalDate(testFormRecord){
    const {is_tw_current, is_perusal_allow, perusal_configs, perusal_type, perusal_offset_hours, perusal_duration_hours, perusal_date_start, perusal_date_end, ts_date_time_start, first_ta_started_on, perusal_end_type, tw_date_start, tw_date_end, test_date_end} = testFormRecord
    const perusalSettings = {is_perusal_allow, perusal_configs, perusal_type, perusal_end_type, perusal_offset_hours, perusal_duration_hours, perusal_date_start, perusal_date_end}
    const sessionInfo = {ts_date_time_start, first_ta_started_on, tw_date_start, tw_date_end, test_date_end}

    return renderPerusalDate(perusalSettings, sessionInfo, this.lang, true);
  }

  isPerusalAvailable(testFormRecord) {
    const {is_tw_current, is_perusal_allow, perusal_configs, perusal_type, perusal_offset_hours, perusal_duration_hours, perusal_date_start, perusal_date_end, ts_date_time_start, first_ta_started_on, perusal_end_type, tw_date_start, tw_date_end, test_date_end} = testFormRecord
    const perusalSettings = {is_perusal_allow, perusal_configs, perusal_type, perusal_end_type, perusal_offset_hours, perusal_duration_hours, perusal_date_start, perusal_date_end}
    const sessionInfo = {ts_date_time_start, first_ta_started_on, tw_date_start, tw_date_end, test_date_end}
    return isPerusalAvailableForRole(perusalSettings, sessionInfo, !!is_tw_current, true)
  }
}
