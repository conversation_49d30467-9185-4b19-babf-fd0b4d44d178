<ng-container *ngIf="isNoResponses">
    <div class="reports-body is-padded">
        <div class="notification is-warning">
            No responses received yet (click the back button, or refresh the page if students are actively working through the assessment)
        </div>
    </div>
    <div class="reports-header">
        <div class="reports-header-breadcrumb">
            <span class="header-label"><tra slug="abed_invig_stdnt_results"></tra></span>
            <span class="breadcrumb-separator">/</span>
            <a href="#/en/educator/classrooms/{{sc_id}}"><tra slug="abed_invig_class"></tra></a>
        </div>
        <div>
        </div>
    </div>
</ng-container>
<ng-container *ngIf="!isNoResponses">
    <div *ngIf="isLoading">Loading...</div>
    <ng-container *ngIf="!isLoading && !isScoring">
        <pre *ngIf="false">
            isPrintView: {{isPrintView}}
            isPrintViewMulti: {{isPrintViewMulti}}
        </pre>

        <div *ngIf="!isPerusalEnabled" class="notification is-warning" style="margin:1em;">
          Perusal is not available.
          <span *ngIf="perusalDate">
            The availability period is {{perusalDate}}.
          </span>
          <span *ngIf="!perusalDate">
            Perusal is not configured for this assessment, contact the ministry for more details.
          </span>
        </div>

        <div *ngIf="isPrintView">
            <ng-container *ngIf="isPrintViewSingle && getCurrentQuestion().itemDisplay && currentResponseState && !isResettingQuestion">
                <button 
                    class="button is-small is-info dont-print" 
                    (click)="exitPrintMulti()"
                >
                    <tra slug="abed_invig_exit_print"></tra>
                </button>
                <div>
                    <div *ngFor="let responseGroup of getCurrentQuestion().responseGroups" >
                        <div *ngIf="isSelectedResponseGroup(responseGroup)">
                            <div *ngFor="let response of responseGroup.responses">
                                <tra slug="abed_invig_student_name"></tra>&nbsp;{{ getResponseStudentName(response) }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="is-dbl-spaced-print">
                    <question-runner 
                        [currentQuestion]="getCurrentQuestion().itemDisplay" 
                        [questionState]="currentResponseState"
                        [isSkipAnimatedEntry] = "true"
                        [isSubmitted]="true" 
                        [isPrintMode]="isCurrentQuestionPrintMode()"
                    ></question-runner>
                </div>
            </ng-container>
            <ng-container *ngIf="isPrintViewMulti">
                <button 
                    class="button is-small is-info dont-print" 
                    (click)="exitPrintSingle()"
                >
                    <tra slug="abed_invig_exit_print"></tra>
                </button>
                <div *ngFor="let question of questions; let questionIndex = index">
                    <div *ngFor="let responseGroup of question.responseGroups; let respgroupIndex = index" >
                        <div *ngFor="let response of responseGroup.responses; let respIndex = index">
                            <div [class.enforce-new-page]="notOnLastPage(respIndex, respgroupIndex, questionIndex)">
                                <div>
                                    {{question.auto_increment}}: <tra slug="abed_invig_student_name"></tra>&nbsp;{{ getResponseStudentName(response) }} ({{getResponseStuGovId(response)}})
                                </div>
                                <div class="is-dbl-spaced-print">
                                    <question-runner 
                                        [currentQuestion]="question.itemDisplay" 
                                        [questionState]="responseGroup.responseState"
                                        [isSkipAnimatedEntry] = "true"
                                        [isSubmitted]="true" 
                                        [isPrintMode]="isQuestionPrintMode(question)"
                                    ></question-runner>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-container>
        </div>
        <!-- <pre *ngIf="false"> currentViewMode: {{currentViewMode}}</pre> -->
        <div *ngIf="isPerusalEnabled && !isPrintView" [ngSwitch]="currentViewMode" class="frame-to-window">
            <div class="reports-body"> 
                <!-- <button class="button" (click)="loadSummaryReport()" *ngIf="this.currentViewMode == ViewMode.ALL">Summary Report</button> -->
                <div *ngSwitchCase="ViewMode.ALL">
                    <div *ngIf="showFormSelection()">
                        <span *ngFor="let design of test_designs; let formIndex = index">
                            <button class="button is-small" 
                            [class.is-selected]="selectedTestDesign == design.test_design_id" 
                            (click)="selectForm(design)">
                                Form {{formIndex + 1}}
                            </button>
                        </span>
                    </div>
                    <div style="padding:1em;">
                        <div *ngIf="isPerusalEnabled" class="notification">
                            <span>
                                <a routerLink="/{{currentLang()}}/test-auth/shared-test-version/-1/{{test_design_id}}/ts/{{test_session_id}}" [queryParams]="{isPeruseTeacher:1}" target="_blank">
                                  <tra slug="abed_sr_peruse_link"></tra>
                                </a>
                                <span *ngIf="perusalDate">
                                  &nbsp;
                                  <tra slug="lbl_accessible"></tra>
                                  {{perusalDate}}
                                </span>.
                            </span>
                        </div>
                        <div>
                          <span>
                              <!-- <a (click)="showQuestionModal()"></a> -->
                              Click on the questions in the header to view student responses.
                          </span>
                        </div>
                    </div>
                    <table class="student-results" *ngIf="!isScoreEntrySession">
                        <tr>
                            <th><tra [slug]="getStudentIdent()"></tra></th>
                            <th>
                                <div>
                                    <tra slug="abed_invig_name_th"></tra>&nbsp;
                                    <button class="button is-small" (click)="toggleStudentNames()">
                                        <span class="icon">
                                            <i *ngIf="isStudentNames()" class="fa fa-eye-slash"></i>
                                            <i *ngIf="!isStudentNames()" class="fa fa-eye"></i>
                                        </span>
                                    </button>
                                </div>
                                <!-- <button class="button is-small"></button> -->
                            </th>
                            <th title="percentage correct" class="student-score-perc">
                                <tra slug="abed_invig_score_th"></tra>
                            </th>
                            <th title="questions accessed" class="student-completion">
                                <i aria-hidden="true" class="fas fa-chart-pie"></i>
                            </th>
                            <th *ngFor="let question of questions" class="question-header" (click)="viewQuestion(question.id)" style="text-align:center">
                                {{question.auto_increment}}
                                <span *ngIf="isLocalScoreEnabled">
                                    {{getQuestionPoints(question)}}
                                </span>
                            </th>
                        </tr>
                        <tr *ngFor="let student of students">
                            <td>
                                <a *ngIf="isStudentNames()">
                                    {{student.student_gov_id}}
                                </a>
                                <span *ngIf="!isStudentNames()">
                                    *********
                                </span>
                            </td>
                            <td>
                                <span *ngIf="isStudentNames()" >
                                    {{student.first_name}} {{student.last_name}}
                                </span>
                                <span *ngIf="!isStudentNames()" style="color:#999">
                                    <i class="fa fa-eye-slash"></i> <tra slug="abed_invig_hidden_name"></tra>        
                                </span>
                            </td>
                            <ng-container [ngSwitch]="(!student.is_submitted && !isLocalScoreEnabled)">
                                <td *ngSwitchCase="false" class="student-score-perc">
                                    {{(!student.scoreComplete && isHidePartialScore)? "Score Not Completed": student.percScore + "%" }}
                                </td>
                                <td *ngSwitchCase="true" class="student-score-perc">Not Submitted</td>
                            </ng-container>
                            
                            <td class="student-completion">{{student.percComplete}}%</td>
                            <ng-container *ngFor="let question of questions" [ngSwitch]="getStudentQuestionCorrect(student, question)">
                                <ng-container *ngIf="question.isPrintMode">
                                    <ng-container *ngIf="isLocalScoreEnabled && isQuestionLocalScoreEnabled(question.id)">
                                        <td 
                                            (click)="viewQuestion(question.id, student)" 
                                            class="student-score"
                                            [class.is-nr]="!getStudentQuestionMarked(student, question)"
                                        > 
                                            <tra slug="{{getStudentQuestionScoreDisplay(student, question)}}"></tra>
                                        </td>
                                    </ng-container>
                                    <ng-container *ngIf="!isLocalScoreEnabled || !isQuestionLocalScoreEnabled(question.id)">
                                        <td *ngSwitchCase="'YES'" (click)="viewQuestion(question.id)" class="student-score is-good"> <i class="fas fa-check"></i> </td>
                                        <td *ngSwitchCase="'NO'"  (click)="viewQuestion(question.id)" class="student-score is-good"> <i class="fas fa-check"></i> </td>
                                        <td *ngSwitchCase="'NR'"  (click)="viewQuestion(question.id)" class="student-score is-nr"> </td>
                                    </ng-container>
                                </ng-container>
                                <ng-container *ngIf="!question.isPrintMode">
                                    <ng-container *ngIf="isLocalScoreEnabled && isQuestionLocalScoreEnabled(question.id)">
                                        <td 
                                            (click)="viewQuestion(question.id, student)" 
                                            class="student-score"
                                            [class.is-nr]="!getStudentQuestionMarked(student, question)"
                                        > 
                                            <tra slug="{{getStudentQuestionScoreDisplay(student, question)}}"></tra>
                                        </td>
                                    </ng-container>
                                    <ng-container *ngIf="!isLocalScoreEnabled || !isQuestionLocalScoreEnabled(question.id)">
                                        <td *ngSwitchCase="'YES'" (click)="viewQuestion(question.id)" class="student-score is-good"> <i class="fas fa-check"></i> </td>
                                        <td *ngSwitchCase="'NO'"  (click)="viewQuestion(question.id)" class="student-score is-bad"> <i class="fas fa-times"></i> </td>
                                        <td *ngSwitchCase="'NR'"  (click)="viewQuestion(question.id)" class="student-score is-nr"> </td>
                                    </ng-container>
                                </ng-container>
                            </ng-container>
                        </tr>
                    </table>
                    <!-- <table class="score-entry-table" *ngIf="isScoreEntrySession">
                        <tr>
                            <th><tra [slug]="getStudentIdent()"></tra></th>
                            <th>
                                <div>
                                    <tra slug="abed_invig_name_th"></tra>&nbsp;
                                    <button class="button is-small" (click)="toggleStudentNames()">
                                        <span class="icon">
                                            <i *ngIf="isStudentNames()" class="fa fa-eye-slash"></i>
                                            <i *ngIf="!isStudentNames()" class="fa fa-eye"></i>
                                        </span>
                                    </button>
                                </div>
                            </th>
                            <th *ngFor="let section of sectionRef.values()" class="question-header" style="text-align:center">
                                {{section.caption}}
                                <span *ngIf="isLocalScoreEnabled">
                                    {{getQuestionPoints(questionRef.get(section.questions[0]))}}
                                </span>
                            </th>
                            <th title="percentage correct" class="student-score-perc">
                                <tra slug="abed_invig_score_th"></tra>
                            </th>
                        </tr>
                        <tr *ngFor="let student of students">
                            <td>
                                <a *ngIf="isStudentNames()" (click)="loadIndividualReport(student)">
                                    {{student.student_gov_id}}
                                </a>
                                <span *ngIf="!isStudentNames()">
                                    *********
                                </span>
                            </td>
                            <td>
                                <span *ngIf="isStudentNames()" >
                                    {{student.first_name}} {{student.last_name}}
                                </span>
                                <span *ngIf="!isStudentNames()" style="color:#999">
                                    <i class="fa fa-eye-slash"></i> <tra slug="abed_invig_hidden_name"></tra>        
                                </span>
                            </td>
                            <ng-container *ngFor="let section of sectionRef.values()" [ngSwitch]="getStudentQuestionCorrect(student, questionRef.get(section.questions[0]))">
                                <td *ngSwitchCase="'YES'" (click)="viewQuestion(section.questions[0])" class="student-score is-good"> <i class="fas fa-check"></i> </td>
                                <td *ngSwitchCase="'NO'"  (click)="viewQuestion(section.questions[0])" class="student-score is-bad"> <i class="fas fa-times"></i> </td>
                                <td *ngSwitchCase="'NR'"  (click)="viewQuestion(section.questions[0])" class="student-score is-nr"> </td>
                            </ng-container>
                            <td class="student-score" [ngClass]="{'is-good': student.percScore >= student.percCutOff, 'is-bad': student.percScore < student.percCutOff}">
                                {{student.percScore}}%
                            </td>
                            
                        </tr>
                        <tr>
                            <td [colSpan]="2">TOTAL</td>
                            <ng-container *ngFor="let section of sectionRef.values()" [ngSwitch]="getQuestionTotalCorrect(questionRef.get(section.questions[0]))">
                                <td *ngSwitchCase="'YES'" class="student-score is-good"> <i class="fas fa-check"></i> </td>
                                <td *ngSwitchCase="'NO'" class="student-score is-bad"> <i class="fas fa-times"></i> </td>
                            </ng-container>
                            <ng-container [ngSwitch]="getScoreTotalCorrect()">
                                <td *ngSwitchCase="'YES'" class="student-score is-good">
                                    {{totalStudentScore / students.length}}%
                                </td>
                                <td *ngSwitchCase="'NO'" class="student-score is-bad">
                                    {{totalStudentScore / students.length}}%
                                </td>
                            </ng-container>
                        </tr>
                    </table> -->
                </div>
                <div *ngSwitchCase="ViewMode.QUESTION">
                    <div class="question-focus">
                        <div class="question-display" *ngIf="getCurrentQuestion()">
                            <ng-container *ngIf="getCurrentQuestion().itemDisplay && currentResponseState && !isResettingQuestion">
                                <div>
                                    <span *ngIf="isCurrentQuestionPrintMode()" style="margin-right:2em;">
                                        <button 
                                            class="button is-small is-info" 
                                            (click)="sendResponseToPrint()"
                                            *ngIf="isCurrentSelectedResponseGroup()"
                                        >
                                            <tra slug="abed_invig_print_btn"></tra>
                                        </button>
                                    </span>
                                    <span>
                                        <!-- todo:trans dedicated slugs instead of shared? -->
                                        <button (click)="fontSizeIncr()" class="button is-light is-small"><tra slug="ncr_table_increate_font"></tra></button>
                                        <button (click)="fontSizeDecr()" class="button is-light is-small"><tra slug="ncr_table_decrease_font"></tra></button>
                                        <span style="position: relative; top: 0.3em;">{{getCurrentFontSizePerc()}}</span>
                                    </span>
                                </div>
                                <div *ngIf="currentResponseState">
                                    <small>Updated on: {{currentResponseState.updated_on}}</small>
                                </div>
                                <div class="is-dbl-spaced-print" [style.font-size.em]="getCurrentFontSize()">
                                    <question-runner 
                                        [currentQuestion]="getCurrentQuestion().itemDisplay" 
                                        [questionState]="currentResponseState"
                                        [isSkipAnimatedEntry] = "true"
                                        [isSubmitted]="true" 
                                        [isPrintMode]="isCurrentQuestionPrintMode()"
                                    ></question-runner>
                                </div>
                            </ng-container>
                        </div>
                        <div class="question-responses">
                            <div class="student-response-header">
                                <span><tra slug="abed_invig_student_resps"></tra></span>
                                <button class="button is-small" (click)="toggleStudentNames()">
                                    <span class="icon">
                                        <i *ngIf="isStudentNames()" class="fa fa-eye-slash"></i>
                                        <i *ngIf="!isStudentNames()" class="fa fa-eye"></i>
                                    </span>
                                    <span><tra slug="abed_invig_student_names_btn"></tra></span>
                                </button>
                            </div>
                            <div>
                                <table *ngIf="getCurrentQuestion()">
                                    <tr 
                                        *ngFor="let responseGroup of getCurrentQuestion().responseGroups; let studentIndex = index" 
                                        (click)="renderResponseGroup(responseGroup)"
                                        class="response-group"
                                        [class.is-selected-response-group]="isSelectedResponseGroup(responseGroup)"
                                    >
                                        <td *ngIf="!isCurrentQuestionPrintMode()" class="formatted-response-display">
                                            <div>{{responseGroup.responseText}}</div>
                                        </td>
                                        <ng-container>
                                            <ng-container *ngIf="isCurrentQuestionPrintMode()">
                                                <ng-container *ngIf="isLocalScoreEnabled">
                                                    <td>
                                                        <ng-container *ngIf="isCurrentQuestionPrintMode()">
                                                            <tra slug="lbl_osslt_response"></tra>&nbsp;{{studentIndex+1}}
                                                        </ng-container>
                                                    </td>
                                                </ng-container>
                                                <ng-container *ngIf="!isLocalScoreEnabled"> 
                                                    <!-- PWR -->
                                                    <td>
                                                        <ng-container *ngIf="isCurrentQuestionPrintMode() || isCurrentQuestionLocalScoreEnabled()">
                                                            <tra slug="lbl_osslt_response"></tra>&nbsp;{{studentIndex+1}}
                                                        </ng-container>
                                                        <ng-container *ngIf="!isCurrentQuestionPrintMode()">
                                                            <div class="freq-indicator" >
                                                                <div class="freq-bar" [style.width.em]="getFreqBarWidthEn(responseGroup.responses.length)"></div>
                                                                <span>{{responseGroup.responses.length}}</span>
                                                                <span> <tra slug="abed_invig_student_set"></tra> </span>
                                                            </div>
                                                        </ng-container>
                                                    </td>
                                                </ng-container>
                                            </ng-container>
                                            <ng-container *ngIf="!isCurrentQuestionPrintMode() && !isCurrentQuestionLocalScoreEnabled()">
                                                <td>
                                                    <div class="freq-indicator" >
                                                        <div class="freq-bar" [style.width.em]="getFreqBarWidthEn(responseGroup.responses.length)"></div>
                                                        <span>{{responseGroup.responses.length}}&nbsp;</span>
                                                        <span> <tra slug="abed_invig_student_set"></tra> </span>
                                                    </div>
                                                </td>
                                                <td 
                                                    class="student-score "
                                                    [class.is-good]="responseGroup.isCorrect"
                                                    [class.is-bad]="!responseGroup.isCorrect"
                                                    [class.is-nr]="false"
                                                > 
                                                    <span>
                                                        <i *ngIf="responseGroup.isCorrect" class="fas fa-check"></i>    
                                                        <i *ngIf="!responseGroup.isCorrect" class="fas fa-times"></i>  
                                                    </span>
                                                    <span>
                                                        {{responseGroup.score}}/{{responseGroup.weight}}
                                                    </span>
                                                </td>
                                            </ng-container>
                                        </ng-container>
                                            
                                        <td *ngIf="isStudentNames()">
                                            <div *ngFor="let response of responseGroup.responses">
                                                {{ getResponseStudentName(response) }}
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="question-nav-list">
                        <button 
                            class="button is-small is-dark"
                            (click)="viewTable()"
                        >
                            <tra slug="abed_invig_back_btn"></tra>
                        </button>
                        <button 
                            *ngFor="let question of questions" 
                            class="button is-small"
                            [class.is-info]="question.id == currentQuestionId"
                            (click)="viewQuestion(question.id)"
                        >
                            {{question.auto_increment}}
                        </button>
                    </div>
                </div>
                <!-- <div *ngSwitchCase="ViewMode.SUMMARY">
                    <button class="button" (click)="renderTable()">Back to Overview</button>
                    <summary-report [schoolClassId]="sc_id" [students]="students" [testDesign]="test_designs" [summaryReport]="summaryReport" ></summary-report>
                </div>
                <div *ngSwitchCase="ViewMode.INDIVIDUAL">
                    <button class="button" (click)="renderTable()">Back to Overview</button>
                    <individual-report [schoolClassId]="sc_id" [testSessionId]="test_session_id" [student]="selectedStudent" [testDesigns]="test_designs" [summaryReport]="summaryReport"></individual-report>
                </div> -->
            </div>
            <div class="reports-header">
                <div class="reports-header-breadcrumb">
                    <span class="header-label"><tra slug="abed_invig_stdnt_results"></tra></span>
                    <span class="breadcrumb-separator">/</span>
                    <a href="#/en/educator/classrooms/{{sc_id}}"><tra slug="abed_invig_class"></tra></a>
                    <ng-container *ngSwitchCase="ViewMode.ALL">
                        <span class="breadcrumb-separator">/</span>
                        <span><tra slug="abed_invig_overview_header"></tra></span>
                    </ng-container>
                    <ng-container *ngSwitchCase="ViewMode.INDIVIDUAL">
                        <span class="breadcrumb-separator">/</span>
                        <span>Student</span> <!--Needs translation-->
                    </ng-container>
                    <ng-container *ngSwitchCase="ViewMode.QUESTION">
                        <span class="breadcrumb-separator">/</span>
                        <a (click)="viewTable()"><tra slug="abed_invig_overview_header"></tra></a>
                        <span class="breadcrumb-separator">/</span>
                        <span class="question-indic-display">{{(getCurrentQuestion() || {}).auto_increment}}</span>
                        <button class="button is-small is-light" (click)="navQuestionPrev()"><tra slug="abed_invig_prev"></tra></button>
                        <button class="button is-small is-light" (click)="navQuestionNext()"><tra slug="abed_invig_next"></tra></button>
                    </ng-container>
                </div>
                <div style="display: flex; gap:0.5em; align-items: center;">
                    <ng-container *ngIf="isLocalScoreEnabled">
                        <span>{{getNumItemsMarked()}}/{{localMarkingQuestions.length}} Questions Marked</span>
                        <button (click)="startLocalMarking()" class="button is-small is-warning">Access Local Marking</button>
                    </ng-container>
                    <button *ngIf="isPerusalEnabled" [disabled]="!isBulkPrintEnabled || isScoreEntrySession" (click)="openPrintMulti()" class="button  is-small is-info" >Bulk Print</button>
                    <button *ngIf="isPerusalEnabled" [disabled]="!isExportEnabled" class="button is-small is-primary" (click)="exportResults()"><tra slug="abed_invig_export_csv"></tra></button>
                </div>
            </div>
        </div>
    </ng-container>
    <ng-container *ngIf="isScoring">
        <div class="simple-scoring-panels">
            <div class="scoring-panel" style="min-width: 6em; max-width: 20%; flex-shrink: 0; background-color:#333; border-radius:1em; margin:1em; padding:0.5em;">
                <div 
                    class="response-scoring-row"
                    *ngFor="let responseGroup of getCurrentQuestion().responseGroups; let studentIndex = index" 
                    [class.is-selected]="isSelectedResponseGroup(responseGroup)"
                    (click)="renderResponseGroup(responseGroup)"
                >
                    <div> {{studentIndex + 1}} </div>
                    <div 
                        class="score-indic-block" 
                        [class.is-fully-marked]="isFullyMarkedCached(studentIndex)"
                    ></div>
                </div>
                <button class="button is-small is-warning  is-fullwidth" (click)="isScoring=false">
                    Finish
                </button>
            </div>
            <div class="scoring-panel" style="min-width: 22em; max-width: 50%; flex-shrink: 0;">
                <span>
                    <!-- todo:trans dedicated slugs instead of shared? -->
                    <button (click)="fontSizeIncr()" class="button is-light is-small"><tra slug="ncr_table_increate_font"></tra></button>
                    <button (click)="fontSizeDecr()" class="button is-light is-small"><tra slug="ncr_table_decrease_font"></tra></button>
                    <span style="position: relative; top: 0.3em;">{{getCurrentFontSizePerc()}}</span>
                </span>
                <ng-container *ngIf="!isResettingQuestion">
                    <div class="is-dbl-spaced-print" [style.font-size.em]="getCurrentFontSize()">
                        <question-runner 
                            [currentQuestion]="getCurrentQuestion().itemDisplay" 
                            [questionState]="currentResponseState"
                            [isSkipAnimatedEntry] = "true"
                            [isSubmitted]="true" 
                            [isPrintMode]="isCurrentQuestionPrintMode()"
                        ></question-runner>
                    </div>
                </ng-container>
                
            </div>
            <div class="scoring-panel" style="min-width: 22em; max-width: 30%; flex-shrink: 0;">
                <ng-container *ngIf="getCurrentQuestion() && currentResponseKey">
                    <div class="buttons" style="justify-content: flex-end; padding-bottom:1em; margin-bottom:1em; border-bottom:1px solid #ccc;">
                        <button (click)="prevLocalMarking()" class="button is-small is-light">Previous Student</button>
                        <button (click)="nextLocalMarking()" class="button is-small is-light">Next Student</button>
                    </div>
                <!-- Student Accommodations -->
                <strong>Accommodations</strong>
                <div *ngIf="getCurrentAccommodationList().length == 0">None</div>
                <div style="max-height: 100px; overflow-y: scroll;">
                    <ng-container *ngFor="let acc of getCurrentAccommodationList()">
                        <div>
                        <ng-container *ngIf="acc.type == 'checkbox'">
                            <input [id]="acc.accommodation_slug" [disabled]="true" type="checkbox" [(ngModel)]="acc.value" />&nbsp;
                        </ng-container>
                        <label [for]="acc.accommodation_slug">
                            <b><tra [slug]="acc.name"></tra></b>
                        </label>&nbsp;
                        <ng-container *ngIf="acc.has_extra_notes">
                            <input type="text" [disabled]="true" [(ngModel)]="acc.extra_notes_value" />
                        </ng-container>
                        </div>
                    </ng-container>
                </div>
                    <div class="space-between" style="padding-left: 1em;">
                        <div style="font-size:1.25em;">
                            <strong>{{getCurrLocalMarkingCaption()}}</strong>
                        </div>
                        <div style="padding:1em; display:flex; flex-direction:column; gap:0.5em;">
                            <ng-container *ngFor="let flag of getCurrLocalMarkingFlags()">
                                <button 
                                    [class.is-danger]="isFlagSelected(flag)"
                                    (click)="saveFlag(flag)"
                                    [disabled]="isSavingMark"
                                    class="button is-small" 
                                >
                                    <tra slug="{{flag.caption}}"></tra>
                                </button>
                            </ng-container>
                        </div>
                    </div>
                    <div style="padding:1em">
                        <div *ngFor="let scale of getCurrLocalMarkingScales()" style="margin-top:1em;">
                            
                                <div>
                                    {{scale.caption}}
                                </div>
                                <div class="buttons score-options" style="padding-left:1em;">
                                    <ng-container *ngFor="let scoreOption of scale.scoreOptions">
                                    <button 
                                        class="button" 
                                        [disabled]="isSavingMark"
                                        *ngIf="checkScoreOptions(scoreOption.scoreCode)"
                                        [class.is-info]="isMarkSelected(scale.scaleCode, scoreOption)"
                                        (click)="saveMark(scale.scaleCode, scoreOption)"
                                    >
                                        {{displayScale(scoreOption.scoreCode)}}
                                    </button>
                                    </ng-container>
                                </div>
                            
                        </div>
                    </div>
                </ng-container>
            </div>
        </div>
        <div class="reports-header">
            <div class="reports-header-breadcrumb">
                <span class="header-label"><tra slug="abed_invig_stdnt_results"></tra></span>
                <span class="breadcrumb-separator">/</span>
                <a href="#/en/educator/classrooms/{{sc_id}}"><tra slug="abed_invig_class"></tra></a>
                <span class="breadcrumb-separator">/</span>
                <a (click)="viewTable()"><tra slug="abed_invig_overview_header"></tra></a>
                <span class="breadcrumb-separator">/</span>
                <span>Local Marking</span>
                <select style="margin-left:1em;" [(ngModel)]="currentQuestionId" (change)="selectFirstResponse()">
                    <option *ngFor="let question of localMarkingQuestions" [value]="question.id" >
                        {{question.auto_increment}}
                    </option>
                </select>
                <!-- <button class="button is-small is-light" (click)="navQuestionPrev()"><tra slug="abed_invig_prev"></tra></button>
                <button class="button is-small is-light" (click)="navQuestionNext()"><tra slug="abed_invig_next"></tra></button> -->
                <div *ngIf="showFormSelection()" style="margin-left:1em;">Form {{getCurrentFormId()}}</div>
                <div *ngIf="scoringGuides.length > 0" style="margin-left:1em;">
                    <button class="button is-small" (click)="guideModalStart()">
                        <tra slug="lbl_scoring_guide"></tra>
                    </button>
                </div>
                
            </div>
            <div> </div>
        </div>
    </ng-container>
</ng-container>
<div class="custom-modal" *ngIf="cModal()">
    <div class="modal-contents" style="min-width: 60vw; height: 90vh; overflow: hidden;">
        <h2><tra slug="lbl_scoring_guide"></tra></h2>
        <menu-bar [menuTabs]="scoringGuides" [tabIdInit]="scoringGuides[0].id" (change)="selectScoringGuide($event)"></menu-bar>
        <iframe 
        [src]="selectedScoringGuide.src"
        style="height: 100%; width: 100%"
        ></iframe>
    </div>
    <modal-footer [pageModal]="pageModal" [confirmButton]="false" closeMessage="btn_close"></modal-footer>
</div>
