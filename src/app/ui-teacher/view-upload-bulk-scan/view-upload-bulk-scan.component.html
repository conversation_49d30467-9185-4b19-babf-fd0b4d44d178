<div class="page-body is-offwhite">
  <div>
    <header
      [breadcrumbPath]="breadcrumb"
      [hasSidebar]="true"
    ></header>
    <div style="padding:4em; padding-top:1em;">

      <div style="margin-bottom: 1em">
        <button class="button is-light" [routerLink]="getInvigPageLink()" [queryParams]="currentQueryParams">
          <i class="fas fa-arrow-left has-text-grey-light"></i>&nbsp;&nbsp;
          <tra slug="lbl_invig_return"></tra>
        </button>
      </div>
      
      <div class="card dashboard-card full-width" style="padding:2em;">

        <div class="columns" style="max-width: 600px">
          <div class="column is-narrow" style="padding-right: 2em">
            <h2><tra slug="educ_bulk_scan_upload_title"></tra></h2>
            <p><tra slug="educ_bulk_scan_upload_instr"></tra></p>
          </div>
          <div class="column is-narrow">            
            <button 
              class="button is-medium is-rounded is-link"
              (click)="onUploadBtnclick()"
              [disabled]="false"
              style="vertical-align: middle"
            >
              <tra slug="btn_upload"></tra>
            </button>
            <span *ngIf="isUploadUntilDateAvailable()"  class="notification note-type-red">
              <tra slug="Bulk upload available until"></tra>&nbsp;{{formatUploadDate()}}
            </span>
            <input #singleUploadInput style="display: none" type="file" accept=".pdf" (change)="initBulkUpload($event.target.files)">
          </div>
        </div>

        <div *ngIf="isFileUploading" class="notification is-warning">
          <tra-md slug="educ_bulk_scan_upload_file_loading_msg"></tra-md>
        </div>

        <div *ngIf="isLastUploadStatus(BULK_SCAN_STATUS.IN_PROGRESS)" class="notification is-info is-light">
          <tra-md slug="educ_bulk_scan_start_msg"></tra-md> 
        </div>

        <div *ngIf="isLastUploadStatus(BULK_SCAN_STATUS.IN_QUEUE)" class="notification is-info">
          <tra-md slug="educ_bulk_scan_queue_msg"></tra-md>
        </div>

      </div>

      <div class="card dashboard-card full-width" style="padding:2em; margin-top: 2em">


        <h2 style="padding: 0"><tra slug="educ_bulk_scan_upload_history_title"></tra></h2>

        <div style="display: flex; justify-content: space-between; margin-bottom: 2em">
          <div style="display: flex; align-items: flex-end">
            <div>
              <system-message [slug]="getCustomMessageScanSecuritySlug()"></system-message>
              <div 
                *ngIf="classScanStats.uploaded !== null && classScanStats.expected !==null" 
                class="notification is-light" 
                style="word-wrap: break-word;"
              >
                <b> {{classScanStats.uploaded}} <tra slug="educ_bulk_scan_upload_lbl_num_uploaded"></tra> / {{classScanStats.expected}} <tra [slug]="getNumExpectedSlug()"></tra> </b>
              </div>
            </div>
          </div>
          <div>
            <h3><b><tra-md slug="educ_bulk_scan_upload_guide_title"></tra-md></b></h3>

            <table class="guide-table">
              <tr>
                  <td>
                    <span class="note-type-green"><tra slug="educ_bulk_scan_upload_guide_green"></tra></span><tra slug="lbl_suffix_colon"></tra>&nbsp;<tra slug="educ_bulk_scan_upload_guide_green_desc"></tra>
                  </td>
                  <td>
                    <span class="note-type-yellow"><tra slug="educ_bulk_scan_upload_guide_yellow"></tra></span><tra slug="lbl_suffix_colon"></tra>&nbsp;<tra slug="educ_bulk_scan_upload_guide_yellow_desc"></tra>
                  </td>
              </tr>
              <tr>
                <td>
                  <span class="note-type-blue"><tra slug="educ_bulk_scan_upload_guide_blue"></tra></span><tra slug="lbl_suffix_colon"></tra>&nbsp;<tra slug="educ_bulk_scan_upload_guide_blue_desc"></tra>
                </td>
                <td>
                  <span class="note-type-red"><tra slug="educ_bulk_scan_upload_guide_red"></tra></span><tra slug="lbl_suffix_colon"></tra>&nbsp;<tra slug="educ_bulk_scan_upload_guide_red_desc"></tra>
                </td>
              </tr>
            </table>

          </div>
        </div>



        <div class="scroll-container">
          <table mat-table [dataSource]="uploadHistoryData">
            <ng-container matColumnDef="created_on">
              <th mat-header-cell *matHeaderCellDef ><tra slug="educ_bulk_scan_upload_col_created_on"></tra></th>
              <td mat-cell *matCellDef="let data"> {{renderDate(data.created_on)}} </td>
            </ng-container>
            <ng-container matColumnDef="processing_status">
              <th mat-header-cell *matHeaderCellDef ><tra slug="educ_bulk_scan_upload_col_status"></tra></th>
              <td mat-cell *matCellDef="let data"> 
                <p [ngSwitch]="data.status == BULK_SCAN_STATUS.COMPLETED && isBulkUploadIssues(data)">
                  <tra *ngSwitchCase="true" slug="educ_bulk_scan_status_completed_issues"></tra>
                  <tra *ngSwitchCase="false" [slug]="bulkStatuses[data.status].slug"></tra>
                </p>
                <p class="has-text-grey" *ngIf="data.status == BULK_SCAN_STATUS.IN_PROGRESS">(<tra slug="g9_started_on"></tra> {{renderDate(data.started_on)}})</p>
                <p class="has-text-grey" *ngIf="data.status == BULK_SCAN_STATUS.IN_QUEUE">(#{{data.numInQueue}})</p>
              </td>
            </ng-container>
            <ng-container matColumnDef="bulk_file_url">
              <th mat-header-cell *matHeaderCellDef ><tra slug="educ_bulk_scan_upload_col_file_history"></tra></th>
              <td mat-cell *matCellDef="let data">
                <button class="button is-link is-light is-small is-rounded" (click)="onDownloadFile(data.bulk_file_url)" [tooltip]="data.bulk_file_name"> 
                  <i class="fas fa-download"></i>
                </button>
              </td>
            </ng-container>
            <ng-container matColumnDef="uploaded_by">
              <th mat-header-cell *matHeaderCellDef ><tra slug="educ_bulk_scan_upload_col_uploaded_by"></tra></th>
              <td mat-cell *matCellDef="let data">
                <p>{{data.uploader_name}}</p>
                <p>({{data.uploader_email}})</p>
              </td>
            </ng-container>
            <ng-container matColumnDef="num_scans_uploaded">
              <th mat-header-cell *matHeaderCellDef ><tra slug="educ_bulk_scan_upload_col_num_uploaded_num_pages"></tra></th>
              <td mat-cell *matCellDef="let data"> {{data.num_scans_uploaded}} / {{ data.num_input_pages ? data.num_input_pages : '?'}} </td>
            </ng-container>
            <ng-container matColumnDef="notes">
              <th mat-header-cell *matHeaderCellDef ><tra slug="educ_bulk_scan_upload_col_notes"></tra></th>
              <td mat-cell *matCellDef="let data"> 
                <div *ngIf="data.status == BULK_SCAN_STATUS.COMPLETED && !isBulkUploadIssues(data)" class="notification note-type-green">
                  <tra [slug]="getNoIssuesSlug()"></tra>
                </div>
                <div *ngIf="data.scan_data?.num_scans_override?.length" class="notification note-type-blue" style="margin: 0; padding: 1em">
                  <div><tra-md [slug]="getScanOverrideSlug()"></tra-md></div>
                  <div>
                    <div *ngFor="let scanNum of data.scan_data.num_scans_override" class="has-text-centered">
                      <tra slug="lbl_page"></tra> {{ scanNum }}
                    </div>
                  </div>
                </div>
                <div *ngIf="data.scan_data?.num_scans_invalid?.length" class="notification note-type-yellow" style="margin: 0; padding: 1em">
                  <div><tra-md slug="pj_scan_lbl_invalid_scans"></tra-md></div>
                  <div>
                    <div *ngFor="let scan of data.scan_data.num_scans_invalid" class="has-text-centered">
                      <tra slug="lbl_page"></tra> {{ scan.num }}
                      <span *ngIf="scan.is_student_invalid"> - <tra slug="pj_scan_lbl_is_student_invalid_alt"></tra></span>
                      <span *ngIf="scan.is_session_invalid"> - <tra slug="pj_scan_lbl_is_session_invalid_alt"></tra></span>
                    </div>
                  </div>
                </div>
                <div *ngIf="data.scan_data?.num_scans_with_errors?.length" class="notification note-type-red" style="margin: 0; padding: 1em">
                  <div><tra-md slug="pj_scan_lbl_not_processed"></tra-md></div>
                  <div>
                    <div *ngFor="let scanNum of data.scan_data.num_scans_with_errors" class="has-text-centered">
                      <a 
                        class="has-text-link" 
                        (click)="onExtractFailedPages(data, [scanNum])"
                      >
                        <tra slug="lbl_page"></tra> {{ scanNum }}
                      </a>
                    </div>
                  </div>
                  <div><tra slug="pj_scan_lbl_not_processed_instruction"></tra></div>
                  <div class="mt-2">
                    <button 
                      class="button is-small is-link" 
                      [class.is-loading]="isExtractingFailedPages"
                      (click)="onExtractFailedPages(data)"
                      [disabled]="isExtractingFailedPages"
                    >
                      <i class="fas fa-download"></i>&nbsp;
                      <tra slug="pj_scan_extract_failed_pages"></tra>
                    </button>
                  </div>
                </div>
  
  
              </td>
            </ng-container>
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let rows; columns: displayedColumns;"></tr>
          </table>
        </div>


      </div>
    </div>
  </div>
</div>