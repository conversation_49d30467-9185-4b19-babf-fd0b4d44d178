import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { SidepanelService } from '../../core/sidepanel.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { ActivatedRoute, Router } from '@angular/router';
import { LangService } from '../../core/lang.service';
import { G9DemoDataService } from '../../ui-schooladmin/g9-demo-data.service';
import { Subscription } from 'rxjs';
import { ClassroomsService } from 'src/app/core/classrooms.service';
import { AuthService } from 'src/app/api/auth.service';
import { ScanInfoService } from '../scan-info.service';
import { RoutesService } from 'src/app/api/routes.service';
import { mtz } from '../../core/util/moment';
import { downloadFile } from '../../core/download-string';
import { OnlineOrPaperService } from 'src/app/ui-testrunner/online-or-paper.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { calculateDateEnd } from '../../core/util/date';

const REFRESH_INTERVAL_MS = 1000 * 60 * 1 // 1 minute

export enum BULK_SCAN_STATUS {
  IN_QUEUE = 'IN_QUEUE', 
  IN_PROGRESS = 'IN_PROGRESS', 
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR'
}

export const bulkStatuses = {
  [BULK_SCAN_STATUS.IN_PROGRESS]: {slug: "educ_bulk_scan_status_in_progress", cellColor: 'lightyellow'},
  [BULK_SCAN_STATUS.COMPLETED]: {slug: "educ_bulk_scan_status_completed", cellColor: 'lightgreen'},
  [BULK_SCAN_STATUS.IN_QUEUE]: {slug: "educ_bulk_scan_status_in_queue", cellColor: '#d1eeff'},
  [BULK_SCAN_STATUS.ERROR]: {slug: "educ_bulk_scan_status_error", cellColor: 'lightpink'}
}

@Component({
  selector: 'view-upload-bulk-scan',
  templateUrl: './view-upload-bulk-scan.component.html',
  styleUrls: ['./view-upload-bulk-scan.component.scss']
})
export class ViewUploadBulkScanComponent implements OnInit {

  constructor(
    private sidePanel: SidepanelService,
    public loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private route: ActivatedRoute,
    private lang: LangService,
    private g9demoService: G9DemoDataService,
    private classroomService: ClassroomsService,
    private auth: AuthService,
    private scanInfo: ScanInfoService,
    private routes: RoutesService,
    private onlineOrPaper: OnlineOrPaperService,
    private whiteLabel: WhitelabelService
  ) { }

  @ViewChild('singleUploadInput', { static: false }) singleUploadInput: ElementRef<HTMLInputElement>;

  
  private classroomServiceSub: Subscription = null;
  
  bulkStatuses = bulkStatuses
  BULK_SCAN_STATUS = BULK_SCAN_STATUS;

  currentQueryParams;
  routeSub;
  breadcrumb = [];

  session_id;
  classroomId;
  activeClassroom;
  studentScanInfoMap;
  studentStates;
  studentList;

  classScanStats  = {
    expected: null,
    missing: null,
    uploaded: null,
  }


  isFileUploading: boolean = false;
  isHistoryFirstLoaded: boolean = false;
  isNewProcessInit: boolean = false;

  refreshInterval;

  uploadHistoryData = [];

  displayedColumns = ['created_on', 'processing_status', 'bulk_file_url', 'uploaded_by', 'num_scans_uploaded', 'notes'];

  isExtractingFailedPages = false;

  async ngOnInit(): Promise<void> {
    this.sidePanel.activate();
    this.loginGuard.activate();
    this.route.queryParams.subscribe((queryParams) => {
      const isSchlAdmin = !!queryParams['isSchlAdmin'];
      if (isSchlAdmin){
        this.g9demoService.setIsFromSchoolAdmin(true)
      } else {
        this.g9demoService.setIsFromSchoolAdmin(false)
      }
      this.currentQueryParams = {
        school: queryParams['school'],
        isSchlAdmin: queryParams['isSchlAdmin']
      }
    });
    this.classroomServiceSub = this.classroomService.sub().subscribe(data => {
      if (data) {
        this.loadRoute();
        this.loadStudentList().then(() => {
          // Continiously refresh data to monitor scan upload progress
          this.loadBulkHistoryAndScanData();
          if (this.refreshInterval){
            clearInterval(this.refreshInterval)
          }
          this.refreshInterval = setInterval(() => {
            this.loadBulkHistoryAndScanData();
          }, REFRESH_INTERVAL_MS)
        });
        
      }
    })
  }

  ngOnDestroy(){
    if (this.refreshInterval){
      clearInterval(this.refreshInterval)
    }
  }


  loadRoute(fromInit:boolean = false) {
    if (this.routeSub) { this.routeSub.unsubscribe(); }
      this.routeSub = this.route.params.subscribe(async (params) => {
        this.session_id = params['asmtSessionId'];
        this.classroomId = params['classroomId'];
        this.sidePanel.classroomId = this.classroomId;
        this.activeClassroom = this.g9demoService.teacherClassrooms.map[this.classroomId];
        this.updateBreadCrumbs();
    });
  }


  /** Get link to return to the invigilation page */
  getInvigPageLink(){
    return `/${this.lang.c()}/educator/assessment/${this.classroomId}/${this.session_id}`
  }

  /** Set the breadcrumb path (slightly different if viewing as school admin or teacher) */
  updateBreadCrumbs() {
    const classroomName = this.g9demoService.getClassroomNameById(this.classroomId)
    const activeSession = this.g9demoService.getSessionByClassroomId(this.classroomId, this.session_id);
    const asmtName = activeSession?.name || this.lang.tra('lbl_assessment_session');

    const basePath = `/${this.lang.c()}/educator`;
    const adminDashboardRoute = `/${this.lang.c()}/school-admin/dashboard`
    const adminSessionsRoute = `/${this.lang.c()}/school-admin` + '/sessions'
    const assessmentRoute = basePath + '/assessment/' + this.classroomId + '/' + this.session_id
    const bulkScanRoute = assessmentRoute + '/bulk-scan'

    if(this.g9demoService.getIsFromSchoolAdmin()){
      this.breadcrumb = [
        this.breadcrumbsService._CURRENT(this.lang.tra('sa_dashboard_school_admin'), adminDashboardRoute, this.currentQueryParams),
        this.breadcrumbsService._CURRENT(this.lang.tra('g9_sessions'), adminSessionsRoute, this.currentQueryParams),
        this.breadcrumbsService._CURRENT(asmtName, assessmentRoute, this.currentQueryParams),
        this.breadcrumbsService._CURRENT(this.lang.tra('lbl_response_sheet_upload'), bulkScanRoute, this.currentQueryParams),
      ];
    } else{
      this.breadcrumb = [
        this.breadcrumbsService._CURRENT(this.lang.tra('lbl_classes'), basePath, this.currentQueryParams),
        this.breadcrumbsService._CURRENT(classroomName, basePath + '/classrooms/' + this.classroomId, this.currentQueryParams),
        this.breadcrumbsService._CURRENT(asmtName, assessmentRoute, this.currentQueryParams),
        this.breadcrumbsService._CURRENT(this.lang.tra('lbl_response_sheet_upload'), bulkScanRoute, this.currentQueryParams),
      ];
    }  
  }


  /** Recalculate number uploaded, missing, expected in the session */
  calculateScanStats () {
    if(!this.studentScanInfoMap) {
      this. classScanStats  = {
        expected: null,
        missing: null,
        uploaded: null,
      }
      return;
    }
    this.classScanStats  = {
      expected: 0,
      missing: 0,
      uploaded: 0,
    }
    if(this.studentList.length > 0) {
      const students = this.studentList.map(student => {
        const isCrScanDefault = this.g9demoService.getPropVal(student, 'IsCrScanDefault', this.activeClassroom.curricShort);
        const isStudentWritingPaper = this.onlineOrPaper.getPaperVal(isCrScanDefault, this.activeClassroom.curricShort);
        const studentSubSessionsState = this.studentStates[student.uid]['subSessions'];
        return {
          uid: student.id,
          studentSubSessionsState,
          isStudentWritingPaper
        };
      });
      if(students.length > 0) {
        students.forEach( student => {
          this.scanInfo.getMissingUnconfirmedScans(this.studentScanInfoMap, student, _ => {this.classScanStats.missing++;} , _ => {}, _ => {this.classScanStats.expected++;});
        })
      }
      this.classScanStats.uploaded = this.classScanStats.expected - this.classScanStats.missing
    }
  }


  onUploadBtnclick() {
    this.singleUploadInput.nativeElement.value = '';
    this.singleUploadInput.nativeElement.click();
  }


  /** Load latest data for the upload history table, and student scan records */
  async loadBulkHistoryAndScanData(){

    const classroom = this.classroomService.getClassroomById(this.classroomId);
    const isSasn = this.classroomService.isSASNLogin(this.classroomId) ? 1 : 0;
    const classGroupId = classroom.group_id
    const query = {
      test_session_id: this.session_id
      , school_class_id: this.classroomId
      , schl_class_group_id: classGroupId
      , isSasn
    }
    // Get data for the upload history table
    this.uploadHistoryData = await this.auth.apiFind(this.routes.EDUCATOR_RESP_SHEET_UPLOAD_RES_SHEETS, {query})
    if (!this.isHistoryFirstLoaded) this.isHistoryFirstLoaded = true;

    // Get the latest info on student scans to refresh the missing and unconfirmed number
    const studentScanInfo = await this.auth.apiGet(this.routes.EDUCATOR_CLASS_SCAN_INFO, this.session_id, { query: {schl_class_group_id: classGroupId}});
    this.studentScanInfoMap = studentScanInfo.studentScanInfoMap;
    this.calculateScanStats();
  }

  /** Get the student list data for this session's class (used to determine missing and unconfirmed scan numbers)  */
  async loadStudentList (){
    this.studentList = this.g9demoService.getStudentsByClassroomId(this.classroomId).list;
    const classroom = this.classroomService.getClassroomById(this.classroomId);
    const school_class_group_id = classroom.group_id;
    const params = {
      query: {
        school_class_group_id,
        school_class_id:this.classroomId
      }
    }
    const res = await this.auth.apiGet(this.routes.EDUCATOR_SESSION, this.session_id, params)
    this.studentStates = res.subSessions.studentStates;
    this.calculateDateEndForUpload(res);
  }

  /** Disallow new upload if another (latest one) is in progress */
  isUploadBtnDisabled(){
    if (!this.isHistoryFirstLoaded || this.isNewProcessInit || this.isFileUploading) return true;
    if (this.uploadHistoryData.length) {
      const firstRecord = this.uploadHistoryData[0]
      const isFirstRecordInProgress = [BULK_SCAN_STATUS.IN_PROGRESS, BULK_SCAN_STATUS.IN_QUEUE].includes(firstRecord['status'])
      return isFirstRecordInProgress;
    }
    return false;
  }

  async initBulkUpload(files: FileList){
    if(files.length !== 1) {
      return;
    }
    const bulkFile = files.item(0)
    if (bulkFile.type !== 'application/pdf') {
      this.loginGuard.quickPopup('lbl_err_invalid_file_format');
      return;
    }

    this.isFileUploading = true;
    this.auth.uploadFile(bulkFile, `user_scans/uploads/${bulkFile.name}`)
    .then(res => {
      this.isNewProcessInit = true;
      const { filePath } = res;

      if (!filePath) return this.loginGuard.quickPopup(this.lang.tra('ie_error_file_upload'))

      const classroom = this.classroomService.getClassroomById(this.classroomId);
      const isSasn = this.classroomService.isSASNLogin(this.classroomId) ? 1 : 0;
      const classGroupId = classroom.group_id

      this.scanInfo.initSingleFileBulkScan(filePath, this.session_id, isSasn, classGroupId, this.classroomId)
      .then(() => {
        this.loadBulkHistoryAndScanData();
      })
      .finally(() => {
        this.isNewProcessInit = false;
      })

    })
    .catch(err => {
      this.loginGuard.quickPopup(this.lang.tra('ie_error_file_upload'))
    })
    .finally(() => {
      this.isFileUploading = false;
    })

  }

  /** Return if the latest upload has the target status */
  isLastUploadStatus(status:BULK_SCAN_STATUS){
    if (!this.uploadHistoryData.length) return false;
    return this.uploadHistoryData[0].status == status
  }


  /** Display a date in correct format */
  renderDate(inputDate){
    return mtz(inputDate).format(this.lang.tra('datefmt_timestamp'))
  }

  onDownloadFile(fileUrl:string){
    downloadFile(fileUrl)
  }

  /**
   * Does the upload history item include specific issues in the notes?
   * @param uploadHistoryLog - The bulk upload history log object
   */
  isBulkUploadIssues(uploadHistoryLog): boolean {
    return !!(
      uploadHistoryLog.scan_data?.num_scans_override?.length
      || uploadHistoryLog.scan_data?.num_scans_invalid?.length
      || uploadHistoryLog.scan_data?.num_scans_with_errors?.length
    )
  }


  getFileName(fileUrl:string){
    const fileNameSplit = fileUrl.split('/');
    const fileName = fileNameSplit.pop();
    return fileName
  }

  isNBED = () => this.whiteLabel.getSiteFlag('IS_NBED')
  isABED = () => this.whiteLabel.getSiteFlag('IS_ABED')
  isMBED = () => this.whiteLabel.getSiteFlag('IS_MBED')
  isEQAO = () => this.whiteLabel.getSiteFlag('IS_EQAO')

  getScanOverrideSlug(){
    if (this.isABED()) return "abed_scan_lbl_override"
    else return "pj_scan_lbl_override"
  }

  getNumExpectedSlug(){
    if (this.isABED()) {
      return "educ_bulk_scan_upload_lbl_num_expected_abed"
    } else {
      return "educ_bulk_scan_upload_lbl_num_expected"
    }
  }

  getNoIssuesSlug(){
    if (this.isABED()) return "abed_scan_lbl_no_issues"
    else return "pj_scan_lbl_no_issues" 
  }

  getCustomMessageScanSecuritySlug(){
    if(this.g9demoService.getIsFromSchoolAdmin()){
      return 'INVIG_SCAN_SECURITY_MSG_ADMIN'
    } else {
      return 'INVIG_SCAN_SECURITY_MSG_TEACHER'
    }
  }
  
  isUploadUntilDateAvailable() {
    if(this.scanInfo.uploadUntildateEnd){
      return true;
    }
    return false;
  }
  calculateDateEndForUpload(res) {
    if(!res.tw_resp_sheet_config) return;
    const {upload_until_days, exception_dates, is_exclude_weekends} = res.tw_resp_sheet_config;
    const isWeekendsExcluded = !!is_exclude_weekends;
    this.scanInfo.uploadUntildateEnd = calculateDateEnd(res.date_time_start,+upload_until_days,true, exception_dates, isWeekendsExcluded);

  }
  formatUploadDate(){
    if(this.isUploadUntilDateAvailable()){
      return mtz(this.scanInfo.uploadUntildateEnd).format(this.lang.tra('datefmt_dashboard_short'))
    }
  }

  async onExtractFailedPages(data: any, pageNumbers?: number[]) {
    if (!data.scan_data?.num_scans_with_errors?.length) {
      return;
    }

    this.isExtractingFailedPages = true;
    
    this.auth.apiCreate(this.routes.EDUCATOR_RESP_SHEET_UPLOAD_EXTRACT_FAILED_PAGES, { 
      cbsuId: data.id, 
      failedPageNumbers: pageNumbers || data.scan_data.num_scans_with_errors 
    })
      .then(response => {
        if (response?.fileUrl) {
          downloadFile(response.fileUrl);
        }
      })
      .catch(() => {
        this.loginGuard.quickPopup('ERR_EXTRACTING_PAGES');
      })
      .finally(() => {
        this.isExtractingFailedPages = false;
      });
  }

}
