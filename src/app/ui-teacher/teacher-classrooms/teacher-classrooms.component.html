<div class="page-body is-offwhite" [class.is-archive-mode]="isViewingArchive">
  <div>
    <header
      [breadcrumbPath]="breadcrumb"
      [hasSidebar]="true"
    ></header>
    <div style="padding:4em; padding-top:1em;">
      <div *ngIf="activeClassroom">
        <tra-md [slug]="getClassHeader()"></tra-md>
      </div>
      <div class="active-classroom-container" #selectedClassroomPanel [class.is-active]="activeClassroom">
        <div *ngIf="activeClassroom" class="active-classroom">
          <div class="classroom-header">
            <div class="classroom-identity">
              <div *ngIf="!isEditingClassroomName" style="font-size: 1.6em;">
                <span>
                  {{activeClassroom.name}}
                </span>
                <!-- <button *ngIf="activeClassroom.name && !activeClassroom.isAssigned" class="button is-light" (click)="openClassroomNameEditor()">
                  <i class="fas fa-pencil-alt"></i>
                </button> -->
                <span *ngIf="activeClassroom.isAssigned" style="margin: 0 1em; color:green;">
                  <i class="fas fa-star" ></i>
                  <span style="font-size:.6em"><tra [slug]="getTeacherClassSlug()"></tra></span>  <!--This is your class section.-->
                </span>
              </div>
              <div *ngIf="showAddRemoveSchoolTeachersBtn()" style="margin-right: 0.5em; margin-top: 1em;">
                <a (click)="openAddRemoveInvigilatorsModal()">
                  <span><tra slug="pj_add_remove_class_invigilators"></tra></span>
                </a>
                <span style = "font-weight: bold; margin-left: 0.5em;">
                  <tra [slug]="getTAClassInvigilatorsLabel()"></tra>
                </span>&nbsp;{{getClassInvigilators()}}
              </div>
              <div *ngIf="isEditingClassroomName">
                <form [formGroup]="classroomNameForm" (ngSubmit)="changeCurrentClassroomName()">
                  <div>
                    <strong><tra slug="class_section_name"></tra></strong>
                  </div>
                  <input type="text" class="input" formControlName="name">
                  <button type="button" class="button" (click)="closeClassroomNameEditor()"><tra slug="btn_cancel"></tra></button>
                  <input type="submit" class="button is-success" value="Save">
                  <div class="notification" *ngIf="isSavingClassroomName">
                    <tra slug="ie_saving"></tra>
                  </div>
                </form>
              </div>
              <div class="teacher-list" *ngIf="false">
                <tra slug='lead_by'></tra>
                <span *ngFor="let teacher of activeTeachers; first as isFirst; last as isLast">
                  <span *ngIf="isLast && !isFirst"><tra slug="lbl_and"></tra></span>
                  <span>{{teacher.displayName}}</span>
                  <span *ngIf="!isLast">, </span>
                </span>
                <a routerLink="/teacher/classroom/{{classroomId}}/share">
                  [Share]
                </a>
              </div>
            </div>
            <div>
              <class-code-indicator
              [classCode]="activeClassroom.classCode"
              [accessScope]="getAccessScopeSlug()"
              ></class-code-indicator>
            </div>
          </div>
          <div *ngIf="isABED() && false">
            <table class="progress-summary">
              <tr>
                <th>Assessment</th>
                <th>Session</th>
                <th>Scoring</th>
                <th>Reports</th>
              </tr>
              <tr *ngFor="let row of activeClassroom.openAssessments">
                <td>{{row.name}}</td>
                <td><button class="session-link" (click)="getScheduledInvigilateRouteLink(row)">[Start Now]</button></td>
                <td>N/A</td>
                <td>N/A</td>
              </tr>
              <tr *ngFor="let row of activeClassroom.scheduledAssessments">
                <td>{{row.name}}</td>
                <td>{{row.dateTimeStartLong}}</td>
                <td>N/A</td>
                <td>N/A</td>
              </tr>
              <tr *ngFor="let row of activeClassroom.recentAssessments">
                <td>{{row.name}}</td>
                <td>Completed</td>
                <td>N/A</td>
                <td>N/A</td>
              </tr>
            </table>
          </div>
          <hr/>

          <div class="classroom-columns">
            <!-- Ann mock up WIP -->
            <div class="classroom-info-column assessments-column">


              <div class="info-title">
                <span class="icon icon-assignment"></span>
                <span><tra [slug]="getActiveAssessmentSlug()"></tra></span>
              </div>
              <div *ngIf="activeClassroom.openAssessments && activeClassroom.openAssessments.length > 1" class="notification is-small is-danger is-flashing">
                <tra-md slug="txt_multi_session"></tra-md>
              </div>
              <div class="entries-container" >
                <a class="entry-card"
                  *ngFor="let entry of activeClassroom.openAssessments"
                >
                    <a class="assessment"
                      [routerLink]="getInvigilateRouteLink(entry)"
                      [queryParams]="currentQueryParams"
                    >
                      {{entry.name}} 
                      <ng-container *ngIf="hasSessionScanProgress(entry.test_session_id)">
                        <div  class="scan-progress" [class.is-upload-pending]="!isSessionScanProgressComplete(entry.test_session_id)">
                          {{getSessionScanProgressDisplay(entry.test_session_id)}}
                        </div>
                      </ng-container>
                    </a>
                    <div class="date-stamp"> {{entry.timeDirectStart}} </div>
                    <div>
                      <button
                        *ngIf="entry.is_score_entry == 0"
                        (click)="openAssessmentReport(entry)"
                        class="button is-small" 
                        style="margin: 0em; margin-left: 0.5em;"
                      >
                        <tra slug="abed_invig_view_responses"></tra>
                      </button>
                    </div>
                </a>
                <ng-container *ngIf="!isABED()">
                  <a class="entry-card"
                  *ngFor="let entry of activeClassroom.scheduledAssessments"
                  (click)="getScheduledInvigilateRouteLink(entry)">
                    <div class="assessment"> 
                      {{entry.name}}
                    </div>
                    <div *ngIf="!isPJ()" class="date-stamp" style="display: flex">
                      <tra-md
                        slug="lbl_scheduled"
                        [props]="getEntryProps(entry)"
                        [ngStyle]="{'margin': '1em auto 0'}">
                      </tra-md>
                    </div>
                    <div *ngIf="isPJ()" class="date-stamp" style="display: flex">
                      <tra-md
                        slug="lbl_pj_scheduled"
                        [props]="getEntryProps(entry)"
                        [ngStyle]="{'margin': '1em auto 0'}">
                      </tra-md>
                    </div>
                    <div class="date-stamp"> &nbsp; </div>
                </a>
                </ng-container>
              </div>

              <button
                *ngIf="showStartOrScheduleButton()"
                class="button is-success is-outlined is-fullwidth"
                (click)="openNewAssessmentModal()"
                [disabled]="inValidTestWindow() || isPlaceHolder()">
                  <tra [slug]="getStartOrScheduleSlug()"></tra>
              </button>

              <div class="notification is-danger" *ngIf="isPlaceHolder()" style="margin-top:1em">
                You cannot administer a session from a Placeholder class
              </div>
              <div class="notification is-danger" *ngIf="inValidTestWindow()" style="margin-top:1em">
                You cannot start a new session in this grouping as the administration window has closed. If you are looking to administer an assessment today with this group of students, please go back to the groupings page and consider applying "Rollover" on this grouping.
              </div>

              <div *ngIf="isPJ()" style="margin-top: 1em;">
                <tra slug="msg_another_active_session_note_pj"></tra>
              </div>

              <!--Scheduled Assessment(s) -->
              <ng-container *ngIf="activeClassroom.scheduledAssessments && activeClassroom.scheduledAssessments.length">
                <div class="info-title" style="margin: 2rem 0 0 0">
                  <span class="icon icon-assignment"></span>
                  <span><tra slug="abed_scheduled_assessment"></tra></span>
                </div>
                <div class="entries-container" >
                  <a class="entry-card" *ngFor="let entry of activeClassroom.scheduledAssessments"
                    (click)="getScheduledInvigilateRouteLink(entry)">
                    <div class="assessment"> {{entry.name}} </div>
                    <div class="date-stamp" style="display: flex">
                      <tra-md slug="lbl_scheduled" [props]="getEntryProps(entry)" [ngStyle]="{'margin': '1em auto 0'}">
                      </tra-md>
                    </div>
                    <div class="date-stamp"> &nbsp; </div>
                  </a>
                </div>
              </ng-container>

              <!-- Completed Assessment(s) -->
              <div *ngIf="activeClassroom.recentAssessments.length" class="info-title is-mid" >
                <span class="icon icon-assignment"></span>
                <span><tra [slug]="getCompletedAssessmentSlug()"></tra></span>
                <span style="margin-left:1em;" *ngIf="activeClassroom.recentAssessments.length > MAX_COMPLETED_SHOWN">
                  <button class="button is-light is-small" (click)="showNextBatchCompletedAssessments(-1)" [disabled]="numCompletedAssessmentsCap <= MAX_COMPLETED_SHOWN">
                    newer
                  </button>
                  <button class="button is-light is-small" (click)="showNextBatchCompletedAssessments(1)" [disabled]="numCompletedAssessmentsCap >= activeClassroom.recentAssessments.length">
                    older
                  </button>
                </span>
              </div>
              <div class="entries-container" >
                <!-- [routerLink]="" -->
                <div
                  *ngFor="let entry of activeClassroom.recentAssessments.slice(numCompletedAssessmentsCap-MAX_COMPLETED_SHOWN, numCompletedAssessmentsCap)"
                  (click)="openAssessmentReport(entry)"
                  class="entry-card clickable"
                >
                  <div class="assessment"> 
                    {{entry.name}}
                    <ng-container *ngIf="hasSessionScanProgress(entry.test_session_id)">
                      <div  class="scan-progress" [class.is-upload-pending]="!isSessionScanProgressComplete(entry.test_session_id)">
                        {{getSessionScanProgressDisplay(entry.test_session_id)}}
                      </div>
                    </ng-container>
                  </div>
                    <div class="date-stamp">
                      <span style="padding-left: 2.5px;">
                        {{entry.timeDirectStart}} -
                      </span>
                    </div>
                    <div class="date-stamp">
                      <ng-container
                        *ngIf="entry.timeDirectClose">
                        {{entry.timeDirectClose}}
                      </ng-container>
                      <ng-container
                        *ngIf="!entry.timeDirectClose">
                        N/A
                      </ng-container>
                    </div>
                    <button
                        (click)="openAssessmentReport(entry)"
                        class="button is-small" 
                        style="margin: 0em; margin-left: 0.5em;"
                      >
                        <tra slug="abed_invig_view_responses"></tra>
                      </button>
                </div>
              </div>
              <ng-container *ngIf="isReportsEnabled()">
                <a *wlCtxNot="'IS_INVIG_VIEW_REPORTS_DISABLED'" (click)="getReportRouteForClass()" class="button is-info is-outlined is-fullwidth">
                  <tra slug="view_reports"></tra>
                </a>
              </ng-container>
              <br/>
              <div *ngIf="activeClassroom.curricShort == 'EQAO_G9' && activeClassroom.isAssigned">
                <tra slug="report_error_message"></tra>
                <br/><br/>
                <!-- <tra slug="report_error_message5"></tra> -->
              </div>

              </div>

              <div class="classroom-info-column students-column">
                <div class="info-title">
                  <span class="icon icon-student"></span>
                  <span><span><tra slug="lbl_students"></tra></span> ({{getNumStudents()}})</span>
                </div>
                <div>
                  <div *ngFor="let entry of activeStudents()" class="info-text"> {{entry.displayName}} </div>
                  <div *ngIf=" false && getNumStudents() > MAX_STUDENT_DISPLAY" class="info-more">+ {{getNumStudents() - activeStudents().length}} <span><tra slug="lbl_others"></tra></span></div>
                </div>
                <a  [routerLink]="getStudentRouteLink()" [queryParams]="currentQueryParams" class="button is-info is-small is-outlined is-fullwidth">
                  <tra slug="mng_manage_students"></tra>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- <div *ngIf="isArchiving" class="classroom-list-header"><tra slug="active_classrooms"></tra></div> -->

        <div *ngIf="!activeClassroom && isEQAO()">
          <div *ngIf="haveQuestionnaireSession" class="banner-container">
            <div style="display: flex; flex-direction: row;">
              <button class="banner-btn" (click)="onClickQuestionnaire()"><tra slug="lbl_teacher_questionnaire"></tra></button>
              <span *ngIf="reset_flag" style="margin-top:1em ;">
                <tra-md slug ="teacher_questionnaire_complete" [props]="{DUE_DATE: getQuestionnaireDueDate()}"></tra-md>
              </span>
            </div>
            <div style="margin: 0.8em 0.75em 0.5em 0.75em; text-align: center;"><tra slug="lbl_teacher_questionnaire_banner"></tra></div>
          </div>
          <tra-md *ngIf="isPrivateSchool" slug="teacher_dashboard_private"></tra-md>
          <tra-md *ngIf="!isPrivateSchool" slug="teacher_classroom_header"></tra-md>
        </div>
        <div *wlCtx="'IS_LANDING_GUIDE'" style="margin-bottom: 2em; font-size: 1em; width: 36em; margin-top: 1em;">
          <tra [slug]="getGuideSlug()"></tra>&nbsp;
          <a [routerLink]="['/', lang.c(), 'user-guides']" [queryParams]="{ expanded: 'Teacher' }" target="_blank">
            <tra [slug]="getClickText()"></tra>
          </a>
        </div>
        <system-message [slug]="'CUSTOM_MSG_SA_T'"></system-message>
        <div class="space-between">
          <h1>
            <tra slug="my_school"></tra><tra slug="txt_colon"></tra>
            {{schoolName}}
          </h1>
          <div class="calender-container">
            <button *ngIf="!isReportView" class="button is-small" (click)="toggleViewReport()" [disabled]="!isClassroomsLoaded">View Report</button>
            <button *ngIf="isReportView" class="button is-small" (click)="toggleViewReport()">Classrooms</button>
            <div class="select">
              <select [(ngModel)]="selectedAcademicYear" (change)="onSelectedAcademicYear($event)">
                <option *ngFor="let ay of academicYears">{{ay.code}}</option>
              </select>
            </div>
          </div>
          
        </div>
        <!-- If open report page -->
        <ng-container *ngIf="isReportView">
          <div class="buttons">
            <button *ngIf="isReportView" class="button is-small" (click)="downloadScoringReport()" [disabled]="!selectedReport">Download</button>
          </div>
          <ag-grid-angular
              class="ag-theme-alpine ag-grid-fullpage"
              [rowData]="scoringReportRecords"
              [gridOptions]="scoringReportGridOptions"
              [enableCellTextSelection]="true"
              (gridReady)="onGridReady($event)"
              (selectionChanged)="onSelected($event)"
          >
          </ag-grid-angular>
        </ng-container>
        <!-- If not opening report page -->
        <ng-container *ngIf="!isReportView">
          <ng-container *ngFor="let twSlot of testWindowSlots">
            <div *ngIf="selectedAcademicYear == twSlot.academic_year" style="margin-top:2em;">
              <div class="space-between">
                <h3>
                  <strong>{{twNames[twSlot.tw_id]}}</strong>&nbsp;
                  <span *ngIf="twSlot.is_teacher_creation && !isViewingArchive">
                    <button (click)="handleCreateNewClassroom(twSlot.tw_id)" class="button is-small"><tra slug="sa-create-new-grouping-ABED"></tra></button>
                  </span>
                </h3>
                <!-- <div>{{twSlot.academic_year}}</div> -->
              </div>
              <div *ngIf="isRolloverCandidate(twSlot.tw_id)" class="notification is-small">
                <tra-md slug="txt_rollover_instr" [props]="{ROLLOVER_TW_NAME: getRolloverName(twSlot.tw_id)}"></tra-md>
              </div>
              <div class="class-card-container">
                <ng-container *ngFor="let classroom of twSlot.classes">
                  <div *ngIf="isShowClass(classroom)" class="class-card" [class.is-selected]="classroomId == classroom.id">
                    <!-- <ng-container *ngIf="isPrivateSchool && classroom.curricShort === 'EQAO_G10'"> -->
                    <ng-container *ngIf="isPrivateSchool">
                      <div class="class-card-header">
                        <span>
                          <button
                            *ngIf="isRolloverCandidate(twSlot.tw_id)"
                            class="button is-small is-info"
                            (click)="rolloverClass(classroom.id)"
                            [disabled]="isRollingOver"
                          >
                            <tra slug="lbl_rollover"></tra>
                          </button>
                        </span>
                        <div class="tag class-code">
                          {{classroom.classCode}}
                        </div>
                      </div>
                      <div class="class-card-title">
                        {{classroom.name}}
                        <div class="class-card-subtitle">
                          {{ studentsDynamicTranslation(classroom.numStudents) }} <!-- 1 student vs 2 students -->
                          <!-- <div>{{classroom.curricShort}}</div> -->
                        </div>
                      </div>
                      
                      <div class="button-container">
                        <button class="button is-fullwidth btn-select-class" [disabled]="isClickBufferActive" (click)="selectClassroom(classroom)">
                          <span *ngIf="classroom.isAssigned"><tra [slug]="getSelectClassroomLabel(classroom)"></tra></span>
                          <span *ngIf="!classroom.isAssigned"><tra [slug]="getSelectClassroomLabel(classroom)"></tra></span>
                        </button>
                      </div>
                    </ng-container>
                    <ng-container *ngIf="!isPrivateSchool">
                      <div class="class-card-header">
                        <span>
                          <button
                            *ngIf="isRolloverCandidate(twSlot.tw_id)"
                            class="button is-small is-info"
                            (click)="rolloverClass(classroom.id)"
                            [disabled]="isRollingOver"
                          >
                          <tra slug="lbl_rollover"></tra>
                          </button>
                        </span>
                        <span *ngIf="classroom.is_fi == 1" class="fr-imm"><tra slug="sa_lbl_french_immi"></tra></span>
                        <div *ngIf="classroom.classCode" class="tag class-code">
                          {{classroom.classCode}}
                        </div>
                      </div>
                      <div class="class-card-title">
                        {{classroom.name}}
                        <div class="class-card-subtitle">
                          {{ studentsDynamicTranslation(classroom.numStudents) }} <!-- 1 student vs 2 students -->
                          <!-- <div>{{classroom.curricShort}}</div> -->
                        </div>
                      </div>
                      <div class="button-container">

                        <button class="button is-fullwidth btn-select-class" [disabled]="isClickBufferActive" (click)="selectClassroom(classroom)">
                          <span *ngIf="classroom.isAssigned"><tra [slug]="getSelectClassroomLabel(classroom)"></tra></span>
                          <span *ngIf="!classroom.isAssigned"><tra [slug]="getSelectClassroomLabel(classroom)"></tra></span>
                        </button>

                        <button 
                          *ngIf="isArchiving && !isViewingArchive" 
                          class="button is-danger" 
                          [disabled]="isClickBufferActive" 
                          (click)="classroomArchiveToggle(classroom)"
                        >
                          <tra slug="lbl_archive"></tra>
                        </button>

                        <button 
                          *ngIf="isViewingArchive" 
                          class="button is-warning" 
                          [disabled]="isClickBufferActive" 
                          (click)="classroomArchiveToggle(classroom)"
                        >
                          Restore
                        </button>

                      </div>
                      </ng-container>
                  </div>
                </ng-container>

                <div *ngIf="false" class="btn-new-classroom" (click)="openNewClassroomModal()">
                  <i class="icon fa fa-plus" aria-hidden="true"></i>
                  <div class="caption"><tra slug="new_group"></tra> </div>
                </div>
              </div>
            </div>
          </ng-container>
        
          <hr/>

          <div *ngIf="isClassroomsLoaded">
            <h3>Manage Classes</h3>
            <div>
              <mat-slide-toggle [(ngModel)]="isArchiving" >
                Allow Class Archiving ({{isArchiving ? 'Active' : 'Off'}})
              </mat-slide-toggle>
            </div>
            <div>
              <mat-slide-toggle [(ngModel)]="isViewingArchive">
                Show Archived Classes ({{isViewingArchive ? 'On' : 'Off'}})
              </mat-slide-toggle>
            </div>
          </div>
        </ng-container>
    </div>
    <div class="dashboard-overlays" *ngIf="activeOverlay">
      <div class="dashboard-overlay-screen" (click)="closeActiveOverlay()"></div>
      <div class="dashboard-overlay-window">
        <div class="dashboard-overlay-header">
          <div class="overlay-title" *ngIf ='isCreateSessionOverlays()'><tra slug="g9_create_new_assess"></tra></div>
          <div class="overlay-title" *ngIf ='isAddRemoveInvigilatorOverlays()'><tra slug="ta_add_remove_invigilators"></tra></div>
          <div class="overlay-close" (click)="closeActiveOverlay()">
            <i class="fa fa-times" aria-hidden="true"></i>
          </div>
        </div>
        <div class="dashboard-overlay-content">
          <div [ngSwitch]="activeOverlay">
            <div *ngSwitchCase="OVERLAYS.NEW_CLASSROOM" class="dashboard-overlay-account">
              <div class="field is-horizontal" *ngIf="false">
                <div class="field-label is-normal">
                  <label class="label"><tra slug="lbl_curriculum"></tra></label>
                </div>
                <div class="field-body">
                  <div class="field select">
                    <select>
                      <option selected>{{whitelabelService.getSiteText('DEFAULT_CURRICULUM', '(Default Curriculum)')}}</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="field is-horizontal">
                <div class="field-label is-normal">
                  <label class="label"><tra slug="lbl_name"></tra></label>
                </div>
                <div class="field-body">
                  <div class="field">
                    <input
                      class="input"
                      type="text"
                      [formControl]="newClassName"
                      placeholder={{inputValue}}
                    >
                  </div>
                </div>
              </div>
              <div *ngIf="false">
                <button [disabled]="!newClassName.value || isAddingNewClassroom" (click)="createNewClassroom()" class="button is-success is-fullwidth "><tra slug="create_group"></tra></button>
              </div>
            </div>
            <div *ngSwitchCase="OVERLAYS.NEW_ASSESSMENT && !whitelabelService.isABED()" class="dashboard-overlay-account">
              <view-assessment-new
                [activeClassroom]="activeClassroom"
                [isFreeStart]="isFreeStart(classroomId)"
                [classId]="classroomId"
                (getActive)="getActiveSessions($event)"
              ></view-assessment-new>
            </div>
            <div *ngSwitchCase="OVERLAYS.ACTIVE_ASSESSMENT" class="dashboard-overlay-account">
              <view-assessment-active
                [activeSubSessions]="activeSubSessions"
                [completedSubSessions]="completedSubSessions"
                [subSessions]="subSessions"
                [studentList]="activeStudents()"
                (confirmEvent)="createNewAssessmentSession($event)"
                [selectedSession]="config"
                (back)="openNewAssessmentModal()"
              ></view-assessment-active>
            </div>
            <div *ngSwitchCase="OVERLAYS.ASSESSMENT_SCHEDULER" class="dashboard-overlay-account">
              <view-assessment-scheduler
                [state] = "{config: config, sessionDetails: sessionDetails, schoolClassId: activeClassroom.id, isFIClass: activeClassroom.is_fi}"
                (back)="openActiveAssessmentModal()"
                (scheduleEvent)="finalizeAssessmentCreation($event)"
              >
              </view-assessment-scheduler>
            </div>
            <div *ngSwitchCase="OVERLAYS.ADD_REMOVE_INVIGILATORS" class="dashboard-overlay-account">
              <tra-md slug="ta_add_remove_invig_desc"></tra-md>
              <div style = "height:30em; overflow:auto;">
                <table>
                  <tr>
                      <th style="width:2em;"></th>
                      <th><tra slug="ta_teacher_list_name_col"></tra></th>
                      <th><tra slug="ta_teacher_list_email_col"></tra></th>
                  </tr>
                  <tr *ngFor="let teacher of availableTeachers">
                      <td><input type ="checkbox" [checked] = "teacher.selected" (change) = "toggleAddRemoveInvigilator(teacher)"></td>
                      <td>{{teacher.name}}</td>
                      <td>{{teacher.email}}</td>
                  </tr>
                </table>
              </div>
              <button class="button is-info invig-ok-btn" (click)="closeActiveOverlay()"><tra [slug]="'btn_ok'"></tra></button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <footer [hasLinks]="true"></footer>
</div>

<div class="custom-modal" *ngIf="cModal() && whitelabelService.isABED()">
  <div [ngSwitch]="cModal().type" class="modal-contents" style="width:42em;">
    <div>
      <div *ngSwitchCase="SessionModal.NEW_SESSION">
        <tra-md
          class="new-assessment-session"
          slug="new_assessment_sessions_ABED">
        </tra-md>
        <view-new-session-modal [forTeacher]="true" [schoolClassGroupId]="getClassroomGroupId()" [savePayload]="cmc()" saveProp="payload"></view-new-session-modal>
      </div>
      <div *ngSwitchCase="ConfidentialityAgreement.HAVE_NOT_AGREED">
        <confidentiality-agreement [savePayload]="cmc()"></confidentiality-agreement>
      </div>
      <div *ngSwitchCase="ClassroomModal.NEW_CLASS">
        <view-new-class-educator [savePayload]="cmc()"></view-new-class-educator>
      </div>
    </div>
    <modal-footer *ngIf="ClassroomModal.NEW_CLASS || SessionModal.NEW_SESSION" [pageModal]="pageModal"></modal-footer>
    <modal-footer *ngSwitchCase="ConfidentialityAgreement.HAVE_NOT_AGREED" [isEditDisable]="disableConfidentiality()" [pageModal]="pageModal"></modal-footer>
  </div>
</div>
