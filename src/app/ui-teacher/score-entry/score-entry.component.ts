import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { ClassroomsService } from 'src/app/core/classrooms.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { LangService } from 'src/app/core/lang.service';
import { AuthService } from 'src/app/api/auth.service';
import { ChangeDetectorRef } from '@angular/core';
import { ComparisonOperator, comparisonOperators, evaluateComparison, IDiscontActions, IDiscontConditions, IDiscontRuleMap, SCORE_ENTRY_ACTION_TYPES } from './types';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { TQuestionScoreSlugMap } from 'src/app/ui-item-maker/widget-score-entry-settings/types';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';

export enum Modal {
  REENTRY = "REENTRY"
}

type IReportResponseRecord = {
  uid: number,
  attempt_id: number,
  test_question_id : number,
  score : number,
  weight : number,
  responseText : string,
  response_raw : string,
  updated_on : string,
  responseKey ?: string,
  test_design_id?: number
}
type IStudent = {
  uid:number,
  firstName :string,
  lastName :string,
  dob:string,
  studentGovId:string,
  /////
  attempt_id: number,
  responseRef: Map<number, IReportResponseRecord>,
  percScore: string,
  percComplete: string,
}

type IStudentAttempt = {
  questions: { 
    [key: number]: {
      score: any,
      weight: number,
      isLoading?: boolean,
      isLongLoading?: boolean,
      hasError?: boolean,
      originalScore?: any,
      isLocked?: boolean
    }
  }
  isClosed: null | number,
  testAttemptId: number,
}

export interface IScoreEntryPatchData {
  weight?: number,
  score?: number,
  is_closed?: number
}

export interface IScoreEntryPatchParams {
  query: {
    testSessionId?: number,
    questionId?: number,
    questionIds?: number[],
    testAttemptId: number,
    action: SCORE_ENTRY_ACTION_TYPES,
  }
}

export interface IScoreEntryTestDesign {
  studentAttempts: {
      [x: number]: IStudentAttempt;
  };
  testForm: {
      sections: any[];
      questionDb: {};
  };
  discontinuationRules: IDiscontRuleMap;
  questionScoreSlugMap: TQuestionScoreSlugMap;
  assessment_description: {[key: string]: string};
}

@Component({
  selector: 'score-entry',
  templateUrl: './score-entry.component.html',
  styleUrls: ['./score-entry.component.scss']
})

export class ScoreEntryComponent implements OnInit {
  @Input() testSessionId: number;
  @Input() classId: number;
  @Input() scoreEntryPatch: (
    uid: any, 
    data: IScoreEntryPatchData, 
    params: IScoreEntryPatchParams
  ) => Promise<any> = async (uid: any, data: IScoreEntryPatchData, params: IScoreEntryPatchParams) => {};
  @Input() test_designs: IScoreEntryTestDesign[];
  @Input() isSample:boolean = false;

  constructor(
    private whiteLabel: WhitelabelService,
    private classroomService: ClassroomsService,
    private lang: LangService,
    private auth: AuthService,
    private cdr: ChangeDetectorRef,
    private login: LoginGuardService,
    private pageModalService: PageModalService,
  ) { }
  isLoading = true;
  sections: {questions: number[], sectionSlug: string}[];
  studentAttempts: {[key: number]: IStudentAttempt};
  selectedTestDesign: IScoreEntryTestDesign;
  students:IStudent[]
  discontinuationRules: IDiscontRuleMap = {};
  questionScoreSlugMap: TQuestionScoreSlugMap = {}; // Mapping of question slug information with entry domain and caption

  // Modal
  pageModal: PageModalController;
  Modal = Modal;

  async ngOnInit() {
    this.pageModal = this.pageModalService.defineNewPageModal();
    try {
      await this.fetchTestDesignForTestSession();
    } catch(err) {
      console.error(err);
    }
    this.isLoading = false;
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }
  get cmc() { return this.cModal().config; }

  sectionQuestionMap: {[key: string]: number[]} = {}; // Used to get an array of question IDs based on a specified section slug
  questionSectionMap: {[key: number]: string} = {}; // Used to get a section slug based on a specified question ID
  sectionQuestionSlugMap: {[key: string]: {[key: string]: number}} = {}; // Maps question slug/entry domains to a question ID, grouped by section slug.
  prepareStudentDataModel() {
    if(!this.test_designs) {
      this.login.quickPopup(`${this.lang.tra('txt_scor_entry_error')} MISSING_TEST_DESIGN`);
      this.isLoading = true;
      return;
    }
    this.loadDiscontRules();
    this.loadQuestionScoreSlugMap();
    this.loadAssessmentDescription();

    const questions:number[] = []
     this.sections.forEach((section, sectionIndex) => {
      this.sectionQuestionMap[section.sectionSlug] = [];
      this.sectionQuestionSlugMap[section.sectionSlug] = {};
      section.questions.forEach((q) => {
        this.questionSectionMap[q] = section.sectionSlug;
        this.sectionQuestionMap[section.sectionSlug].push(q);

        const questionSlug = this.questionScoreSlugMap[q]?.entry_domain ?? q
        this.sectionQuestionSlugMap[section.sectionSlug][questionSlug] = q;
        questions.push(q)
      })
    })
    for (const uid in this.studentAttempts) {
      questions.forEach(question => {
        if (!this.studentAttempts[uid].questions[question]) {
          const weight = +this.selectedTestDesign.testForm.questionDb[question].content[0].scoreWeight;
          this.studentAttempts[uid].questions[question] = {
            score: this.isSample ? this.getRandomInt(weight) : null,
            weight
          }
        }
      })
    }

    if(!this.questionScoreSlugMap || !Object.keys(this.questionScoreSlugMap).length) {
      this.login.quickPopup(`${this.lang.tra('txt_scor_entry_error')} MISSING_ENTRY_DOMAINS`);
      this.isLoading = true;
      return;
    }
    
    this.triggerAllDiscontRules();
  }

  loadDiscontRules() {
    this.discontinuationRules = this.selectedTestDesign.discontinuationRules ?? {};
  }

  assessmentDescription = {};
  loadAssessmentDescription() {
    this.assessmentDescription = this.selectedTestDesign.assessment_description;
  }

  hasAssessmentDescription() {
    try {
      return this.assessmentDescription && !!this.assessmentDescription[this.lang.getCurrentLanguage()]
    } catch (err) {
      console.error(err);
      return false;
    }
  }

  getAssessmentDescription() {
    return this.assessmentDescription[this.lang.getCurrentLanguage()];
  }

  loadQuestionScoreSlugMap() {
    this.questionScoreSlugMap = this.selectedTestDesign.questionScoreSlugMap ?? {};
  }

  /**
   * Executes the initial trigger on the discontinuation rules.
   * Runs and applies lock/unlock rules on all student attempts.
   */
  async triggerAllDiscontRules() {
    console.log(this.discontinuationRules, 'discontinuation rules');
    console.log(this.sections, 'sections');
    console.log(this.questionScoreSlugMap, 'questionScoreSlugMap');
    if(!this.hasDiscontRules()) {
      return;
    }
    for(const uid in this.studentAttempts) {
      const itemsNeedsClearing: number[] = [];
      // Trigger section level lock/unlock rules
      for(const section of this.sections) {
        if(section.sectionSlug) {
          this.triggerSectionLockRules(+uid, section.sectionSlug, false, itemsNeedsClearing)
        }
      }

      // Trigger item level lock rules
      for(const section of this.sections) {
        if(section.sectionSlug) {
          this.triggerItemLockRules(+uid, section.sectionSlug, false, itemsNeedsClearing);
        }
      }

      await this.updateClearScore(+uid, itemsNeedsClearing);
    }
  }

  /**
   * Executes the discontinuation rules on a given student.
   * @param uid of the student to validate.
   */
  async triggerAttemptDiscontRule(uid: number) {
    if(!this.hasDiscontRules()) {
      return;
    }
    
    const itemsNeedsClearing: number[] = [];

    for(const section of this.sections) {
      this.triggerSectionLockRules(+uid, section.sectionSlug, false, itemsNeedsClearing)
    }
    // Trigger item level lock rules
    for(const section of this.sections) {
      this.triggerItemLockRules(+uid, section.sectionSlug, false, itemsNeedsClearing);
    }

    await this.updateClearScore(+uid, itemsNeedsClearing);
  }

  /**
   * Checks the discontinuation rules on a given student, to determine if any scores need clearing.
   * @param uid of the student to validate.
   * @returns true if any scores need clearing.
   */
  validateAttemptDiscontRule(uid: number) {
    if(!this.hasDiscontRules()) {
      return false;
    }
    let needsClearing: boolean[] = [];
    for(const section of this.sections) {
      needsClearing.push(this.triggerSectionLockRules(+uid, section.sectionSlug, true))
    }
    // Trigger item level lock rules
    for(const section of this.sections) {
      needsClearing.push(this.triggerItemLockRules(+uid, section.sectionSlug, true));
    }

    return needsClearing.includes(true);
  }

  /**
   * Finds and triggers any confirmation rules on a given question ID.
   * @param uid 
   * @param questionId 
   */
  triggerConfirmRules(uid: number, questionId: number): {isTriggered: boolean, message?: string} {
    const defaultValue = {isTriggered: false}

    try {

      if(!this.hasDiscontRules() || !this.questionScoreSlugMap || !Object.keys(this.questionScoreSlugMap).length) {
        return defaultValue;
      }
      
      const property = this.questionScoreSlugMap[questionId].entry_domain;
      if(!property) {
        return defaultValue;
      }
  
      const targetSection = this.questionSectionMap[questionId];
  
      const confirmRules = this.discontinuationRules[targetSection]?.rules?.filter((rule) => {
        const includesConditions = rule.conditions.findIndex((cond) => cond.property == property) > -1;
        return rule.action == IDiscontActions.CONFIRM && includesConditions;
      });
      
      if(!confirmRules || !confirmRules.length) {
        return defaultValue;
      }
  
      let isTriggered: boolean = false;
      let message: string;
      confirmRules.forEach((rule) => {
        if(isTriggered) {
          return;
        }
  
        const filteredConditions = rule.conditions.filter((cond) => cond.property == property);
  
        if(!filteredConditions || !filteredConditions.length) {
          return;
        }
  
        filteredConditions.forEach((cond) => { // Confirm conditions can either be measured by the regular comparison or the difference between 2 values.
          if(isTriggered) {
            return;
          }
  
          if(cond.sourceCard) { // if the confirm condition has a source card, then find the difference between the entries in the current and the source card.
            // condition will always be based on the current property.
            const sourceQId = this.sectionQuestionSlugMap[cond.sourceCard]?.[cond.property];
            const sourceScore = this.studentAttempts[uid]?.questions?.[sourceQId]?.score;
            const currQScore = this.studentAttempts[uid]?.questions?.[questionId]?.score;
  
            // Do not check if either target or current scores are empty.
            if(sourceScore == null || sourceScore == undefined || currQScore == undefined || currQScore == null) {
              return;
            }
            const difference = Math.abs(sourceScore-currQScore);
            if(evaluateComparison(difference, cond.operator, cond.value || undefined)) {
              isTriggered = true;
              message = rule.message[this.lang.getCurrentLanguage()] ?? this.lang.tra('txt_scor_entry_confirm_default');
            }
          } else {
            // Conditions and rules are already filtered down to the current question.
            const sourceScore = this.studentAttempts[uid]?.questions?.[questionId].score;
            
            if(evaluateComparison( sourceScore, cond.operator, cond.value || undefined)) {
              isTriggered = true;
              message = rule.message[this.lang.getCurrentLanguage()] ?? this.lang.tra('txt_scor_entry_confirm_default');
            }
          }
        })
      });
  
      return {isTriggered, message};
    } catch (err) {
      console.error(err);
      return defaultValue;
    }
  }

  /**
   * Runs the item lock rules on a given section.
   * @param uid of the student to validate.
   * @param targetSection slug of the section to validate.
   * @returns true if any scores need clearing.
   */
  triggerItemLockRules(uid: number, targetSection: string, checkOnly: boolean = false, itemsNeedsClearing: number[] = []): boolean {
    // get all item level lock rules.
    const lockRules = this.discontinuationRules[targetSection].rules.filter((rule) => rule.action == IDiscontActions.LOCK && rule.targetProperty !== undefined);
    let needsClearing: boolean[] = [];
    for(const rule of lockRules) {
      // Store target item to lock
      const lockTargetItemId = this.sectionQuestionSlugMap[targetSection]?.[rule.targetProperty];
      if(!lockTargetItemId) {
        this.login.quickPopup(`${this.lang.tra('txt_scor_entry_error')} MISSING_ENTRY_DOMAINS`);
      }

      let isLocked = false;
      // Evaluate source items.
      rule.conditions.forEach((cond) => {
        if(this.processComparison(uid, cond)) {
          isLocked = true;
        }
      });

      // Lock the item based on evaluation.
      needsClearing.push(this.clearAndLock(uid, lockTargetItemId, isLocked, checkOnly, itemsNeedsClearing))
    }

    // TODO: function for bulk question score change

    return needsClearing.includes(true);
  }

  /**
   * Runs the section lock/unlock rules on a given section.
   * @param uid of the specified student.
   * @param targetSection slug of the section to validate
   * @returns true if any scores need clearing.
   */
  triggerSectionLockRules(uid: number, targetSection: string, checkOnly: boolean = false, itemsNeedsClearing: number[] = []): boolean {
    const lockRules = this.discontinuationRules[targetSection].rules.filter((rule) => rule.action == IDiscontActions.LOCK && rule.targetProperty === undefined);
    const unlockRules = this.discontinuationRules[targetSection].rules.filter((rule) => rule.action == IDiscontActions.UNLOCK && rule.targetProperty === undefined);
  
    if(!lockRules || !lockRules.length) {
      return false;
    }

    let isLocked = false;
    for(const rule of lockRules) {
      rule.conditions.forEach((cond) => {
        if(this.processComparison(uid, cond)) {
          isLocked = true;
        }
      })
    }

    // If lock not needed, do not check unlock rules.
    if(!isLocked || !unlockRules || !unlockRules.length) {
      return this.clearAndLockSection(uid, targetSection, isLocked, checkOnly, itemsNeedsClearing)
    }

    for(const rule of unlockRules) {
      rule.conditions.forEach((cond) => {
        if(this.processComparison(uid, cond)) {
          isLocked = false;
        }
      })
    }

    return this.clearAndLockSection(uid, targetSection, isLocked, checkOnly, itemsNeedsClearing)
  }

  /**
   * Helper function for checking comparisons based off of conditions with several different cases.
   * 
   * If the object has a singular "property", then it will evaluate using the singular entry_domain.
   * 
   * If the object has an array in "properties", then it will evaluate the total score of the domains.
   * 
   * If the object has neither "property" or "properties", then it will evaluate the total score of the entire section.
   * 
   * @param uid 
   * @param cond 
   * @returns True if a discontinuation rule has been met. False if any error occurs.
   */
  processComparison(uid: number, cond: IDiscontConditions) {
    try {
      if(cond.properties && cond.properties.length) { // compare using total score of properties
        const sourceQIds: number[] = [];
        let totalScore = 0;
  
        cond.properties.forEach((property) => {
          const sourceQId = this.sectionQuestionSlugMap[cond.sourceCard]?.[property];
  
          if(sourceQId) {
            sourceQIds.push(sourceQId);
          }
        })
  
        sourceQIds.forEach((sourceQId) => {
          totalScore += this.studentAttempts[uid]?.questions?.[sourceQId].score ?? 0;
        })

        return evaluateComparison( totalScore, cond.operator, cond.value || undefined )
      } else if(cond.property) { // compare using score of property
        const sourceQId = this.sectionQuestionSlugMap[cond.sourceCard]?.[cond.property];
        const sourceScore = this.studentAttempts[uid]?.questions?.[sourceQId]?.score;

        return evaluateComparison( sourceScore, cond.operator, cond.value || undefined)
      } else { // total entire section
        const sourceQIds: number[] = this.sectionQuestionMap[cond.sourceCard] ?? [];
        let totalScore = 0;
  
        sourceQIds.forEach((sourceQId) => {
          totalScore += this.studentAttempts[uid]?.questions?.[sourceQId].score ?? 0;
        })

        return evaluateComparison( totalScore, cond.operator, cond.value || undefined )
      }
    } catch (err){
      this.login.quickPopup(`${this.lang.tra('txt_scor_entry_error')} PROCESS_COMPARISON_ERR_${err.message}`);
      console.error(err)
      return false;
    }
  }

  /**
   * Helper function for locking and clearing questions scores.
   * Clears the score if isLocked is true
   * 
   * @param uid of the specified student
   * @param qId question ID of the item to lock.
   * @param isLocked toggle of the lock state.
   * @returns true if the score needs to be cleared.
   */
  clearAndLock(uid: number, qId: number, isLocked: boolean, checkOnly = false, itemsNeedsClearing: number[] = []): boolean {
    let needsClearing = false;

    if(isLocked && this.studentAttempts[uid].questions[qId].score != null && this.studentAttempts[uid].questions[qId].score != undefined) {
      needsClearing = true;
    }

    if(!checkOnly) {
      this.studentAttempts[uid].questions[qId].isLocked = isLocked;
    }
    if(isLocked && !checkOnly) {
      itemsNeedsClearing.push(qId);
      this.studentAttempts[uid].questions[qId].score = null;
    }

    return needsClearing;
  }

  // TODO
  // Check first if a rule will be broken, then show message saying it will be cleared.
  // Implement bulk attempt update.

  /**
   * Helper function for locking and clearing all questions in a given section.
   * @param uid of the specified student.
   * @param targetSection the slug of the section to lock/unlock.
   * @returns true if any scores need to be cleared.
   */
  clearAndLockSection(uid: number, targetSection: string, isLocked: boolean, checkOnly = false, itemsNeedsClearing: number[] = []): boolean {
    const qIds = this.sectionQuestionMap[targetSection];
    let needsClearing: boolean[] = [];

    if(!qIds) {
      this.login.quickPopup(`${this.lang.tra('txt_scor_entry_error')} INVALID_SECTION_SLUG`)
      console.error('INVALID_SECTION_SLUG');
      return needsClearing.includes(true);
    }

    qIds.forEach((qId) => {
      needsClearing.push(this.clearAndLock(uid, qId, isLocked, checkOnly, itemsNeedsClearing));
    })

    // TODO: function for bulk question score change

    return needsClearing.includes(true);
  }

  async fetchTestDesignForTestSession() {
    if(!this.test_designs) {
      this.login.quickPopup(`${this.lang.tra('txt_scor_entry_error')} MISSING_TEST_DESIGN`)
    }
    
    this.selectedTestDesign = this.test_designs[0]; // 

    this.studentAttempts = this.selectedTestDesign.studentAttempts;

    this.sections = this.selectedTestDesign.testForm.sections

    this.prepareStudentDataModel()
  }

  getStudentIdent(){
    return this.whiteLabel.getSiteText('student_ident');
  }

  showFormSelection(){
    return this.test_designs.length > 1;
  }

  selectForm(testDesign){
    this.selectedTestDesign = testDesign;
    this.studentAttempts = this.selectedTestDesign.studentAttempts;
    this.sections = this.selectedTestDesign.testForm.sections
    this.prepareStudentDataModel()
  }


  getStudentScore(uid: number, questionId: number): number {
    // Retrieve the current score from the data structure
    return this.studentAttempts[uid]?.questions[questionId]?.score || 0;
  }

  getStuAttItem(studentObj:{value: IStudentAttempt}, item_id:number){
    return studentObj.value.questions[item_id]
  }

  isStuAttItemError(studentObj:{value: IStudentAttempt}, item_id:number){
    return this.getStuAttItem(studentObj, item_id)?.hasError
  }

  isStuAttItemLongLoading(studentObj: { value: IStudentAttempt }, item_id: number) {
    return this.getStuAttItem(studentObj, item_id)?.isLongLoading;
  }
  

  isStuAttItemBlank(studentObj:{value: IStudentAttempt}, item_id:number){
    // Don't give blank class to locked attempts
    if (this.isAttemptClosed(studentObj.value)) {
      return false;
    }
    const {score} = this.getStuAttItem(studentObj, item_id)
    return score === null || score === undefined || score === '';
  }

  onScoreFocus(questionId, uid) {
    const originalValue = this.studentAttempts[uid].questions[questionId].score;
    this.studentAttempts[uid].questions[questionId].originalScore = originalValue;
  }
  
  /**
   * Controls inputs on focus out
   * @param questionId 
   * @param uid 
   * @param testAttemptId 
   * @param newValue 
   * @param inputElement 
   * @returns 
   */
  async onScoreChange(questionId, uid, testAttemptId, newValue: string, inputElement: HTMLInputElement) {
    const parsedValue = newValue === '' ? null : Number(newValue);
    const originalValue = this.studentAttempts[uid].questions[questionId].originalScore;
    console.log(uid)
    if (parsedValue > this.studentAttempts[uid].questions[questionId].weight) {
      this.login.quickPopup(this.lang.tra('txt_scor_entry_max_popup'))
      inputElement.value = originalValue || null;
      return;
    }
    // prevent negatives and decimals
    if (parsedValue < 0 || parsedValue % 1 != 0) {
      this.login.quickPopup(this.lang.tra('txt_scor_entry_invalid_popup'))
      inputElement.value = originalValue || null;
      return;
    }

    if(parsedValue === null) { // To ensure that any invalid inputs are reflected properly.
      inputElement.value = null;
    }

    this.studentAttempts[uid].questions[questionId].score = parsedValue; // temporarily updating attempt for discontinuation rules.

    const needsConfirmation = parsedValue == originalValue ? false : this.triggerConfirmRules(uid, questionId);
    const needsClearing = this.validateAttemptDiscontRule(uid);
    if(needsClearing) {
      this.login.confirmationReqActivate({
        caption: this.lang.tra('txt_scor_entry_score_clear'),
        confirm: async () => {
          this.studentAttempts[uid].questions[questionId].score = originalValue || null;
          await this.updateScore(uid, questionId, parsedValue, testAttemptId);
        },
        close: () => {
          if(inputElement) {
            inputElement.value = originalValue || null;
          }
          this.studentAttempts[uid].questions[questionId].score = originalValue || null;
        }
      })
    } else if(needsConfirmation && needsConfirmation.isTriggered) {
      const config = {
        message: needsConfirmation.message,
        error: null,
        reentryModalValue: null,
        isConfirm: false
      }

      this.pageModal.newModal({type: Modal.REENTRY, config, finish: async () => {
        if(+config.reentryModalValue != parsedValue) {
          config.error = this.lang.tra('txt_scor_entry_confirm_invalid')
          return;
        }
        try {
          config.isConfirm = true;
          this.studentAttempts[uid].questions[questionId].score = originalValue || null;
          await this.updateScore(uid, questionId, parsedValue, testAttemptId);
        } catch (err) {
          this.login.quickPopup(`${this.lang.tra('txt_scor_entry_error')} SCORE_CHANGE_ERR_${err.message}`);
          console.error(err);
        } finally {
          this.pageModal.closeModal();
        }
      }, cancel: () => {
        if(!config.isConfirm) {
          console.log('cancelling change')
          this.studentAttempts[uid].questions[questionId].score = originalValue || null;
        }
      }});
    } else {
      await this.updateScore(uid, questionId, parsedValue, testAttemptId);
    }
  }

  /**
   * Controls inputs upon typing.
   * @param event 
   * @param uid 
   * @param questionId 
   */
  onScoreInput(event: Event, uid: string, questionId: string): void {
    try {
      const inputEl = event.target as HTMLInputElement;
      const weight = this.studentAttempts[uid].questions[questionId].weight;
    
      const value = inputEl.value;
      const isNegative = value.startsWith('-');
  
      // Remove non-numeric characters
      let rawValue = value.replace(/\D/g, '');
    
      const maxDigits = weight.toString().length;
      if (rawValue.length > maxDigits) {
        rawValue = rawValue.slice(0, maxDigits);
        inputEl.value = isNegative ? '-' + rawValue : rawValue;
      }
    } catch (err) {
      console.error(err);
    }
  }

  async updateScore(uid: number, questionId: number, newValue: number, testAttemptId: number) {

    this.studentAttempts[uid].questions[questionId].isLoading = true;
    this.studentAttempts[uid].questions[questionId].hasError = false;

    // Indicator after 3 seconds for when user is lagging
    const loadingTimeout = setTimeout(() => {
      this.studentAttempts[uid].questions[questionId].isLongLoading = true;
    }, 3000);

    try {
      const scoreEntryPatchData: IScoreEntryPatchData = {
        score: newValue,
        weight: +this.studentAttempts[uid].questions[questionId].weight,
      }
      const scoreEntryPatchParams: IScoreEntryPatchParams = {
        query: {
          testSessionId: this.testSessionId,
          questionId,
          testAttemptId,
          action: SCORE_ENTRY_ACTION_TYPES.SCORE_CHANGE,
        }
      }
      await this.scoreEntryPatch(uid, scoreEntryPatchData, scoreEntryPatchParams);
      this.studentAttempts[uid].questions[questionId].score = newValue;
      clearTimeout(loadingTimeout);
      this.studentAttempts[uid].questions[questionId].isLongLoading = false;
      // this.checkDiscontRules();
      await this.triggerAttemptDiscontRule(uid)
    }
    catch (error) {
      console.error('Error updating score:', error);
      this.studentAttempts[uid].questions[questionId].hasError = true;
    }
    finally {
      clearTimeout(loadingTimeout);
      this.studentAttempts[uid].questions[questionId].isLoading = false;
      this.studentAttempts[uid].questions[questionId].isLongLoading = false;
      this.cdr.markForCheck();
    }
  }


  async updateClearScore(uid: number, itemsNeedsClearing: number[]) {
    if(!itemsNeedsClearing || !itemsNeedsClearing.length) {
      return;
    }
    const testAttemptId = this.studentAttempts[uid].testAttemptId;
    
    try {
      const scoreEntryPatchData: IScoreEntryPatchData = {
        score: null,
      }
      const scoreEntryPatchParams: IScoreEntryPatchParams = {
        query: {
          testSessionId: this.testSessionId,
          questionIds: itemsNeedsClearing,
          testAttemptId,
          action: SCORE_ENTRY_ACTION_TYPES.SCORE_CLEAR,
        }
      }
      await this.scoreEntryPatch(uid, scoreEntryPatchData, scoreEntryPatchParams);
    }
    catch (error) {

      this.login.quickPopup(`${this.lang.tra('txt_scor_entry_error')} ${error.message}`)
      console.error('Error updating score:', error);

      for(const questionId of itemsNeedsClearing) {
        this.studentAttempts[uid].questions[questionId].hasError = true;
        this.studentAttempts[uid].questions[questionId].isLocked = false; // errored questions unlocked so that the user doesn't get stuck.
      }
    }
    finally {
      for(const questionId of itemsNeedsClearing) {
        this.studentAttempts[uid].questions[questionId].isLoading = false;
        this.studentAttempts[uid].questions[questionId].isLongLoading = false;
      }
      this.cdr.markForCheck();
    }
  }

  async toggleLock(studentObj:{value: IStudentAttempt, key: string}) {

    const isNullLocked = Object.values(studentObj.value.questions).some((check:any)=> !check.isLocked && check.score === null);
    const newStatus = this.isAttemptClosed(studentObj.value) ? 0 : 1;
    
    if(isNullLocked && newStatus === 1) {
      this.login.confirmationReqActivate({
        caption: this.lang.tra('eya_score_entry_lock_score_popup'),
        confirm: async () => {
          try {
            const scoreEntryPatchData: IScoreEntryPatchData = {
              is_closed: newStatus
            }
            const scoreEntryPatchParams: IScoreEntryPatchParams = {
              query: {
                action: SCORE_ENTRY_ACTION_TYPES.LOCK_CHANGE,
                testAttemptId: studentObj.value.testAttemptId,
              },
            }
            await this.scoreEntryPatch(studentObj.key, scoreEntryPatchData, scoreEntryPatchParams); //updating score in API
            studentObj.value.isClosed = newStatus;
          }
          catch (error) {
            console.error('Error updating lock status:', error);
          }
        },
        close: () => {
         console.log("No changes")
        }
      })
    }
    else{
      studentObj.value.isClosed = newStatus;
    }
    // this.cdr.markForCheck();
  }

  getColumnCount(): number {
    // Default 2 for name and asn
    let columnCount = 2;
    this.sections.forEach(section => {
        columnCount += section.questions.length;
    });
    return columnCount;
  }
  isAttemptClosed(studentAttempt: IStudentAttempt) {
    return studentAttempt.isClosed == 1 ? true : false;
  }

  getRandomInt(max) {
    return Math.floor(Math.random() * max);
  }

  hasDiscontRules() {
    if(!this.discontinuationRules) {
      return false;
    }

    if(!Object.keys(this.discontinuationRules).length) {
      return false;
    }

    return true;
  }

  isQuestionLocked(uid: number, qId: number) {
    return this.studentAttempts[uid]?.questions[qId]?.isLocked ?? false;
  }

  getQuestionLabel(questionId: number) {
    return this.questionScoreSlugMap[questionId]?.entry_caption?.[this.lang.getCurrentLanguage()] ?? `Question ${questionId}`;
  }

  inputFocus(event){
    event.preventDefault();
    const inputCells = Array.from(document.querySelectorAll('input'));
    const iter = inputCells.indexOf(event.target as HTMLInputElement);
    if (iter>-1 && iter<inputCells.length-1) {
      (inputCells[iter+1] as HTMLElement).focus();
    }
  }
}
