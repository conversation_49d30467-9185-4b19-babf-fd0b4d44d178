<div *ngIf="!isLoading">
    <div *ngIf="showFormSelection()">
        <span *ngFor="let design of test_designs; let formIndex = index">
            <button class="button is-small" 
            [class.is-selected]="selectedTestDesign == design.test_design_id" 
            (click)="selectForm(design)">
                Form {{formIndex + 1}}
            </button>
        </span>
    </div>
    <div *ngIf="hasAssessmentDescription()" style="margin-bottom: 1em; margin-top: 1em;">
        {{getAssessmentDescription()}}
    </div>
    <div class="score-entry-wrapper">
        <table class="student-results" [style.--column-count]="getColumnCount()">
            <thead>
                <tr>
                    <th [rowSpan]="1" [colSpan]="1"><tra [slug]="getStudentIdent()"></tra></th>
                    <th [rowSpan]="1" [colSpan]="1">
                        <div>
                            <tra slug="abed_invig_name_th"></tra>&nbsp;
                        </div>
                    </th>
                    <th 
                        *ngFor="let section of sections;" 
                        class="section-header"
                        [rowSpan]="1" 
                        [colSpan]="section.questions?.length ?? 1" 
                    >
                        {{section.caption}}
                    </th>
                    <th></th>
                </tr>
                <tr>
                    <th></th>
                    <th></th>
                    <ng-container *ngFor="let section of sections">
                        <ng-container *ngFor="let questionId of section.questions; let j = index;">
                            <th
                                [class.section-start]="j === 0"
                                [class.section-end]="j === section.questions.length - 1"
                            >
                                {{getQuestionLabel(questionId)}}
                            </th>
                        </ng-container>
                    </ng-container>
                    <th></th>
                </tr>
                <tr>
                    <th></th>
                    <th></th>
                    <ng-container *ngFor="let section of sections">
                        <ng-container *ngFor="let questionId of section.questions; let j = index;">
                            <th
                                [class.section-start]="j === 0"
                                [class.section-end]="j === section.questions.length - 1"
                            >
                                /{{+this.selectedTestDesign.testForm.questionDb[questionId].content[0].scoreWeight}}
                            </th>
                        </ng-container>
                    </ng-container>
                    <th></th>
                </tr>
            </thead>
            <tr *ngFor="let student of studentAttempts | keyvalue">
                <td>
                    <span>
                        {{student.value.studentGovId}}
                    </span>
                </td>
                <td>
                    <span >
                        {{student.value.firstName}} {{student.value.lastName}}
                    </span>
                </td>
                <ng-container *ngFor="let section of sections; let i = index;">
                    <ng-container *ngFor="let item_id of section.questions; let j = index;">
                        <td
                            [class.score-loading]="getStuAttItem(student, item_id)?.isLoading"
                            [class.score-long-loading]="isStuAttItemLongLoading(student, item_id)"
                            [class.score-error]="isStuAttItemError(student, item_id)"
                            [class.score-blank]="isStuAttItemBlank(student, item_id)"
                            [class.score-locked]="isQuestionLocked(student.key, item_id)"
                            [class.section-start]="j === 0"
                            [class.section-end]="j === section.questions.length - 1"
                        >
                            <div style="min-width: 5em;">
                                <input
                                    [ngClass]="isQuestionLocked(student.key, item_id)?'locked':'unlocked'"
                                    style="width: 100%;"
                                    type="number"
                                    [ngModel]="getStuAttItem(student, item_id).score"
                                    (focus)="onScoreFocus(item_id, student.key)"
                                    (blur)="onScoreChange(item_id, student.key, student.value.testAttemptId, $event.target.value, $event.target)"
                                    min="0"
                                    max="{{ getStuAttItem(student, item_id).weight }}"
                                    [readonly]="isQuestionLocked(student.key, item_id)"
                                    [disabled]="isAttemptClosed(student.value)"
                                    (keydown.enter)="inputFocus($event)"
                                    (input)="onScoreInput($event, student.key, item_id)"
                                />
                            </div>
                        </td>
                    </ng-container>
                </ng-container>
                <td>
                    <button class="button" (click)="toggleLock(student)">
                        <i *ngIf="!isAttemptClosed(student.value)" class="fas fa-lock-open"></i>
                        <i *ngIf="isAttemptClosed(student.value)" class="fas fa-lock"></i>
                    </button>
                  </td>
            </tr>
        </table>
    </div>
</div>

<div class="custom-modal" *ngIf="cModal()">
    <div class="modal-contents">
        <div [ngSwitch]="cModal().type">
            <div *ngSwitchCase="Modal.REENTRY" style="width: 35em; ">
                <div>
                    {{cmc.message}}
                </div>
                <div style="width: 8em; margin-top: 1em; margin-bottom: 1em;">
                    <input [(ngModel)]="cmc.reentryModalValue" class="input" type="number"/>
                </div>
                <div class="is-error" *ngIf="cmc.error">
                    {{cmc.error}}
                </div>
                <modal-footer [pageModal]="pageModal" [isConfirmAlert]="true"></modal-footer>
            </div>
        </div>
    </div>
</div>