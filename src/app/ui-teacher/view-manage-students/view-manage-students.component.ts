import { Component, OnInit, ViewChild, ElementRef} from '@angular/core';
import { G9DemoDataService } from '../../ui-schooladmin/g9-demo-data.service';
import { IStudentAccount, IStudentTestSession, } from '../../ui-schooladmin/data/types';
import { IStudent, IStudentList, ISession, PrintScanModal} from '../data/types';
import { ActivatedRoute } from '@angular/router';
import { SidepanelService } from '../../core/sidepanel.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { FormGroup, FormControl, AbstractControl } from '@angular/forms';
import { STUDENT_G9_COURSES_SIMPLE, STUDENT_GENDERS, STUDENT_G9_COURSES } from '../../ui-schooladmin/data/constants';
import {PageModalController, PageModalService} from "../../ui-partial/page-modal.service";
import { TeacherModal } from '../data/types';
import { LoginGuardService } from '../../api/login-guard.service';
import { ClassroomsService } from '../../core/classrooms.service'
import { AuthService } from '../../api/auth.service';
import { RoutesService } from '../../api/routes.service';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { IStudentInfoConfig, showScanModal } from '../t-modal-student-info-chooser/t-modal-student-info-chooser.component';
import { ScanInfoService } from '../scan-info.service';
import { TeacherViews, TEACHER_VIEWS } from './data/view';
import { OnlineOrPaperService } from '../../ui-testrunner/online-or-paper.service';
import { ITSchlStuModalData } from '../t-modal-school-students/t-modal-school-students.component';

const trim = (str:any) => {
  if(!str){
    return null;
  } else return (str.toString() || '').trim();
}

const TEMP_META_MAPPING = { // to be replaced with the central
  eqao_student_gov_id: 'StudentOEN',
  nbed_user_id: 'NBED_UserId',
  mbed_user_id: 'MBED_UserId',
  eqao_g9_course: 'Program',
  eqao_g9_gender: 'Gender',
  teacher_notes: 'TeacherNotes',
  nbed_student_identification_number: 'Student_Identification_Number', //TODO : refactor the name - too generalize
  abed_student_identification_number: 'StudentIdentificationNumber',
  eqao_g9_sasn: 'SASN',
}

const NBED_VALID_ID_LENGTH  = 11;
const MBED_VALID_ID_LENGTH  = 11;
const EQAO_VALID_ID_LENGTH = 9;
const ABED_VALID_ID_LENGTH = 9;

@Component({
  selector: 'view-manage-students',
  templateUrl: './view-manage-students.component.html',
  styleUrls: ['./view-manage-students.component.scss']
})
export class ViewManageStudentsComponent implements OnInit {

  constructor(
    private route: ActivatedRoute,
    public lang: LangService,
    private auth: AuthService,
    private sidePanel: SidepanelService,
    public  loginGuard: LoginGuardService,
    private g9demoService: G9DemoDataService,
    private breadcrumbsService: BreadcrumbsService,
    private pageModalService: PageModalService,
    private classroomService: ClassroomsService,
    private routes: RoutesService,
    public whiteLabelService: WhitelabelService,
    private scanInfo: ScanInfoService,
    private onlineOrPaper: OnlineOrPaperService,
  ) { }

  breadcrumb = [];
  studentList:IStudentAccount[];
  // students:IStudent[] = [];
  classroomId:string;
  activeClassroom;
  additionalInvigilators;
  guestClassInviligates;
  isCreatingStudents:boolean;
  formGroup = new FormGroup({
    first_name: new FormControl(),
    last_name: new FormControl(),
    date_of_birth: new FormControl(),
    eqao_student_gov_id: new FormControl(),
    eqao_g9_course: new FormControl('1'), // todo:CONST (what does this represent?)
    eqao_g9_gender: new FormControl(),
    teacher_accomm: new FormControl(),
    teacherNotes: new FormControl(),
    lang: new FormControl(),
    class_access_code: new FormControl(),
    student_id_number: new FormControl()
  })
  pageModal: PageModalController;
  TeacherModal = TeacherModal;
  isLookedupStudentGovId:boolean;
  lookupButtonValue:string = this.whiteLabelService.isABED() ? this.lang.tra('abed_lookup') : this.lang.tra('student_account_look');
  studentExistsInAnotherClass:boolean;

  isScanInfoLoaded = false;
  isAddingVirtualClass = false;
  addButtonValue:string = this.lang.tra('ta_add_btn_value_look');

  TeacherViews = TeacherViews;
  views = [];
  selectedView: TeacherViews;
  guestClasses= [];
  currentQueryParams
  PrintScanModal = PrintScanModal;

  currentStudentDetail: any;
  accommodationList: any[];
  schoolStudentsList: any[] = [];
  selectedSchoolStudents: any[];

  allowPASIUpdates: boolean = this.whiteLabelService.getSiteFlag('IS_ALLOW_PASI_UPDATES');

  isSampleSN:boolean = this.whiteLabelService.getSiteFlag('IS_ALLOW_SAMPLE_SN');

  @ViewChild('csvStudentInput', { static: false }) csvStudentInput: ElementRef<HTMLInputElement>;

  ngOnInit(): void {
    this.sidePanel.deactivate();
    this.loginGuard.activate();
    this.route.queryParams.subscribe((queryParams) => {
      this.currentQueryParams = {
        school: queryParams['school']
      }
    });
    this.route.params.subscribe(params => {
      this.classroomId = params['classroomId'];
      this.sidePanel.classroomId = this.classroomId;
      this.classroomService.sub().subscribe(data => {
        if (data){
          this.loadStudentList();
          this.loadGuestClassList();
          this.loadSchoolStudents();
          this.loadAdditionalInviligatorList();
          this.loadGuestClassInviligateList();
          this.initRoute();
        }
      })
    });
    //console.log(this.classroomService.classrooms)
    this.pageModal = this.pageModalService.defineNewPageModal();

    this.views = [];
    this.selectedView = this.TeacherViews.Students;
    TEACHER_VIEWS.forEach(view => {
      if(view.id === TeacherViews.GuessClass){
        if (this.isGuestStudentsSupported()){
          this.views.push( Object({
            ...view,
            caption: this.lang.tra(view.caption),
            description: this.lang.tra(view.description),
          }))
        }
      }
    })
  }

  getSchoolGroupId = () => this.g9demoService.getSchoolGroupId()

  viewSchoolStudents(){
    alert('Please contact the school administrator to confirm that all students are loaded.')
  }

  isGuestStudentsSupported(){
    return this.whiteLabelService.getSiteFlag('IS_GUEST_STU_AVAIL')
  }

  async loadStudentAccommodations(uid?){
    this.accommodationList = await this.g9demoService.loadStudentAccommodations(this.activeClassroom.curricShort, uid);
  }

  initRoute(){
    this.activeClassroom = this.g9demoService.getClassroomById(this.classroomId);
    if(this.activeClassroom.curricShort === 'EQAO_G10'){
      this.formGroup =  new FormGroup({
        first_name: new FormControl(),
        last_name: new FormControl(),
        eqao_student_gov_id: new FormControl(),
        eqao_g9_course: new FormControl(),
        eqao_g9_gender: new FormControl(),
        teacher_accomm: new FormControl(),
        student_id_number: new FormControl(),
        teacherNotes: new FormControl(),
        lang: new FormControl(),
        class_access_code: new FormControl(),
      });
    }
    
    const classroomName = this.g9demoService.getClassroomNameById(this.classroomId)
    const basePath = `/${this.lang.c()}/educator/classrooms`
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT( this.lang.tra('lbl_classes'), basePath, this.currentQueryParams),
      this.breadcrumbsService._CURRENT( classroomName, basePath+'/'+this.classroomId, this.currentQueryParams),
      this.breadcrumbsService._CURRENT( this.lang.tra('mng_manage_students'), basePath+'/students/'+this.classroomId, this.currentQueryParams),
    ];
  }

  getCaptionForExistingStudent(user: any, inSameClass: boolean) {
    let mainSlug;
    
    if(inSameClass) {
      mainSlug = this.isSasnLogin ? 'txt_student_exists_class_sasn':'txt_student_exists_class'
    } else if (user.isInSameTW){
      mainSlug = this.isSasnLogin ? 'txt_student_exists_tw_same_sasn':'txt_student_exists_tw_same'
    }
    else{
      mainSlug = this.isSasnLogin ? 'txt_student_exists_tw_diff_sasn':'txt_student_exists_tw_diff'
    } 
    if (this.whiteLabelService.isABED()) {
      if(inSameClass) {
        mainSlug = this.isTestCenter() ? 'txt_student_exists_class_ttid' : 'txt_student_exists_class_asn';
      }
      else {
        mainSlug = this.isTestCenter() ? 'txt_student_exists_another_class_ttid' : 'txt_student_exists_another_class_asn';
      }
    }
    let caption = `${this.lang.tra(mainSlug)}
          <br><br> ${this.lang.tra('lbl_student_info')} <br> ${this.lang.tra('lbl_first_name')}: ${user.first_name}
          <br> ${this.lang.tra('lbl_last_name')}: ${user.last_name}`;
    // If user is comes from lookupStudentGovId() try to show the student classes from the user object     
    if(!inSameClass) {
      if (user.classrooms) {
        user.classrooms.forEach(classroom => {
          if(classroom.isInSameTW) caption += `<br> ${this.lang.tra('lbl_class')}: ${classroom.name} `;
        });
      } else { // This student is already exists in the current scope (Teachers students)
        this.g9demoService.classrooms.forEach(classroom => {
          let students = this.g9demoService.getStudentsByClassroomId(String(classroom.id)).list;
          const student = students.find(student => student.id == user.id);
          if(student) {
            caption += `<br> ${this.lang.tra('lbl_class')}: ${classroom.class_code} `;
          }
        });
      }   
    } 
    
    return caption;
  }

  creatNewStudentEnableOEN(){
    this.isCreatingStudents = true;
    this.formGroup.controls.eqao_student_gov_id.enable();
  }

  lookupStudentGovId(){
    let eqao_student_gov_id = this.formGroup.controls.eqao_student_gov_id.value;
    eqao_student_gov_id = trim(eqao_student_gov_id);

    const studentIdLabel = this.lang.tra(this.getStudentAccountLookupSlug())

    // Check if student is already in the class (both guest and regular students)
    const isStudentInClass = this.studentList.some(s => s.course.StudentIdentificationNumber == eqao_student_gov_id)
    if (isStudentInClass){
      return this.loginGuard.quickPopup(this.lang.tra('student_account_id_exists_err', undefined, {studentIdLabel}))
    }

    let invalidMsg = this.lang.tra('student_account_inv_variable', undefined, {studentIdLabel})
    
    if(!eqao_student_gov_id){
      return this.loginGuard.quickPopup(invalidMsg)
    }

    if (!this.isSampleSN && (!this.isSasnLogin && eqao_student_gov_id.length !== this.getValidIdLength())){
      return this.loginGuard.quickPopup(invalidMsg)
    }
    
    if (this.isSasnLogin && eqao_student_gov_id.length > 9){
      return this.loginGuard.quickPopup(invalidMsg)
    }

    if(!this.getValidIdRegex().test(eqao_student_gov_id)){
      return this.loginGuard.quickPopup(invalidMsg)
    }

    this.auth.apiFind(this.routes.EDUCATOR_STUDENTS, {
      query: {
        value: eqao_student_gov_id,
        schoolClassId: this.classroomId,
        school_class_group_id:this.getClassGroupId(),
        sasn_login: this.isSasnLogin ? 1:0
      }
    }).then( users => {
      console.log(users);
      const { sameTwUser, differentTWuser, userToUpdate, crossSchool } =
      this.handleStudentUpdate(users, +this.classroomId);
      let caption;
      if (!sameTwUser && !differentTWuser){
        caption = this.lang.tra('lbl_no_student_oen')
        if(this.isSasnLogin) caption = this.lang.tra('lbl_no_student_sasn')
        if(this.whiteLabelService.isNBED() || this.whiteLabelService.isMBED()) caption = this.lang.tra('lbl_no_student_oen_nbed') 
        if(this.whiteLabelService.isABED()) {
          if(this.allowPASIUpdates){ //allow pasi updates means that the message mentioning creating a new student is suppressed in the else clause. 
            //We could move this to the context file as a site text, it seemed to be too specific to go there so I used isSasnLogin -Benney
            caption = this.isTestCenter() ? 'lbl_no_student_asn_no_reg_caec' : 'lbl_no_student_asn_no_reg';
          }else{
            caption = this.lang.tra('lbl_no_student_asn'); 
          }
        }
      }
      else {
        caption = this.getCaptionForExistingStudent(sameTwUser || differentTWuser, !this.studentExistsInAnotherClass);
      }
      this.loginGuard.confirmationReqActivate({
        caption,
        btnProceedConfig: {
          caption: this.studentExistsInAnotherClass ? 'abed_quick_add_btn' : 'OK'
        },
        confirm: () => {
          if(!this.isABED()) this.formGroup.controls.eqao_student_gov_id.disable();
          if (userToUpdate){
            this.updateStudentForm(userToUpdate);
            if (!this.studentExistsInAnotherClass) {
              this.cancelStudentEntry();
            }
          }
          if (this.studentExistsInAnotherClass) {
            this.moveStudentInAnotherClass(userToUpdate, sameTwUser, !!crossSchool, eqao_student_gov_id);
          }
          else{
              if(!this.allowPASIUpdates) {
                this.isLookedupStudentGovId = true;
                this.loadStudentAccommodations();
              }else{
              this.isLookedupStudentGovId = true;
            }
          }
          this.loadStudentList();
        }
      })
    }).catch(error =>{
      if(error.message == "STUDENT_NOT_EXIST_IN_COURSE" ){
        window.alert(this.lang.tra("lbl_student_not_in_course"));
      }

      // TODO: needs translations
      else if (error.message == "This ASN is a secondary ASN.") {
        window.alert(this.lang.tra("This ASN is a secondary ASN."));
      }
    })
  }



  moveStudentIntoCurrentClass(uid:number){

  }

  validateFormEntries(controls:{label:string, fc:AbstractControl}[]){
    const invalidEntries = [];
    controls.forEach(control => {
      if (!trim(control.fc.value)){
        invalidEntries.push(this.lang.tra(control.label))
      }
    });
    if (invalidEntries.length){
      this.loginGuard.quickPopup(`Please fill the missing fields (${invalidEntries.join(', ')}).`)
      return false;
    }
    return true;
  }
  getValidatableFormentries(){
    const controls = this.formGroup.controls;
    if(this.activeClassroom.curricShort === 'EQAO_G10'){
      return  [
          {label: 'OEN', fc: controls.eqao_student_gov_id},
          {label: 'lbl_first_name', fc: controls.first_name},
          {label: 'lbl_last_name', fc: controls.last_name},
          {label: 'sdc_1_gender', fc: controls.eqao_g9_gender},
          {label: 'student_id_number', fc: controls.student_id_number},
        ];
    } 
    else if(this.whiteLabelService.isABED()) {
      return  [
        {label: 'ASN', fc: controls.eqao_student_gov_id},
        {label: 'lbl_first_name', fc: controls.first_name},
        {label: 'lbl_last_name', fc: controls.last_name},
        {label: 'abed_dob_lbl', fc: controls.date_of_birth},
      ];
    }
    else if( this.whiteLabelService.isNBED() || this.whiteLabelService.isMBED()){
      return  [
        {label: 'OEN', fc: controls.eqao_student_gov_id},
        {label: 'lbl_first_name', fc: controls.first_name},
        {label: 'lbl_last_name', fc: controls.last_name},
        {label: 'student_id_number', fc: controls.student_id_number},
      ];
    }
    else{
      return  [
        {label: 'OEN', fc: controls.eqao_student_gov_id},
        {label: 'lbl_first_name', fc: controls.first_name},
        {label: 'lbl_last_name', fc: controls.last_name},
        // {label: 'sdc_1_course', fc: controls.eqao_g9_course},
        {label: 'sdc_1_gender', fc: controls.eqao_g9_gender},
        {label: 'student_id_number', fc: controls.student_id_number},
      ];
    }
  }

  getMetaForStudentEntry(controls) {
    // #MERGE_20220524 : refactor
    const studentMeta = {
      [TEMP_META_MAPPING.eqao_g9_gender]:      controls.eqao_g9_gender.value,
    }

    // todo:DB_DATA_MODEL
    // let key = TEMP_META_MAPPING.eqao_student_gov_id;  //key represents column in user_metas
    if(this.whiteLabelService.isNBED()){
      studentMeta[TEMP_META_MAPPING.nbed_user_id] = trim(controls.eqao_student_gov_id.value.toString())
      studentMeta[TEMP_META_MAPPING.nbed_student_identification_number] = controls.student_id_number.value
    } 
    else if (this.whiteLabelService.isABED()) {
      studentMeta[TEMP_META_MAPPING.abed_student_identification_number] = trim(controls.eqao_student_gov_id.value.toString())
    }
    else if(this.whiteLabelService.isMBED()) {
      studentMeta[TEMP_META_MAPPING.mbed_user_id] = trim(controls.eqao_student_gov_id.value.toString())
      studentMeta[TEMP_META_MAPPING.nbed_student_identification_number] = controls.student_id_number.value
    }
    else {
      studentMeta[TEMP_META_MAPPING.eqao_student_gov_id] = this.isSasnLogin?'000000000':trim(controls.eqao_student_gov_id.value.toString())
      if(this.isSasnLogin){
        studentMeta[TEMP_META_MAPPING.eqao_g9_sasn] = trim(controls.eqao_student_gov_id.value.toString())
      }
    }

    return studentMeta
  }

  addStudentEntry(){
    console.log("adding student entry")
    if (!this.isLookedupStudentGovId){
      return this.lookupStudentGovId()
    }
    const controls = this.formGroup.controls;
    let isValid = this.validateFormEntries(this.getValidatableFormentries());
    if (!isValid){
      return;
    }

    const isG3 = this.activeClassroom.curricShort === 'EQAO_G3';
    const isG6 = this.activeClassroom.curricShort === 'EQAO_G6';
    const isG9 = this.activeClassroom.curricShort === 'EQAO_G9';
    const isG10 = this.activeClassroom.curricShort === 'EQAO_G10';
    
    const localStudentRecord = {
      id: -1,
      first_name:          trim(controls.first_name.value),
      last_name:           trim(controls.last_name.value),
      DateofBirth:       controls.date_of_birth.value,
      eqao_student_gov_id: null,
      eqao_g9_gender:      this.getGender(controls),
      eqao_gender:         this.getGender(controls),
      eqao_is_g3:          isG3 ? "1":"#",
      eqao_is_g6:          isG6 ? "1":"#",
      eqao_is_g9:          isG9 ? "1":"#",
      eqao_is_g10:         isG10 ? "1":"#",
      abed_student_identification_number: this.getStudentIdentificationNumber(controls),
      lang:                trim(controls.lang.value),
      teacher_notes:       controls.teacherNotes.value,
      uid:                 null,
      displayName:         trim(controls.first_name.value) + " " + trim(controls.last_name.value),
      SASN:                this.isSasnLogin?trim(controls.eqao_student_gov_id.value.toString()): null,
      StudentIdentificationNumber: trim(controls.eqao_student_gov_id.value.toString())
    }

    let student:Partial<IStudentAccount> = {
      account: {
        first_name: trim(controls.first_name.value),
        last_name:  trim(controls.last_name.value),
      },
      meta: this.getMetaForStudentEntry(controls),
      meta_clean: [
        { key_namespace: this.getKeyNameSpace(), key: 'TeacherNotes', value: controls.teacherNotes.value, }
      ]
    }

    // todo:DB_DATA_MODEL
    if(isG3){
      student.meta_clean.push({ key_namespace:'eqao_sdc', key: 'IS_G3', value: 1} )
      student.meta_clean.push({ key_namespace:'eqao_sdc_g3', key: 'Grade', value: 3} )
    }
    if(isG6){
      student.meta_clean.push({ key_namespace:'eqao_sdc', key: 'IS_G6', value: 1 } )
      student.meta_clean.push({ key_namespace:'eqao_sdc_g6', key: 'Grade', value: 6} )
    }
    if(isG9){
      localStudentRecord['eqao_g9_course'] = controls.eqao_g9_course.value;
      student.meta[TEMP_META_MAPPING.eqao_g9_course] = controls.eqao_g9_course.value;
      student.meta_clean.push({ key_namespace:'eqao_sdc', key: 'IS_G9', value: 1 })
      student.meta_clean.push({ key_namespace:'eqao_sdc', key: 'IS_G10', value: '#'})

    }
    if(isG10){
      student.meta_clean.push({ key_namespace:'eqao_sdc', key: 'IS_G9', value: '#'} )
      student.meta_clean.push({ key_namespace:'eqao_sdc', key: 'IS_G10', value: 1 } )
    }

    if(this.isABED()){
      const dob = controls.date_of_birth.value;
      if(!this.g9demoService.validDate(dob)) {
        this.loginGuard.quickPopup(this.lang.tra("abed_dob_error_msg"))
        return;
      }
      student.meta_clean.push({ key_namespace:'abed_course', key: 'DateofBirth', value: controls.date_of_birth.value});
    }
    console.log(student)
    if(this.isSasnLogin){
      student.meta[TEMP_META_MAPPING.eqao_g9_sasn] = trim(controls.eqao_student_gov_id.value.toString())
    }
    
    this.classroomService
      .createStudent(this.classroomId, student, this.getClassGroupId(), this.isSasnLogin)
      .then( (studentUid) => {
        localStudentRecord.uid = studentUid
        if(this.isABED()) this.g9demoService.updateStudentAccommodations(studentUid, this.accommodationList);
        this.clearStudentEntry();
        const students = this.g9demoService.getStudentsByClassroomId(this.classroomId);
        students.list.push(localStudentRecord);
        const defaultWritingType = this.onlineOrPaper.getDefaultIsPaperValue(this.activeClassroom.curricShort);
        this.onlineOrPaper.addStudentWritingType(studentUid, defaultWritingType);
        // this.studentList
        // .push(
        //   this.g9demoService.generateStudentInvigilationRecord(localStudentRecord)
        // )
        // this.loginGuard.quickPopup(`A new student account has been created for ${localStudentRecord.first_name+' '+localStudentRecord.last_name}.`)
        this.loginGuard.quickPopup(this.lang.tra('lbl_new_student_added', undefined, {student_name: localStudentRecord.first_name+' '+localStudentRecord.last_name}))
        this.clearStudentEntry()
        this.isLookedupStudentGovId = false;
        this.isCreatingStudents = false;
      })
      .catch((e)=>{
        let caption:string = 'Could not create student record.';
        if (e.message === 'GOV_ID_IN_CLASS'){
          caption = this.lang.tra('lbl_oen_already_added')
        }
        this.loginGuard.quickPopup(caption)
      })
  }

  getKeyNameSpace = () => {
    if(this.whiteLabelService.isNBED()) return 'nbed_sdc';
    if(this.whiteLabelService.isMBED()) return 'mbed_sdc';
    return 'eqao_sdc';
  } 

  getStudentIdentificationNumber(controls){
    if(this.whiteLabelService.isNBED() || this.whiteLabelService.isMBED()) return controls.student_id_number;
    if(this.whiteLabelService.isABED()) return controls.eqao_student_gov_id.value.toString();
    return '#';
  }

  getGender(controls){
    if(this.whiteLabelService.isNBED() || this.whiteLabelService.isMBED()) return '#'
    return controls.eqao_g9_gender.value
  }


  getClassGroupId(){
    let school_class_group_id = null;
    const currentClassRecord = this.classroomService.classrooms.find(classroom => classroom.id == this.classroomId)
    if (currentClassRecord){
      school_class_group_id = currentClassRecord.group_id;
    }
    return school_class_group_id;
  }
  cancelStudentEntry(){
    this.formGroup.reset();
    this.formGroup.controls.eqao_student_gov_id.enable();
    this.isLookedupStudentGovId = false;
    this.isCreatingStudents = false;
  }
  clearStudentEntry(){
    this.formGroup.reset();
  }

  loadStudentList(){
    const classRoom = this.g9demoService.getStudentsByClassroomId(this.classroomId);
    if(classRoom!== undefined){
      this.studentList = [...this.g9demoService.getStudentsByClassroomId(this.classroomId).list];
      // Force change detection by creating a new array reference
    }  
    // .map((student, index) => {
    //   return this.g9demoService.generateStudentInvigilationRecord(student)
    // });
  }

  loadSchoolStudents() {
    this.schoolStudentsList = [];
    const classRoom = this.g9demoService.getClassroomById(this.classroomId);
    const classSemester = this.g9demoService.semesters.map[classRoom.semester_id]
    const classTestWindow = this.g9demoService.testWindows.find(tw => tw.id === classSemester.testWindowId)
    this.g9demoService.schoolStudents.forEach(schoolStudent => {
      let specifiedTwUserMetas = []
      if (schoolStudent.tw_user_metas) {
        specifiedTwUserMetas = schoolStudent.tw_user_metas.filter(user_meta => user_meta.test_window_id == classTestWindow.id)
      }
      this.schoolStudentsList.push({
        ...schoolStudent,
        tw_user_metas: specifiedTwUserMetas
      })
    });
  }

  loadGuestClassList(){
    const classRoom = this.g9demoService.getClassroomById(this.classroomId);
    this.guestClasses = [];
    if(classRoom!== undefined){
      this.guestClasses = this.g9demoService.getguestClasssByHostClassroomGroupId(classRoom.group_id)
    }
  }

  loadGuestClassInviligateList(){
    const classRoom = this.g9demoService.getClassroomById(this.classroomId);
    this.guestClassInviligates = [];
    if(classRoom!== undefined){
      this.guestClassInviligates = this.g9demoService.getGuestClassInvigilateByHostClassroomGroupId(classRoom.group_id)
    }
  }

  loadAdditionalInviligatorList(){
    const classRoom = this.g9demoService.getClassroomById(this.classroomId);
    this.additionalInvigilators = [];
    if(classRoom!== undefined){
      this.additionalInvigilators = this.g9demoService.getAdditionalInvigilatorByGroupId(classRoom.group_id);
    }
  }

  renderCourse(id:number|string){
    return STUDENT_G9_COURSES_SIMPLE.map[id].label
  }
  renderGender(id:number|string){
    return STUDENT_GENDERS.map[id].label
  }
  getCourseList(){
    return STUDENT_G9_COURSES.list
  }
  getGenderList(){
    return STUDENT_GENDERS.list
  }

  timeConvert(n) {
    var num = n;
    var hours = (num / 60);
    var rhours = Math.floor(hours);
    var minutes = (hours - rhours) * 60;
    var rminutes = Math.round(minutes);
    if (rhours > 0){
      return rhours + "hr " + rminutes + "min";
    }
    return  rminutes + "min";
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() {  return this.cModal().config; }

  async sampleModalStart(studentIdx: number){
    const studentList = JSON.parse(JSON.stringify(this.studentList));
    const config: IStudentInfoConfig = {studentList: studentList, curricShort: this.activeClassroom.curricShort, studentIdx};
    const isProceedOnly = this.showScanModal();
    if (this.isABED()) {
      this.currentStudentDetail = studentList[studentIdx];
      await this.loadStudentAccommodations(this.currentStudentDetail.uid);
  
      // Open the modal only after accommodations have been loaded
      this.openSampleModal(config, isProceedOnly, studentList);
    } else {
      this.openSampleModal(config, isProceedOnly, studentList);
    }
  }

  // Helper function to open the modal
  private openSampleModal(config: IStudentInfoConfig, isProceedOnly: boolean, studentList: any) {
    this.pageModal.newModal({
      type: TeacherModal.SAMPLE_MODAL_OTHER,
      config,
      isProceedOnly,
      finish: () => this.sampleModalFinish(studentList),
      cancel: this.resetAccommodations
    });
  }

  showScanModal() {
    return showScanModal(this.activeClassroom.curricShort);
  }
  sampleModalFinish = (editedList) => {
    if(this.isABED()){
      const isValidAccomm = this.g9demoService.validationAccomm(this.accommodationList);
      const isValidstudent = this.g9demoService.validateStudentInfo(this.currentStudentDetail);
      if(isValidAccomm && isValidstudent){
        this.studentList = editedList;
        this.g9demoService.updateStudentAccommodations(this.currentStudentDetail.uid, this.accommodationList);
        //when pasi updates are on, we block student edits, we should block the api call as well
        if(!this.allowPASIUpdates){
          const classrooom = this.g9demoService.getClassroomById(this.classroomId);
          this.classroomService.updateStudentInfo(this.currentStudentDetail, classrooom.group_id);
        }
      }else if(!isValidAccomm){
        this.loginGuard.quickPopup('abed_invalid_accommodations');
      }else if(!isValidstudent){
        this.loginGuard.quickPopup('bced_invalid_information');
      }
      this.pageModal.closeModal();
      this.resetAccommodations();
    }else{
      this.pageModal.closeModal();
    }
  }

  resetAccommodations = () =>{
    this.currentStudentDetail = null;
    this.accommodationList = [];
  }

  existModalStart(account){
    const config = {account, curricShort: this.activeClassroom.curricShort};
    this.pageModal.newModal({
      type: TeacherModal.ALREADY_EXIST,
      config,
      finish: this.existModalFinish
    });
  }
  existModalFinish = () => {
    this.pageModal.closeModal();
  }

  inValidTestWindow(){
    const classroom = this.g9demoService.classrooms.find( classroom => classroom.id === +this.classroomId)
    const classSemester = this.g9demoService.semesters.map[classroom.semester]
    const classTestWindow = this.g9demoService.testWindows.find(tw => tw.id === classSemester.testWindowId)
    return (new Date(classTestWindow.date_end) < new Date ())
  }


  getStudentAccountLookupSlug = () => {
    if(this.whiteLabelService.isNBED() || this.whiteLabelService.isMBED()){
      return 'student_account_num_nbed'
    } else if(this.whiteLabelService.isABED()) {
      return this.whiteLabelService.getSiteText('student_ident');
    }
    //EQAO
    if(this.isSasnLogin) return 'student_account_num_sasn';
    //ABED
    if(this.whiteLabelService.isABED()) return 'lbl_ASN_ABED'
    return 'student_account_num' 
  }

  getValidIdLength = ():number => {
    if(this.whiteLabelService.isNBED()){ return NBED_VALID_ID_LENGTH; }
    if(this.whiteLabelService.isMBED()){ return MBED_VALID_ID_LENGTH; }
    if(this.whiteLabelService.isABED()){ return ABED_VALID_ID_LENGTH; }
    return EQAO_VALID_ID_LENGTH;
  }

  getValidIdRegex = ():RegExp => {
    // todo:CONST store this in a central place in the repo 
    const REGEX_LOWERALPHA6_NUM5 = /^[a-z]{6}\d{5}$/;
    const REGEX_ALPHANUMERIC = /^[A-Za-z0-9]+$/;
    const REGEX_NUMERIC = /^\d+$/;

    // todo:DB_DATA_MODEL
    if(this.whiteLabelService.isNBED() || this.whiteLabelService.isMBED()) {
      return REGEX_LOWERALPHA6_NUM5;
    }
    if(this.isSasnLogin || this.isSampleSN){
      return REGEX_ALPHANUMERIC;
    }
    return REGEX_NUMERIC;
  }

  selectView(id: TeacherViews) {
    this.selectedView = id;
  }

  addVirtualClassEnableAccessCode(){
    this.isAddingVirtualClass = true;
  }

  addVirtualClassEntry(){
    let class_access_code = this.formGroup.controls.class_access_code.value;
    this.auth.apiCreate(this.routes.EDUCATOR_GUEST_STUDENTS, {
      class_access_code 
    },this.configureQueryParams())
    .then( res =>{
      this.g9demoService.addGuessClass(res.class)
      this.g9demoService.addGuessClassInvigilate(res.invigilate);
      this.loadGuestClassList()
      this.loadGuestClassInviligateList()
      this.clearStudentEntry();
      const students = this.g9demoService.getStudentsByClassroomId(this.classroomId);
      res.students.forEach(student =>{
        students.list.push(student);
      })
    })
    .catch(err =>{
      switch(err.message){
        case 'CLASS_DOES_NOT_EXIST':
          alert(this.lang.tra('ta_guessclass_not_exist'));
          break;
        case 'GUEST_CLASS_ALREADY_EXIST':
          alert(this.lang.tra('ta_guessclass_already_exist'));
          break;
        case 'GUEST_CLASS_IS_HOST_CLASS':
          alert(this.lang.tra('ta_guessclass_is_host'));
          break;
        case 'GUEST_CLASS_IS_SAME_SCHOOL':
          alert(this.lang.tra('ta_guessclass_in_same_school'));
          break;     
        default:
          break;
      }
    })
    this.isAddingVirtualClass = false;
  }

  revokeGuestClass(gclass){
    this.auth.apiRemove(this.routes.EDUCATOR_GUEST_STUDENTS, gclass.scg_id, this.configureQueryParams())
    .then( res =>{
      this.g9demoService.removeGuessClass(gclass.scg_id)
      this.loadGuestClassList()
      this.loadGuestClassInviligateList()
      const students = this.g9demoService.getStudentsByClassroomId(this.classroomId);
      const removeStudents = students.list.filter( student => student.scg_id == gclass.scg_id)
      removeStudents.forEach(student =>{
        const studentID = students.list.indexOf(student)
        students.list.splice(studentID,1)
      }) 
    })
  }

  configureQueryParams(){
    const classrooom = this.g9demoService.getClassroomById(this.classroomId)
    const param =  
    {
      query: {
        school_class_group_id:classrooom.group_id
      }
    }
    return param
  }

  getEducatorStudentsViewHeaderSlug() {
    if (this.whiteLabelService.isNBED() || this.whiteLabelService.isMBED()){
      return 'sa_educators_stu_header_nbed';
    }
    if (this.whiteLabelService.isABED()){
      return 'sa_educators_stu_header_ABED';
    }
    return 'sa_educators_stu_header'
  }
  
  // Print Student Information Modal
  studentInfoModalStart(){
    const config = { 
      studentList: this.studentList, 
      activeClassroom: this.activeClassroom, 
      additionalInvigilators: this.additionalInvigilators,
    };  
    const isProceedOnly = true;
    this.pageModal.newModal({
      type: PrintScanModal.STUDENT_INFO,  
      config,
      isProceedOnly,
      finish: this.studentInfoModalFinish
    });
  }

  studentInfoModalFinish = () => {
    this.pageModal.closeModal();
  }

  get isSasnLogin():boolean {
    return this.classroomService.isSASNLogin(+this.classroomId);
  }

  showStudentIdentificationNumber = () => {
    if(this.whiteLabelService.isNBED() || this.whiteLabelService.isMBED()){
      return this.isLookedupStudentGovId;
    }
    return false;
  }

  getNewStudentAccountSlug(){
    if(this.whiteLabelService.isABED()){
      return "abed_new_student_button"
    }
    return "new_student_account"
  }

  getAccessCodeSlug(){
    return this.isABED() ? 'abed_accesscose_for_sessions' : 'lbl_for_sessions';
  }

  isABED = () => this.whiteLabelService.isABED();
  isTestCenter = () => this.whiteLabelService.getSiteFlag('isTestCenter');


  onAddFromCsvClick(){
    const studentIdLabel = this.lang.tra(this.getStudentAccountLookupSlug())
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('lbl_add_stu_csv_file_instr_msg', undefined, {studentIdLabel}),
      btnProceedConfig: {caption: this.lang.tra('lbl_proceed')},
      btnCancelConfig: {caption: this.lang.tra('lbl_cancel')},
      confirm: () => {
        this.csvStudentInput.nativeElement.value = '';
        this.csvStudentInput.nativeElement.click();
      }
    })
  }

  async onAddFromCsvFileUpload(files: FileList){

    const studentIdLabel = this.lang.tra(this.getStudentAccountLookupSlug())
    const studentIdLabelLower = studentIdLabel.toLowerCase()

    if(files.length !== 1) {
      return;
    }
    const file = files.item(0)

    // Convert csv input to json, extract uids
    const jsonRes = await this.auth.excelToJson(file);
    const inputStudentIds: number[] = jsonRes['json']
    .filter(row => row[studentIdLabel] || row[studentIdLabelLower])
    .map(row => {
      return row[studentIdLabel] ?? row[studentIdLabelLower]
    })

    if (!inputStudentIds.length){
      return this.loginGuard.quickPopup(this.lang.tra('lbl_add_stu_csv_no_length_err', undefined, {studentIdLabel}));
    }

    const filteredStudents = [];
    const invalidStudentIds = [];

    inputStudentIds.forEach(inputStudentId => {
      const studentInSchool = this.schoolStudentsList.find(s => s.studentIdentificationNumber == inputStudentId)
      const isStudentInClass = studentInSchool && this.studentList.some(s => s.uid == studentInSchool.uid)
      if (studentInSchool && !isStudentInClass){
        filteredStudents.push(studentInSchool)
      } else {
        invalidStudentIds.push(inputStudentId)
      }
    })

    if (!filteredStudents.length) return this.loginGuard.quickPopup(this.lang.tra('lbl_add_stu_csv_no_valid_err', undefined, {studentIdLabel}));
    
    this.pageModal.newModal({
      type: TeacherModal.STUDENTS_MODAL_CSV_INPUT,
      confirmationCaption: 'btn_invig_add_students',
      config: {
        filteredStudents,
        invalidStudentIds
      },
      finish: async () => {
        this.addStudents();
      }
    });
  }

  startStudentsModal = () => {
    const filteredStudents = this.schoolStudentsList.filter(schoolStudent => !this.studentList.some(classStudent => schoolStudent.uid == classStudent.uid))
    if (!filteredStudents.length) return this.loginGuard.quickPopup('lbl_add_stu_no_valid_err')
    this.pageModal.newModal({
      type: TeacherModal.STUDENTS_MODAL,
      config: { filteredStudents },
      confirmationCaption: 'btn_invig_add_students',
      cancel: () => { 
      },
      finish: async () => {
        this.addStudents();
      }
    })
  }

  async addStudents(){
    this.selectedSchoolStudents.forEach(async schoolStudent => {
      await this.auth.apiFind(this.routes.EDUCATOR_STUDENTS, {
        query: {
          value: schoolStudent.studentIdentificationNumber,
          schoolClassId: this.classroomId,
          school_class_group_id:this.getClassGroupId(),
          sasn_login: this.isSasnLogin ? 1:0
        }
      }).then( users => {
        const { sameTwUser, differentTWuser, crossSchool, userToUpdate } =
        this.handleStudentUpdate(users, +this.classroomId);
            if (userToUpdate){
              this.updateStudentForm(userToUpdate);
              if (!this.studentExistsInAnotherClass) {
                this.cancelStudentEntry();
              }
            }
            if (this.studentExistsInAnotherClass) {
              this.moveStudentInAnotherClass(userToUpdate, sameTwUser, !!crossSchool, schoolStudent.studentIdentificationNumber);
            }
            else{
                if(!this.allowPASIUpdates) {
                  this.isLookedupStudentGovId = true;
                  this.loadStudentAccommodations();
                }else{
                this.isLookedupStudentGovId = true;
              }
              this.loadStudentList();
            }
      }).catch(error =>{
        if(error.message == "STUDENT_NOT_EXIST_IN_COURSE" ){
          window.alert(this.lang.tra("lbl_student_not_in_course"));
        }
        else if (error.message == "This ASN is a secondary ASN.") {
          window.alert(this.lang.tra("This ASN is a secondary ASN."));
        }
      })
    })
  }

  onSelectionConfirmed = (selectedRows: ITSchlStuModalData[]) => {
    console.log('Selected Rows on Confirmation:', selectedRows);
    this.selectedSchoolStudents = selectedRows;
  }

  private handleStudentUpdate(users: any[], classroomId: number) {
    let sameTwUser = users?.find(user => user.isInSameTW)
    let differentTWuser = users?.find(user => !user.isInSameTW)
    let crossSchool = users?.find(user => user.crossSchool)
  
    if (!sameTwUser && !differentTWuser) {
      this.studentExistsInAnotherClass = false;
    } else if (!sameTwUser && differentTWuser) {
      this.studentExistsInAnotherClass = !differentTWuser.classrooms.find(c => c.id == classroomId);
    } else {
      this.studentExistsInAnotherClass = !sameTwUser.classrooms.find(c => c.id == classroomId);
    }
  
    return {
      sameTwUser,
      differentTWuser,
      crossSchool,
      userToUpdate: sameTwUser ? sameTwUser : differentTWuser ? differentTWuser : null
    };
  }


  private updateStudentForm(userToUpdate: any) {
    const controls = this.formGroup.controls;
    controls.first_name.setValue(userToUpdate.first_name);
    controls.last_name.setValue(userToUpdate.last_name);
    controls.eqao_g9_course.setValue('1'); // temp
    // if(user.meta[TEMP_META_MAPPING.eqao_g9_course]){controls.eqao_g9_course.setValue(user.meta[TEMP_META_MAPPING.eqao_g9_course]);}
    controls.eqao_g9_gender.setValue(userToUpdate.meta[TEMP_META_MAPPING.eqao_g9_gender]);
  }

  private moveStudentInAnotherClass(userToUpdate: any, sameTwUser: any, crossSchool: boolean, eqao_student_gov_id) {
    // Make a temperary object for student
    const tmpStudent: any = {
      id: userToUpdate.id,
      first_name: userToUpdate.first_name,
      last_name: userToUpdate.last_name
    };
    // Add the necessary metas
    userToUpdate.meta.forEach(m => {
      tmpStudent[m.key_namespace] = tmpStudent[m.key_namespace] || {}
      tmpStudent[m.key_namespace][m.key] = m.value;
    });
    tmpStudent.class_code = [...new Set(userToUpdate.classrooms.map(c => c.name))];
    tmpStudent.classroomIds = [...new Set(userToUpdate.classrooms.map(c => c.id))];
    // Make a proper IStudentAccount object
    const student = this.g9demoService.apiToClientPayloadStudent(tmpStudent);
    student.StudentIdentificationNumber = eqao_student_gov_id;
    student.is_guest = !!crossSchool;
    const previousClassSameTW = sameTwUser?.classrooms.find( c => c.isInSameTW === true);
    this.classroomService
      .moveStudent(student, this.getClassGroupId(), previousClassSameTW?.group_id, !!crossSchool)
      .then(() => {
        this.cancelStudentEntry();
        // window.location.reload(); // to do, insert the student record             
        this.loadStudentList();
      })
  }
  
}
