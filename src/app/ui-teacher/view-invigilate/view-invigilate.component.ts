import { Component, OnInit, OnDestroy, Input, ViewChild } from '@angular/core';
import { ActivatedRoute, NavigationStart, Router, Event as NavigationEvent } from '@angular/router';
import { Subject, Subscription } from 'rxjs';
import { G9DemoDataService } from '../../ui-schooladmin/g9-demo-data.service'
import { IStudentAccount, IStudentTestSession } from '../../ui-schooladmin/data/types';
import { ASSESSMENT, IStudent, IStudentList, ISession, IClassroom, ISessionBegin, PrintScanModal, isAssessmentABED } from '../data/types';
import * as moment from 'moment-timezone';
import { cloneDeep } from 'lodash';
import { ListSelector } from '../../ui-partial/list-select.service';
import { ClassroomsService } from '../../core/classrooms.service';
import { SidepanelService } from '../../core/sidepanel.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { PageModalController, PageModalService } from "../../ui-partial/page-modal.service";
import { TeacherModal } from '../data/types';
import { LoginGuardService } from '../../api/login-guard.service';
import { RoutesService } from '../../api/routes.service';
import { AuthService } from '../../api/auth.service';
import { IStudentPositionUpdate, StudentG9ConnectionService } from '../../ui-student/student-g9-connection.service';
import { IStudentState, StudentListComponent } from '../student-list/student-list.component';
import { AssessmentService } from '../../core/assessment.service';
import { mtz } from '../../core/util/moment';
import { IStudentInfoConfig, showScanModal } from '../t-modal-student-info-chooser/t-modal-student-info-chooser.component';
import { ScanInfoService } from '../scan-info.service';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { StudentSoftLockService } from '../../ui-student/student-soft-lock.service';
import { OnlineOrPaperService } from '../../../../src/app/ui-testrunner/online-or-paper.service';
import { AccountType } from 'src/app/constants/account-types';
import { SchoolAdminView } from 'src/app/ui-schooladmin/view-schooladmin-dashboard/data/views';
import { ITimeExtUpdate } from '../widget-time-ext/widget-time-ext.component';
import { AssessmentSlugs, IQuestionScan, ITwtarRespSheetConfig } from './types';
import { LDBConfigLevel, LDBModal } from 'src/app/ui-partial/modal-ldb-config/modal-ldb-config.component';
import { calculateDateEnd } from '../../core/util/date';
import { IScoreEntryPatchData, IScoreEntryPatchParams } from '../score-entry/score-entry.component';
export interface IReviewModalConfig {
  studentStates: any,
  studentScanInfo: any,
  subSessions: any,
  asmtSlug?: ASSESSMENT
}

const unSubmitAllowedSlugs = [
  // Eqao Operational
  ASSESSMENT.OSSLT_OPERATIONAL,
  ASSESSMENT.PRIMARY_OPERATIONAL,
  ASSESSMENT.JUNIOR_OPERATIONAL,
  ASSESSMENT.TCLE_OPERATIONAL,
  ASSESSMENT.TCN_OPERATIONAL,
  ASSESSMENT.SCIENCES8_OPERATIONAL,
  // EQAO Sample
  ASSESSMENT.MBED_SAMPLE,
  ASSESSMENT.OSSLT_SAMPLE,
  ASSESSMENT.G9_OPERATIONAL,
  ASSESSMENT.G9_SAMPLE,
  ASSESSMENT.PRIMARY_SAMPLE,
  ASSESSMENT.JUNIOR_SAMPLE,
  // Abed ALL
  ASSESSMENT.ABED_SAMPLE,
  ASSESSMENT.ABED_OPERATIONAL,
  ASSESSMENT.ABED_PWR,
]

const INTERVAL_DURATION = 60 * 1000; // todo:CONST store this in a central place in the repo

@Component({
  selector: 'view-invigilate',
  templateUrl: './view-invigilate.component.html',
  styleUrls: ['./view-invigilate.component.scss']
})
export class ViewInvigilateComponent implements OnInit, OnDestroy {
  @ViewChild(StudentListComponent) studentListChildRef: StudentListComponent
  subSessionId: any;
  closeAllSubSessions: boolean;
  showSessionAEdit: boolean = false;
  showSessionBEdit: boolean = false;
  isShowSessionB:boolean = false;
  showSessionLangEdit: boolean = false;
  showSessionMathEdit: boolean = false;
  allowPASIUpdates: boolean = true;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private auth: AuthService,
    private routes: RoutesService,
    private lang: LangService,
    private sidePanel: SidepanelService,
    public loginGuard: LoginGuardService,
    public g9demoService: G9DemoDataService,
    private breadcrumbsService: BreadcrumbsService,
    private classroomService: ClassroomsService,
    private pageModalService: PageModalService,
    private studentG9Connection: StudentG9ConnectionService,
    private assessmentService: AssessmentService,
    private scanInfo: ScanInfoService,
    private whitelabelService: WhitelabelService,
    private studentSoftLock: StudentSoftLockService,
    private onlineOrPaper: OnlineOrPaperService
  ) {

  }

  // title=this.lang.tra('g9_assess_title');
  isSubmitted = true;
  isActive = true;
  routeSub: Subscription;
  initSessionInfoSub = new Subject();
  session_id: string;
  classroomId: string;
  activeSession: {
    id?: number,
    is_paused?: boolean,
    date_time_start?: string,
    duration_m?: number,
    isDurationEnforced?: boolean,
    time_ext_m?: number
    activeSubSessions?: any[]
    completedSubSessions?: any[]
    subSessions?: any[]
    is_no_td_order?: boolean,
    asmt_slug?: string,
    test_attempt_test_status?: string,
    [param:string]: any,
  } = {};
  isScoreEntrySession: boolean = false;
  isScoreEntryLoaded: boolean = false;
  sessionReport: any;
  test_designs: any;
  selectedTestDesign;
  sampleStudents: any[];
  studentList: IStudentAccount[] = []
  studentEntry = {
    oen: '',
    firstname: '',
    lastname: '',
    accommodation: ''
  }
  studentReviewedStatusMap: any;
  hasReviewMapInit: boolean = false;
  softLockCount = new Map();
  testAttemptSoftLockStatus = new Map();
  isOpenSessionEnabled: boolean
  isCloseSessionEnabled: boolean;
  didSessionStart: boolean = true;
  isSessionPaused: boolean = false
  showSoftlockModal: boolean = false;
  breadcrumb = [];
  pageModal: PageModalController;
  TeacherModal = TeacherModal;
  LDBModal = LDBModal;
  isShowHeaderInstr: boolean = false; // because it needs to be customized based on the assessment type
  numUnlockedStudents = 0;
  activeClassroom: IClassroom;
  studentStates: { [key: number]: IStudentState };
  studentSocketState: { [key: number]: any } = {};
  subSessions;
  activeSubSessions;
  completedSubSessions:boolean = false;
  isSessionOpened: boolean = true;
  isLoaded: boolean;
  pauseStudentFunc: any;
  currentStudentOpenForSoftlock: number; // uid
  studentLockActionMap: any;
  studentsToTrackLockAction: any;
  isUnsubmitToggleVisible: boolean = false;
  isUnsubmitBtnVisiable: boolean = false;
  isSecreteUser
  currentQueryParams
  connectedStudentsSub;
  disconnectedStudentsSub:Subscription;
  classroomServiceSub: Subscription;
  updateStudentPositionSub;
  updateStudentSoftLockSub;
  userSub: Subscription;
  isNoTdOrder: boolean;
  asmtSlug: ASSESSMENT;
  PrintScanModal = PrintScanModal;
  allowedUnsubmitAssessments = new Set([ASSESSMENT.OSSLT_OPERATIONAL, ASSESSMENT.TCLE_OPERATIONAL, ASSESSMENT.TCN_OPERATIONAL, ASSESSMENT.SCIENCES8_OPERATIONAL])
  isScanSession: boolean;
  questionsForScan: IQuestionScan[]
  isRefreshingSessionsInfo: boolean = false;
  reportIssuesCategories: any;
  questionId: number = null;
  currentStudentDetail: any;
  accommodationList: any[];
  walkinStudents: any = [];
  resp_sheet_config?: ITwtarRespSheetConfig;
  isBulkUploadAllowed: boolean;
  isSingleUploadAllowed: boolean;
  printBeforedateEnd?: Date;
  uploadUntildateEnd?: Date;
  IS_MAX_TIME_CHECK_ACTIVE:boolean = false; // temp feature flag, won't need this later

  isUserSoftLockNotified = false;
  ngOnInit(): void {
    this.g9demoService.setIsFromInvig(true)
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.sidePanel.deactivate();
    this.loginGuard.activate();
    this.route.queryParams.subscribe((queryParams) => {
      const isSchlAdmin = !!queryParams['isSchlAdmin'];
      if (isSchlAdmin){
        this.g9demoService.setIsFromSchoolAdmin(true)
      } else {
        this.g9demoService.setIsFromSchoolAdmin(false)
      }
      this.currentQueryParams = {
        school: queryParams['school'],
        isSchlAdmin: queryParams['isSchlAdmin']
      }
    });
    if(this.g9demoService.getIsFromSchoolAdmin()){
       this.classroomService.reloadSchool()
     }  
    this.classroomServiceSub = this.classroomService.sub().subscribe(data => {
      if (data) {
        this.loadRoute();
        if(this.isABED())this.checkIfIntroVidDismissed();
      }
    })
    this.initSessionInfoSub.subscribe((data) => {
      if (data) {
        this.loadStudentList()
      }
    })
    window.onbeforeunload = () => this.ngOnDestroy();
    this.allowPASIUpdates = this.g9demoService.getPASILock();
  }

  // todo:DB_DATA_MODEL (eqao)
  isPrimaryOrJunior() {
    return (this.asmtSlug === ASSESSMENT.PRIMARY_SAMPLE || this.asmtSlug === ASSESSMENT.PRIMARY_OPERATIONAL || this.asmtSlug === ASSESSMENT.JUNIOR_SAMPLE || this.asmtSlug === ASSESSMENT.JUNIOR_OPERATIONAL);
  }
  isPJSampleTest() {
    return (this.asmtSlug === ASSESSMENT.PRIMARY_SAMPLE || this.asmtSlug === ASSESSMENT.JUNIOR_SAMPLE);
  }
  isPJOperationalTest() {
    return (this.asmtSlug === ASSESSMENT.PRIMARY_OPERATIONAL || this.asmtSlug === ASSESSMENT.JUNIOR_OPERATIONAL);
  }
  isG9OrOsslt() {
    return (this.asmtSlug === ASSESSMENT.G9_SAMPLE || this.asmtSlug === ASSESSMENT.G9_OPERATIONAL || this.asmtSlug === ASSESSMENT.OSSLT_SAMPLE || this.asmtSlug === ASSESSMENT.OSSLT_OPERATIONAL);
  }  
  isOssltOperationalTest() {
    return this.asmtSlug === ASSESSMENT.OSSLT_OPERATIONAL;
  }

  getSessionStart(session) {
    this.setSessionOpenState(session)
  }

  checkIfUnsubmitToggleVisible() {
    return this.isUnsubmitToggleVisible;
  }

  isUnsubmitAllowed = (slug: ASSESSMENT) => this.allowedUnsubmitAssessments.has(slug)

  toggleUnsubmitFeature() {
    this.isUnsubmitBtnVisiable = !this.isUnsubmitBtnVisiable
    return this.isUnsubmitBtnVisiable;
  }

  // isWritingPaper(student: IStudentAccount) {
  //   const isCrScanDefault = this.g9demoService.getPropVal(student, 'IsCrScanDefault', this.activeClassroom.curricShort);
  //   return this.onlineOrPaper.getPaperVal(isCrScanDefault, this.activeClassroom.curricShort);
  // }

  getTimeRemaining():number{
    return 0
  }

  initPaperOnlineStudents() {
    const students: IStudentAccount[] = this.studentList;
    const studentsWritingConfig = {};
    const isPaper = true
    students.forEach(stu => {
      // const writingPaperBool = isPaper;
      studentsWritingConfig[stu.uid] = isPaper;
    })
    this.onlineOrPaper.initStudentsWritingType(studentsWritingConfig);
  }

  isSettingClassExtension:boolean;
  toggleClassExtension(){
    this.isSettingClassExtension = ! this.isSettingClassExtension;
  }

  async updateSessionTimeExt(payload: ITimeExtUpdate){
    const {time_ext_m} = await this.auth.apiPatch('public/educator/session-time', this.session_id, {
      updateOption: payload.updateOption,
      updateVal: payload.minutes,
    });
    this.time_ext_m = time_ext_m
    payload.resolve({time_ext_m});
  }

  hasIndivExtensions(){

  }

  numIndivExtensions(){

  }

  maxTimeRemaining(){
    return 0
  }

  setSessionOpenState(session) {
    let currDate = new Date();
    let sessionDate = new Date(session.date_time_start);
    if (currDate.getTime() > sessionDate.getTime()) {
      const currentSession = this.getScheduledAssessmentById(session.id);
      if (currentSession) 
      {
        const currentOpenSession = {
          date_time_start:currentSession.date_time_start,
          slug:currentSession.slug,
          access_code:currentSession.access_code,
          school_class_id:currentSession.school_class_id,
          test_session_id: session.id,
          dateTimeStartLong: this.formatDateTime(currentSession.date_time_start, currentSession.slug),
          name: currentSession.name,
          timeDirectStart: currentSession.timeDirectStart,
          hourDirectStart: currentSession.hourDirectStart,
        }
        this.activeSession = currentOpenSession;
        // //console.log('activeSession[setSessionOpenState]', this.activeSession)
        if (this.activeClassroom.openAssessments){
          this.activeClassroom.openAssessments.push(currentOpenSession);
        }
        this.removeAssessment(this.activeClassroom.scheduledAssessments, session.id);
      }

      this.isSessionOpened = true;

    }
    else {
      //scheduled for the future
      this.isSessionOpened = false;
    }
  }

  formatHourDirectStartTime(dateTime: string, sessionAsmtSlug: string): string {
    return mtz(dateTime).format(this.lang.tra('datefmt_dashboard_short'));
    // console.log(dateTime)
    // if (isAssessmentABED(sessionAsmtSlug)) {
    //   return this.auth.formatDateForWhitelabel(dateTime, moment.tz.guess(), this.lang.c()); //Change to user local timezone
    // }
    // const mStart = moment.tz(dateTime, moment.tz.guess());
    // let timezone = mStart.zoneAbbr();
    // const mStartTime = mStart.format('h:mm A');
    // return `${mStartTime} ${timezone}`;
  }

  // todo:generalize
  formatDateTime(dateTime: string, sessionAsmtSlug: string): string {
    return mtz(dateTime).format(this.lang.tra('datefmt_dashboard_short'));
    // if (isAssessmentABED(sessionAsmtSlug)) {
    //   return this.auth.formatDateForWhitelabel(dateTime, this.whitelabelService.getTimeZone(), this.lang.c());
    // }
    // // todo:DB_DATA_MODEL
    // const isG9orOsslt = sessionAsmtSlug == ASSESSMENT.G9_SAMPLE 
    //   || sessionAsmtSlug == ASSESSMENT.G9_OPERATIONAL 
    //   || sessionAsmtSlug == ASSESSMENT.OSSLT_SAMPLE 
    //   || sessionAsmtSlug == ASSESSMENT.OSSLT_OPERATIONAL; 
    // const mStart = moment.tz(dateTime, moment.tz.guess());
    // let timezone = mStart.zoneAbbr();
    // timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label');

    // // todo:DB_DATA_MODEL
    // if (isG9orOsslt){
    //   return `${mStart.format(this.lang.tra('datefmt_sentence'))} ${timezone}`
    // }
    // else {
    //   return `${mStart.format(this.lang.tra('pj_datefmt_sentence'))}`;
    // }
  }

  addRecentSession(sessionToClose){
    const currentSession = this.getAssessmentById(sessionToClose.test_session_id);
    if (currentSession) {
      const sessionCloseTime = new Date()
      const mClosed = moment.tz(sessionCloseTime, moment.tz.guess());
      const timeDirectClose = mClosed.format(this.lang.tra('datefmt_day_month'));
      const session = {
        date_time_start:currentSession.date_time_start,
        slug:currentSession.slug,
        access_code:currentSession.access_code,
        school_class_id:currentSession.school_class_id,
        test_session_id: currentSession.id || currentSession.test_session_id,
        timeDirectClose,
        dateTimeStartLong: this.formatDateTime(currentSession.date_time_start, currentSession.slug),
        test_duration: currentSession.test_duration,
        name: currentSession.name,
        timeDirectStart: currentSession.timeDirectStart,
        hourDirectStart: currentSession.hourDirectStart,
      }
      return session;
    }
  }

  isBulkUpload() {
    return this.scanInfo.isUploadingBulk;
  }

  isBulkPrint() {
    return this.scanInfo.isPrintingBulk;
  }

  renderTitle(title: string) {
    // todo:DB_DATA_MODEL
    switch (title) {
      case "G9_OPERATIONAL": return "Grade 9 Assessment of Mathematics";
      case "G9_SAMPLE": return "Sample Test";
    }
  }

  renderDay(m) {
    return m.format(this.lang.tra('datefmt_day_month'));
  }

  renderTime(m) {
    return m.format(this.lang.tra('timefmt_hour_time'));
  }

  //depricated
  ensureNewStudent(uid){
    const isRegularStudent       = this.studentList.findIndex(s => s.uid == uid)    != -1;
    const isCurrentWalkinStudent = this.walkinStudents.findIndex(s => s.uid == uid) != -1;
    return !isRegularStudent && !isCurrentWalkinStudent;
  }

  ensureNewStudentToTheRegularList(uid){
    const isRegularStudent       = this.studentList.findIndex(s => s.uid == uid)    != -1;
    return !isRegularStudent;
  }

  ensureNewStudentToTheWaitingRoom(uid){
    const isCurrentWalkinStudent = this.walkinStudents.findIndex(s => s.uid == uid) != -1;
    return !isCurrentWalkinStudent;
  }

  async checkNewStudent(connectedStudents){
      //this block of logic is to identify if a student has just logged in and they are not currently listed on the regular list
      for(let student of connectedStudents){
        if(this.ensureNewStudentToTheRegularList(student.uid) && student.uid){
          //we attempt to identify the student
          const identifiedStudent = await this.auth.apiFind(this.routes.ABED_WALKN_STUDENT, {
            query:{
              classId: this.classroomId,
              uid: student.uid,
              schl_group_id: this.g9demoService.getSchoolGroupId()
            }
          }).catch((e)=>{
            console.log(e.message)
          });
          if(!identifiedStudent.length){
            //if no student is identified then end the current loop cycle and move to the next student 
            continue;
          }
          const currentStudent = identifiedStudent[0];
          if(!currentStudent.is_walkin && this.ensureNewStudentToTheRegularList(currentStudent.uid)){
            //if the identified student is not a walkin (new property pulled from the api based on roletype) and they don't exist in the regular student list, then trigger the 
            //loca walkin acceptance logic, which simply removes the student from the waiting room and adds them to the regular list
            this.localWalkinAcceptance(currentStudent.uid)
          }
          if(!this.ensureNewStudentToTheWaitingRoom(currentStudent.uid)){
            //if the student already exists in either regular or walkin lists within the ui then end the current loop cycle, 
            //no need to add the same student twice 
            continue;
          }
          //by this point, we confirmed the student is specifically missing from the waiting room so we add them 
          if(currentStudent.is_walkin) {
            this.walkinStudents.push(currentStudent);
          }
        }
      }
  }

  private updateStudentSocketStates(connectedStudents: any[]) {
    // console.log('updateStudentSocketStates', connectedStudents)

    if(this.isABED()){
      this.checkNewStudent(connectedStudents)
    }
    const prevStudentSocketState = cloneDeep(this.studentSocketState);
    this.studentSocketState = {};
    for (let student of connectedStudents) {
      if (prevStudentSocketState[student.uid]) {
        this.studentSocketState[student.uid] = prevStudentSocketState[student.uid];
      } 
      else {
        this.studentSocketState[student.uid] = {
          stageIndex: student.stageIndex,
          questionCaption: student.questionCaption,
          questionIndex: student.questionIndex,
          submitted: student.submitted,
          softLock: student.softLock,
          numTimesOffScreen: 0,
          isStateless: true,
        }
      }
    }
  }
  
  private updateDisconnectedSocketStates(disconnectedUids: any[]) {
    //this.studentSocketState = {};
    //console.log("disconnected sub",disconnectedUids)
    for (let studentUid of disconnectedUids) {
      //console.log(studentUid,"LOGGING OFFLINE UID")
      delete this.studentSocketState[studentUid];
    }
  }

  onResetStudentSoftLock(uid){
    // The line below would reset the softLockCount when the teacher dismissed the softlock notification 
    // this.softLockCount.set(uid,0); 
    // this.studentSocketState[uid]['softLock'] = 1; // CLOSER_LOOK_20210807 this line was introduced without the line below on a March 18  (which was added on a separate branch on March 23). Without knowing too much about how this piece of the code operates, it seems like a safe bet to activate this chunk and have it set back to anotehr value based on the below condition but I may be wrong
    if(this.studentSocketState[uid]){

      this.studentSocketState[uid]['softLock'] = 0
      //this.studentSocketState[uid]['softLock'] = this.softLockCount
    }
  }

  pauseStudentTest(student?: IStudentAccount, isSilent?: boolean) {
    // this.pauseStudentFunc();
    this.studentListChildRef.pauseStudentAndDismissSoftLockNotification(student, isSilent);
    this.toggleSoftLockModal({openModal: false});
  }


  unPauseStudentTest() {
    this.studentListChildRef.unPauseStudentSoftLock();
    this.toggleSoftLockModal({openModal: false});
  }
  
  disableSoftLockForAttempt(){
    this.studentListChildRef.disableStudenSoftLockForAttempt();
    this.toggleSoftLockModal({openModal: false});
  }

  // openDismissPopup() {
  //   this.studentListChildRef.openDismissModal();
  //   this.toggleSoftLockModal({openModal: false});
  // }

  private updateStudentPosition(studentPositionUpdate: IStudentPositionUpdate) {
    if (!studentPositionUpdate) {
      return;
    }

    console.log('updateStudentPosition', studentPositionUpdate) // temp

    if (this.studentSocketState[studentPositionUpdate.uid]) {
      // need to keep the previous numTimesOffScreen to prevent softlock notification saying 'undefined' numtimesOffScreen
      const prevNumTimesOffScreen = this.studentSocketState[studentPositionUpdate.uid]['numTimesOffScreen'];
      this.studentSocketState[studentPositionUpdate.uid] = {};
      this.studentSocketState[studentPositionUpdate.uid]['numTimesOffScreen'] = prevNumTimesOffScreen;
    }

    for(let [key, _val] of Object.entries(studentPositionUpdate)) {
      const val:{subSessionIndex:number, submitted:boolean|number} =  <any>_val;
      if(key === "uid"){
        continue;
      } 
      else if(key === "submitConfig" && val !== undefined && val !== null) {
        this.studentSocketState[studentPositionUpdate.uid][`submitted${val.subSessionIndex}`] = val.submitted; 
        //Update the student states, since we know it will now contain updated lock information for that student
        this.loadSessionInfo();
      } 
      else if(key === "softLock" && val != null && val !== undefined){
        // let softLockValue = this.studentSocketState[studentPositionUpdate.uid][key];// CLOSER_LOOK_20210807 this block is slightly older (5 days) and less detailed than what is shown above... it looks like both are doing the same thing
        if (this.softLockCount.has(studentPositionUpdate.uid)){
          let softLockCount = this.softLockCount.get(studentPositionUpdate.uid);

          // do not increase the softLockCount if the student refreshes their page or exits fullscreen
          if (this.studentSocketState[studentPositionUpdate.uid] && this.studentSocketState[studentPositionUpdate.uid]['numTimesOffScreen'] !== studentPositionUpdate['numTimesOffScreen']) {
            softLockCount++;
          }

          // update softLockCount in the teacher's browser, mainly used for continuing the count if the student refreshes their page
          const numTimesOffScreen = Math.max(softLockCount, studentPositionUpdate['numTimesOffScreen']);
          this.softLockCount.set(studentPositionUpdate.uid, numTimesOffScreen);
          this.studentSocketState[studentPositionUpdate.uid][key] = this.softLockCount;
          // this.studentSocketState[studentPositionUpdate.uid][key] = 1 // CLOSER_LOOK_20210807 this block is slightly older (5 days) and less detailed than what is shown above... it looks like both are doing the same thing
        }
        else{
          if (this.studentSocketState[studentPositionUpdate.uid]) {
            const softLockCount = this.softLockCount.set(studentPositionUpdate.uid,1)
            this.studentSocketState[studentPositionUpdate.uid][key] = softLockCount;
            // this.studentSocketState[studentPositionUpdate.uid][key]++;// CLOSER_LOOK_20210807 this block is slightly older (5 days) and less detailed than what is shown above... it looks like both are doing the same thing
          }
        }
        const studentState = this.studentSocketState[studentPositionUpdate.uid]?.softLock;
        const isSoftlockDisabled = this.testAttemptSoftLockStatus.get(studentPositionUpdate.uid)?.is_soft_lock_disabled;
        if(isSoftlockDisabled !== 1 && studentState && studentState.get(studentPositionUpdate.uid) > 0 && !this.getEnablePromptBeforeSoftLockPause()){
          const student = this.getStudentByUid(studentPositionUpdate.uid)
          this.pauseStudentTest(student, true);
        }
        // this.studentSocketState[studentPositionUpdate.uid][key] = val;// CLOSER_LOOK_20210807 this block is slightly older (5 days) and less detailed than what is shown above... it looks like both are doing the same thing
      }
      else if(val !== undefined && val !== null) {
        if (this.studentSocketState[studentPositionUpdate.uid]) {
          // if(this.softLockCount.has(studentPositionUpdate.uid)){
          //   // let softLockCount = this.softLockCount.get(studentPositionUpdate.uid) 
          //   // this.studentSocketState[studentPositionUpdate.uid]['softLock'] = this.softLockCount;
          // }
          this.studentSocketState[studentPositionUpdate.uid][key] = val;
        }
        // update student's numTimesOffScreen, mainly used for continuing the count if the student refreshes their page
        if (key === "numTimesOffScreen" && this.studentSocketState[studentPositionUpdate.uid]) {
          const softLockCount = this.softLockCount.get(studentPositionUpdate.uid);
          const numTimesOffScreen = Math.max(softLockCount, studentPositionUpdate['numTimesOffScreen']);
          this.studentSocketState[studentPositionUpdate.uid]['numTimesOffScreen'] = numTimesOffScreen;
        }
      }
    }
  }

  startSession() {
    this.didSessionStart = !this.didSessionStart
  }

  toggleSoftLockModal(studentWarningConfig: any) {
    //this.showSoftlockModal = studentWarningConfig.openModal;
    if(studentWarningConfig.openModal){
      this.currentStudentOpenForSoftlock = studentWarningConfig.openModal ? studentWarningConfig.uid : null;
      this.pageModal.newModal({
        type: TeacherModal.SOFT_LOCK_MODAL,
        config: {},
        confirmationCaption: this.lang.tra("btn_pause"),
        cancel: () => { 
          //this.openDismissPopup()
        },
        finish: () => this.pauseStudentTest(null, true)
      })
    };
  }

  getNumTimesOffScreenProps() {
    return {NUM_TIMES: this.studentSocketState[this.currentStudentOpenForSoftlock]['numTimesOffScreen']};
  }

  togglePauseSession() {
    if (!this.didSessionStart) {
      this.startSession();
    }
    else {
      const is_paused = !this.isSessionPaused
      const params = this.classroomService.constructClassGroupIdQuery(this.classroomId)
      this.auth
        .apiPatch(this.routes.EDUCATOR_SESSION, this.session_id, { is_paused }, params)
        .then(() => {
          this.isSessionPaused = is_paused
        })
    }
  }

  loadRoute() {
    if (this.routeSub) { this.routeSub.unsubscribe(); }
    this.routeSub = this.route.params.subscribe(async (params) => {
      this.session_id = params['asmtSessionId'];
      this.classroomId = params['classroomId'];
      await this.g9ConnectionInit();

      this.sidePanel.classroomId = this.classroomId;
      this.isSessionInitializing = true;
      this.initInterval();
      this.loadSessionInfo();
      this.initSessionInfoSub.next(true);
      this.updateBreadCrumbs();
    });
    this.route.queryParams.subscribe(queryParams => {
      // if (queryParams.showUnsubmit) {
      //   queryParams.showUnsubmit === 'true' ? this.isUnsubmitToggleVisible = true : this.isUnsubmitToggleVisible = false;
      // }
      if (queryParams.isSecreteUser) {
        this.isSecreteUser = queryParams.isSecreteUser 
        this.isUnsubmitToggleVisible = true // allow isSecreteUser to unsubmit G9 as well
        this.isForceUnsubmitAbility = true
      }
    })
  }
  isForceUnsubmitAbility

  g9ConnectionInit() {
    if(!this.userSub) {
      this.userSub = this.auth.user().subscribe( async (info) => { //ensure that the uid is set before connecting
        if (this.classroomId !== undefined) {
          const classroom = this.classroomService.getClassroomById(this.classroomId)
          if (classroom) {
            this.studentG9Connection.setClassId(classroom.group_id);
          }
          if (this.connectedStudentsSub) {
            console.log('skipping student connection')
          }
          else{
            console.log('init student connection')
            this.connectedStudentsSub = this.studentG9Connection.connectedStudentsSub
              .subscribe(connectedStudents => {
                console.log('updating connnected students')
                this.updateStudentSocketStates(connectedStudents)
              });
    
            this.updateStudentPositionSub = this.studentG9Connection.updateStudentPositionSub
              .subscribe((studentPositionUpdate: IStudentPositionUpdate) => {
                this.updateStudentPosition(studentPositionUpdate);
              })

              this.disconnectedStudentsSub = this.studentG9Connection.disconnectedStudentsSub
                 .subscribe(uid => {
                if(uid && uid.length > 0){
                  this.updateDisconnectedSocketStates(uid)
                }
              });
            await this.studentG9Connection.connect();
          }
        }
      }) 
    }
  }

  updateBreadCrumbs() {
    const classroomName = this.g9demoService.getClassroomNameById(this.classroomId)
    const basePath = `/${this.lang.c()}/educator`;
    const asmtName = this.activeSession.name || this.lang.tra('lbl_assessment_session');
    if(this.g9demoService.getIsFromSchoolAdmin()){
      this.breadcrumb = [
        this.breadcrumbsService._CURRENT(this.lang.tra('ABED_sa_dashboard_school_admin'), `/${this.lang.c()}/school-admin/dashboard`, this.currentQueryParams),
        this.breadcrumbsService._CURRENT(this.lang.tra('abed_ass_sess'), `/${this.lang.c()}/school-admin` + '/sessions', this.currentQueryParams),
        this.breadcrumbsService._CURRENT(asmtName, basePath + '/assessment/' + this.classroomId + '/' + this.session_id, this.currentQueryParams),
      ];
    }else{
      this.breadcrumb = [
        // here
        this.breadcrumbsService._CURRENT(this.lang.tra("lbl_classes_groups_ABED"), basePath, this.currentQueryParams),
        this.breadcrumbsService._CURRENT(classroomName, basePath + '/classrooms/' + this.classroomId, this.currentQueryParams),
        this.breadcrumbsService._CURRENT(asmtName, basePath + '/assessment/' + this.classroomId + '/' + this.session_id, this.currentQueryParams),
      ];
    }  
  }
  
  onStudentLockUpdate(res: { num: number }) {
    this.numUnlockedStudents = res.num;
  }

  reportIssue(isSkipConfirmation?:boolean, customCaption?:string, isSubmitAfterReport?:boolean) {
    if (isSkipConfirmation){
      this.reportModalStart()
    }
    else {
      this.loginGuard.confirmationReqActivate({
        caption: 'abed_view_invigilate_report_issue_disc',
        width: '40em', // todo:CSS_STYLES?
        confirm: () => {
          this.reportModalStart(customCaption, isSubmitAfterReport)
        }
      })
    }
  }
  
  isLanguageVisibleForFIClass(): boolean {
    const isFI = this.activeClassroom?.is_fi == 1 ? true : false;
    if(isFI && this.g9demoService.schoolDist[0].fi_option === 'C' ) {
      return false;
    }
    return true;
  }

  get isFIOptionC():boolean {
    return this.isLanguageVisibleForFIClass() ? false : true;
  }

  isSessionInitializing:boolean;
  isAllowLoadRetry:boolean;
  isScanStateInited:boolean;
  loadSessionInfo() {
    this.isAllowLoadRetry = false
    this.activeClassroom = this.g9demoService.teacherClassrooms.map[this.classroomId];
    const activeSession = this.g9demoService.getSessionByClassroomId(this.classroomId, this.session_id);

    activeSession.dateTimeStartLong = this.formatDateTime(activeSession.date_time_start, activeSession.slug);
    activeSession.hourDirectStart = this.formatHourDirectStartTime(activeSession.date_time_start, activeSession.slug)
    this.activeSession = activeSession;
    // console.log('activeSession[loadSessionInfo]', this.activeSession)
    //const params = this.classroomService.constructClassGroupIdQuery(this.classroomId);
    const classroom = this.classroomService.getClassroomById(this.classroomId)
    if(!classroom){
      return;
    }
    const params = {
      query: {
        school_class_group_id: classroom.group_id,
        school_class_id:this.classroomId
      }
    }

    this.studentG9Connection.logWebsocketInfo('WS_EDUCATOR_HEARTBEAT', this.studentSocketState)

    this.auth
        .apiGet(this.routes.EDUCATOR_SESSION, this.session_id, params)
        .then(res => {
          this.handleSessionInfoRes(res);
          return res;
          // this.activeSession.dateTimeStartLong = this.isG9OrOsslt() ? `${mStart.format(this.lang.tra('datefmt_sentence'))} ${timezone}` : `${mStart.format(this.lang.tra('pj_datefmt_sentence'))}`;
        })
        .then(res => {
          if (this.isScanSession && !this.isScanStateInited) {
            this.isScanStateInited = true;
            this.scanInfo.loadScanInfo(+this.session_id, this.activeClassroom.group_id)
          }
          this.togglethisIsSessionInitializing(false);
          this.calculateDateEndForPrintAndUpload(res);
        })
        .catch(e =>{
          console.log("e: ", e);
          if(e.message == "NO_TEST_DESIGN_RULE_MATCH"){
            // close activeSession
            this.loginGuard.confirmationReqActivate({
              caption: this.lang.tra('unabled_to_initialize_session'),
              confirm: () => {
                this.navigateBack();
              },
              btnCancelConfig:{
                hide: true
              }
            })
          }
          setInterval(()=>{
            this.isAllowLoadRetry = true
          }, 30*1000)
        })
  }
    ngOnDestroy() {
      this.clearInterval();
      this.studentG9Connection.disconnect();
      if(this.connectedStudentsSub) {
        this.connectedStudentsSub.unsubscribe();
      }
      if(this.updateStudentPositionSub) {
        this.updateStudentPositionSub.unsubscribe();
      }
      if(this.userSub) {
        this.userSub.unsubscribe();
      }
      if (this.initSessionInfoSub) {
        this.initSessionInfoSub.unsubscribe();
      }
      if(this.classroomServiceSub){
        this.classroomServiceSub.unsubscribe();
      }
    }

    ticker
    initInterval() {
      if(!this.ticker){
        this.ticker = window.setInterval(() => {
          this.loadSessionInfo();
          // this.refreshComputedState();
        }, INTERVAL_DURATION)
      }  
    }

    clearInterval() {
      window.clearInterval(this.ticker);
    }


    loadStudentList() {
      const students = this.g9demoService.getStudentsByClassroomId(this.classroomId)
      if(students){
        this.studentList = this.g9demoService.getStudentsByClassroomId(this.classroomId).list;
        this.initPaperOnlineStudents();
      }  
    }

    triggerSubmitAssessmentSessions() {
      if (!this.whitelabelService.getSiteFlag('IS_INVIG_FINAL_COMMENT') || this.isScoreEntrySession){
        this.submitAssessmentSessions();
      }
      else {
        this.loginGuard.confirmationReqActivate({
          caption: 'Would you like to provide additional feedback on this session?',
          confirm: () => this.reportIssue(true, 'Session Feedback', true),
          close: () => this.showScanModal() ? this.triggerPaperResponsesReview() : this.delayedSubmitAssessmentSessions(),
          btnProceedConfig: {caption:'Yes'},
          btnCancelConfig:  {caption:'No'},
        })
      }
    }

    
    triggerPaperResponsesReview() {

      const getMissingResponses = () => {
        let missingScans = 0;
        let unconfirmedScans = 0;
        if(this.studentList.length > 0){
          const students = this.studentList.map(student => {
            const currentStudentState = this.studentStates[student.uid];
            const studentSubSessionsState = currentStudentState['subSessions'];
            return {
              uid: student.id,
              studentSubSessionsState,
              isPaperFormatDefault: false
            };
          });
          students.forEach(student => {
            this.scanInfo.getMissingUnconfirmedScans(this.scanInfo.studentScanInfoMap, student, _ => {missingScans++;} , _ => {unconfirmedScans++;});
          })
        }
          return missingScans;
      } 
      const missingScans = getMissingResponses();
      if(this.isScanSession &&  missingScans > 0) {
        const confirmMsg = missingScans == 1 ? this.lang.tra('lbl_invig_one_missing_scan_submit_popup') : this.lang.tra('lbl_invig_missing_scan_submit_popup', undefined, {missing_scans: missingScans})
        setTimeout(() => {
          this.loginGuard.confirmationReqActivate({
            caption: confirmMsg,
            confirm: () => this.delayedSubmitAssessmentSessions(),
            btnProceedConfig: {caption:'lbl_yes'},
            btnCancelConfig:  {caption:'lbl_no'},
          })
        }, 200)
      } else {
        this.delayedSubmitAssessmentSessions();
      }
    }

    delayedSubmitAssessmentSessions() {
      setTimeout(() => {
        this.submitAssessmentSessions()
      }, 200)
    }

    submitAssessmentSessions() {
      let slug = this.activeSession.slug;
      console.log('slug', slug)
      let caption = this.lang.tra('warn_submit_student_assessment2_abed');
      let subCaption = this.whitelabelService.isABED()
      ? `${this.lang.tra("abed_type_yes")}\n${this.lang.tra("abed_cancel_to_return_test_session")}`
      : this.lang.tra('warn_submit_student_assessment2_proceed');
      
      if(this.isFIOptionC) {
        if(slug === ASSESSMENT.PRIMARY_SAMPLE) {
          caption = this.lang.tra('pj_fi_sample_submit_results_caption');
          subCaption = this.lang.tra('pj_fi_submit_results_subcaption_1');
        }
        if(slug === ASSESSMENT.PRIMARY_OPERATIONAL) {
          caption = this.lang.tra('pj_fi_submit_results_caption');
          subCaption = this.lang.tra('pj_fi_submit_results_subcaption_1');
        }
      } 
      else {
        if(slug === ASSESSMENT.PRIMARY_SAMPLE || slug === ASSESSMENT.JUNIOR_SAMPLE) {
          caption =  this.lang.tra('pj_sample_submit_results_caption')
          subCaption = this.lang.tra('pj_sample_submit_results_subcaption_1');
        } 
        else if(slug === ASSESSMENT.G9_SAMPLE || slug === ASSESSMENT.G9_OPERATIONAL) {
          caption =  this.lang.tra('warn_submit_student_assessment3')
          subCaption = this.lang.tra('warn_submit_student_assessment2_proceed');
        } 
        else if(slug === ASSESSMENT.OSSLT_SAMPLE || slug === ASSESSMENT.OSSLT_OPERATIONAL){
          caption =  this.lang.tra('warn_submit_student_assessment2')
          subCaption = this.lang.tra('warn_submit_student_assessment2_proceed');
        }
      }

      if (this.isScoreEntrySession) {
        caption = this.lang.tra('warn_submit_student_assessment_score_entry')
      }

      // switch(this.activeSession.slug){
      //   case ASSESSMENT.PRIMARY_SAMPLE:
      //   case ASSESSMENT.JUNIOR_SAMPLE:
      //     caption =  this.lang.tra('pj_sample_submit_results_caption')
      //   break;
      //   case "G9_OPERATIONAL":
      //     caption =  this.lang.tra('warn_submit_student_assessment3')
      //   break;
      //   case "G9_G9_SAMPLE":
      //     caption =  this.lang.tra('warn_submit_student_assessment3')
      //   break;
      //   case "OSSLT_OPERATIONAL":
      //     caption =  this.lang.tra('warn_submit_student_assessment2')
      //   break;
      //   case "OSSLT_SAMPLE":
      //     caption =  this.lang.tra('warn_submit_student_assessment2')
      //   break;
      //   default:
      //     caption =  this.lang.tra('warn_submit_student_assessment2')
      //   break;   
      // }

      this.loginGuard.confirmationReqActivate({
        caption,
        subCaption,
        requireTextInput: ['yes', 'oui'],
        confirm: () => {
          this.closeAllSubSessions = true;
          const groupIdQuery = this.classroomService.constructClassGroupIdQuery(this.classroomId)
          let params = {
            query: {
              school_class_group_id: groupIdQuery.query.school_class_group_id,
              is_session_completed: false,
              is_score_entry: this.isScoreEntrySession,
              sc_id: +this.classroomId,
            }
          }
          this.classroomService
            .closeAssessmentSession(this.classroomId, this.session_id, params)
            .then(() => {
              this.navigateBack();
              this.classroomService.reloadSchool();
            });
          // this.classroomService
          //   .closeAssessmentSession(this.classroomId, this.session_id, params)
          //   .then(() => {
          //     this.activeClassroom.recentAssessments.push(session);
          //     this.classroomService.navigateToClassroom(this.classroomId);
          //   })
        }
      })
    }

    navigateBack(){
      if(this.isABED()){
        const isFromSchoolAdmin = this.g9demoService.getIsFromSchoolAdmin();
        if(isFromSchoolAdmin){
          let queryParams = this.route.snapshot.queryParams;
          const viewRout = `/${this.lang.c()}/${AccountType.SCHOOL_ADMIN}/${SchoolAdminView.SESSIONS}`
          this.router.navigate([viewRout], {
            relativeTo: this.route,
            queryParams
          });
        }else{
          this.classroomService.navigateToClassroom(this.classroomId);
        }
      }else{
        this.classroomService.navigateToClassroom(this.classroomId);
      }
    }

    toggleUnsubmitBtn() {
      // #MERGE_20220524 : refactor
      if(!this.isUnsubmitBtnVisiable && this.isPrimaryOrJunior()) {
        this.loginGuard.confirmationReqActivate({
          caption: 'pj_unsubmit_btn_caption',
          btnCancelConfig: {
            hide: true
          }
        })
      }
    }
    
    getUnsubmitBtnText(): string {
      if(this.isUnsubmitBtnVisiable) {
        // todo:DB_DATA_MODEL
        if(this.isPrimaryOrJunior()) {
          return this.lang.tra('pj_lbl_hide_unsubmit_button');
        } 
        else if (this.isG9OrOsslt()) {
          return this.lang.tra('lbl_hide_unsubmit_button');
        } 
        else if(this.isABED()) {
          return this.lang.tra('lbl_hide_unsubmit_button_abed');
        }
      } else {
        // todo:DB_DATA_MODEL
        if(this.isPrimaryOrJunior()) {
          return this.lang.tra('pj_lbl_show_unsubmit_button');
        } 
        else if (this.isG9OrOsslt()) {
          return this.lang.tra('lbl_show_unsubmit_button');
        } 
        else if(this.isABED()) {
          return this.lang.tra('lbl_show_unsubmit_button_abed');
        }
      }
    }

    getLabelShowUnsubmitBtn () {
      if(this.isPrimaryOrJunior()){
        return 'pj_lbl_show_unsubmit_button'
      }
      return 'lbl_show_unsubmit_button'
    }

    getLabelHideUnsubmitBtn () {
      if(this.isPrimaryOrJunior()){
        return 'pj_lbl_hide_unsubmit_button'
      }
      return 'lbl_hide_unsubmit_button'
    }

    introVideoModalStart() {
      this.pageModal.newModal({
        type: TeacherModal.INVIG_VIDEO_INTRO_SEEN,
        config: {},
        confirmationCaption: this.lang.tra("abed_dismiss"), 
        isProceedOnly: true,
        finish: () => this.dismissIntroVideo()
      })
    }

    async checkIfIntroVidDismissed(){
      const user_status = await this.g9demoService.checkInvigIntroStatus();
      //disabling the video popup as per request.
      if(!user_status.hasTheTeacherDismissed && false){
        this.introVideoModalStart();
      }
    }

    dismissIntroVideo(){
      this.g9demoService.toggleInvigIntroStatus().catch(e =>{
        //console.log(e);
      });
    }

    reviewSubmissionsModalStart() {
      const config: IReviewModalConfig = { subSessions: this.subSessions, studentStates: this.studentStates, studentScanInfo: this.scanInfo.studentScanInfoMap, asmtSlug: this.asmtSlug};
      this.pageModal.newModal({
        type: TeacherModal.REVIEW_SUBM_MODAL,
        config,
        finish: this.startPjSubmissionModal.bind(this)
      })
    }

    startPjSubmissionModal() {    // for PJ Operational Test
      this.pageModal.closeModal();
      if(this.isFIOptionC){
        this.submitAssessmentSessions();
      } else {
        this.submitPJAssessmentSessions();
      }
    }

    submitPJAssessmentSessions() {
      const caption = this.lang.tra('pj_submit_results_caption');
      const subCaption = this.lang.tra('pj_submit_results_subcaption_1');
      const checkboxCaption = this.lang.tra('pj_submit_results_subcaption_2');
      this.loginGuard.confirmationReqActivate({
        caption,
        subCaption: subCaption,
        requireCheckboxInput: {
          checkboxCaption: checkboxCaption
        },
        requireTextInput: ['yes', 'oui'],
        confirm: () => {
          this.closeAllSubSessions = true;
          const groupIdQuery = this.classroomService.constructClassGroupIdQuery(this.classroomId)
          let params = {
            query: {
              school_class_group_id: groupIdQuery.query.school_class_group_id,
              is_session_completed: false
            }
          }
          this.classroomService
            .closeAssessmentSession(this.classroomId, this.session_id, params)
            .then(() => {
              this.navigateBack();
            });
        }
      })
    }

    finalizeSessionSubmission($event){
      const sessionToClose = this.activeSession;
      const session = this.addRecentSession(sessionToClose);
      const groupIdQuery = this.classroomService.constructClassGroupIdQuery(this.classroomId)
      let params = {
        query: {
          school_class_group_id: groupIdQuery.query.school_class_group_id,
          is_session_completed: true
        }
      }
      this.classroomService
      .closeAssessmentSession(this.classroomId, this.session_id, params)
      .then(() => {
        this.activeClassroom.recentAssessments.unshift(session);
        this.navigateBack();
      })
      this.closeAllSubSessions = false;
    }

    removeAssessment(assessments, sessionId) {
      for (let i = 0; i < assessments.length; i++) {
        const session = assessments[i];
        if (session.test_session_id == sessionId) {
          assessments.splice(i, 1);
        }
      }
    }
    getScheduledAssessmentById(sessionId) {
      return this.activeClassroom.scheduledAssessments.find(session => session.test_session_id === sessionId)
    }
    getAssessmentById(sessionId) {
      return this.activeClassroom.openAssessments.find(session => session.test_session_id === sessionId)
    }

    changeScheduledSessionStart() {
      this.pageModal.newModal({
        type: TeacherModal.CHANGE_SESSION_DATE,
        config: {
          date_time_start: moment.tz(this.activeSession.date_time_start, moment.tz.guess()).format(), //for date
          date_time_start_moment: moment.tz(this.activeSession.date_time_start, moment.tz.guess()), //for time
          is_schedule_range: this.is_schedule_range,
          date_exceptions: this.date_exceptions,
          scheduledRangeStarted: this.scheduledRangeStarted,
          scheduledRangeEnded: this.scheduledRangeEnded
        },
        isProceedOnly: false,
        isExplicitClose: true,
        finish: (config) => this.changeScheduledSessionSubmit(config)
      });
    }
    async changeScheduledSessionSubmit(config) {
      this.pageModal.isSaving = true;
      let new_date_time_start
      if(config.is_schedule_range){
        new_date_time_start = config.new_date_time_start
      }
      else{
        new_date_time_start = config.selected_new_date_time_start
      }
      if(new_date_time_start){
      const groupIdQuery = this.classroomService.constructClassGroupIdQuery(this.classroomId)
      let params = {
        query: {
          school_class_group_id: groupIdQuery.query.school_class_group_id
        }
      }
      try {
        await this.auth.apiPatch('public/educator/session', this.session_id, {
          date_time_start: new_date_time_start
        }, params)
        const updated_date_time_start = new Date(new_date_time_start).toISOString()
        this.activeSession.date_time_start = updated_date_time_start;
        this.activeSession.dateTimeStartLong = this.formatDateTime(updated_date_time_start, this.activeSession.slug);
        this.activeSession.hourDirectStart = this.formatHourDirectStartTime(updated_date_time_start, this.activeSession.slug)
        this.loginGuard.quickPopup('Schedule date changed successfully.')
        this.pageModal.closeModal();
      }
      catch(e) {
        console.log(e.message)
        if(e.message == "SCHEDULED_TIME_OUT_OF_TW_RANGE"){
          this.loginGuard.quickPopup('Scheduled date not allowed.')
        }
        else{
          this.loginGuard.quickPopup('Date change not permitted.')
        }
      }}
      this.pageModal.isSaving = false;
      this.pageModal.closeModal();
    }

    cancelScheduledSession() {
      const groupIdQuery = this.classroomService.constructClassGroupIdQuery(this.classroomId)
      let params = {
        query: {
          school_class_group_id: groupIdQuery.query.school_class_group_id,
          is_session_completed: false
        }
      }
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('alert_cancel_session'),
        confirm: () => {
          this.classroomService
            .closeAssessmentSession(this.classroomId, this.session_id, params)
            .then(() => {
              this.navigateBack();
            })
        }
      })
    }

    clearStudentEntry() {
      this.studentEntry.oen = '';
      this.studentEntry.firstname = '';
      this.studentEntry.lastname = '';
      this.studentEntry.accommodation = '';
    }
    onStudentSelectionUpdate(studentSelections: ListSelector) {
      // this.isCloseSessionEnabled = this.isOpenSessionEnabled = studentSelections.isAnySelected;
    }

    cModal() { return this.pageModal.getCurrentModal(); }
    cmc() { return this.cModal().config; }

    //updated this function to support both walkinStudents and studentList lists.
    sampleModalStart(studentIdx: number, walkins: boolean = false) {
      let studentList;
      if(walkins){
        studentList = JSON.parse(JSON.stringify(this.walkinStudents));
      }else{
        studentList = JSON.parse(JSON.stringify(this.studentList));
      }
      const config: IStudentInfoConfig = { studentList: studentList, curricShort: this.activeClassroom.curricShort, studentIdx, asmtSlug: this.asmtSlug, questionId: this.questionId};
      const isProceedOnly = this.showScanModal();
      if(this.isABED()){
        this.currentStudentDetail = studentList[studentIdx];
        this.loadStudentAccommodations(this.currentStudentDetail.uid);
      }
      this.pageModal.newModal({
        type: TeacherModal.SAMPLE_MODAL,
        config,
        isProceedOnly,
        finish: () => this.sampleModalFinish(studentList, walkins)
      });
      
    }
    onQuestionIdChange(questionId: number) {
      this.questionId = questionId;
    }

    resetAccommodations = () =>{
      this.currentStudentDetail = null;
      this.accommodationList = [];
    }

    async loadStudentAccommodations(uid?){
      this.accommodationList = await this.g9demoService.loadStudentAccommodations(this.activeClassroom.curricShort, uid);
    }

    showScanModal() {
      return !!this.isScanSession
    }

    getBulkUploadTime() {
      return this.scanInfo.timePerUploadCheck;
    }

    getScanInfoMap() {
      return this.scanInfo.studentScanInfoMap;
    }
    

    sampleModalFinish = (editedList, walkins: boolean = false) => {
      if(this.isABED()){
        const isValidAccomm = this.g9demoService.validationAccomm(this.accommodationList);
        const isValidstudent = this.g9demoService.validateStudentInfo(this.currentStudentDetail);
        if(isValidAccomm && isValidstudent){
          if(walkins){
            this.walkinStudents = editedList;
          }else{
            this.studentList = editedList;
          }
          this.g9demoService.updateStudentAccommodations(this.currentStudentDetail.uid, this.accommodationList);
          //when pasi updates are on, we block student edits, we should block the api call as well
          if(!this.allowPASIUpdates){
            const classrooom = this.g9demoService.getClassroomById(this.classroomId);
            this.classroomService.updateStudentInfo(this.currentStudentDetail, classrooom.group_id);
          }
          this.pageModal.closeModal();
          this.resetAccommodations();
        }
        else if(!isValidAccomm){
          this.loginGuard.quickPopup('abed_invalid_accommodations');
        }
        else if(!isValidstudent){
          this.loginGuard.quickPopup('bced_invalid_information');
        }
      }else{
        this.pageModal.closeModal();
      }
    }

    // editModalStart(subSession) {
    //   const config = {};
    //   this.pageModal.newModal({
    //     type: TeacherModal.EDIT_MODAL,
    //     config,
    //     finish: config => this.editModalFinish(config,subSession)
    //   });
    // }
    // editModalFinish = (config,subSession) => {
    //   this.finalizeSubSessionEdit(config.payload,subSession)
    //   .then(res =>{ 
    //     this.loadSessionInfo()
    //     this.pageModal.closeModal();
    //   })
     
    // }
    canEditDate(){
      if (this.whitelabelService.getSiteFlag('DISABLE_TS_DATE_MOD')){
        return false;
      }
      else {
        // todo:DB_DATA_MODEL
        if(this.isPJSampleTest()) {
          if(this.subSessions && (this.subSessions[0] && this.subSessions[2])) {
            if(this.subSessions[0].date_time_start && this.subSessions[2].date_time_start) {
              if(!this.verifyPastDate(this.subSessions[0].date_time_start) || !this.verifyPastDate(this.subSessions[2].date_time_start)){
                return true;
              }
            }
            if(!this.subSessions[0].date_time_start && this.subSessions[2].date_time_start && !this.isLanguageVisibleForFIClass() && this.verifyPastDate(this.subSessions[2].date_time_start)) {
              return false;
            }
            if(!this.subSessions[0].date_time_start || !this.subSessions[2].date_time_start){
              return true;
            }
          } 
        }
        // todo:DB_DATA_MODEL
        if(this.isPJOperationalTest()) {
          if(this.subSessions && (this.subSessions[0] && this.subSessions[4])) {
            if(this.subSessions[0].date_time_start && this.subSessions[4].date_time_start) {
              if(!this.verifyPastDate(this.subSessions[0].date_time_start) || !this.verifyPastDate(this.subSessions[4].date_time_start)){
                return true;
              }
            }
            if(!this.subSessions[0].date_time_start && this.subSessions[4].date_time_start && !this.isLanguageVisibleForFIClass() && this.verifyPastDate(this.subSessions[4].date_time_start)) {
              return false;
            }
            if(!this.subSessions[0].date_time_start || !this.subSessions[4].date_time_start){
              return true;
            }
          }        
        }
        // todo:DB_DATA_MODEL
        if(this.isG9OrOsslt()){
          if(this.subSessions && this.subSessions[1]){
            if(this.subSessions && (!this.verifyPastDate(this.subSessions[0].date_time_start) || !this.verifyPastDate(this.subSessions[1].date_time_start))){
              return true;
            }
          }
          if(this.subSessions && !this.verifyPastDate(this.subSessions[0].date_time_start)){
            return true;
          }
        }
        if(this.isABED()){
          if(this.activeSession && !this.verifyPastDate(this.activeSession.date_time_start)){
            return true;
          }
        }
        return false;
      }
    }

    customReportHeader:string
    reportModalStart(customCaption?:string, isSubmitAfterReport?:boolean){
      this.customReportHeader = customCaption;
      const groupIdQuery = this.classroomService.constructClassGroupIdQuery(this.classroomId)
      let params = {
        query: {
          school_class_group_id: groupIdQuery.query.school_class_group_id,
        }
      }
      this.auth
      .apiFind(this.routes.EDUCATOR_REPORT_ISSUES_CATEGORIES, params)
      .then(result => {
        if(result[0].reportIssuesCategories) {
          this.reportIssuesCategories = result[0].reportIssuesCategories;
          const config = {customCaption};
          this.pageModal.newModal({
            type: TeacherModal.REPORT_MODAL,
            config,
            finish: config => this.reportModalFinish(config, isSubmitAfterReport)
          });
        }
      })
    }

    reportModalFinish = (config:any, isSubmitAfterReport?:boolean) =>{
      if(config.msg && config.categorySelection && !config.selectedStudents){
        // this block should be fully deprecated
        let msg = config.msg
        let categorySelection = config.categorySelection
        let selectedStudents = []
        this.studentList.forEach(student => {
          selectedStudents.push( student.uid || student.id)
        })
        this.classroomService.teacherReportIssue(this.classroomId, this.session_id, msg, categorySelection, selectedStudents);
      } 
      else if(config.msg && config.categorySelection && config.selectedStudents){
        if (!config.selectedStudents.length){
          // todo:TRANS
          this.loginGuard.quickPopup('Please select the affected student(s).');
          throw new Error();
        }
        let msg = config.msg
        let phone_number = config.phone_number
        let contact_email = config.contact_email
        let categorySelection = config.categorySelection
        let selectedStudents:number[] = []
        config.selectedStudents.forEach(student => {
          selectedStudents.push(student.uid ||student.id)
        })
        this.classroomService.teacherReportIssue(this.classroomId, this.session_id, msg, categorySelection, selectedStudents, {phone_number, contact_email});
      }
      else{
        this.loginGuard.quickPopup(this.lang.tra('alert_ta_report_issue_empty'));
        throw new Error();
      }
      this.pageModal.closeModal();
    }

    editModalStart() {
      const config = {};
      // todo:DB_DATA_MODEL
      if(this.isPrimaryOrJunior()){
        if(this.isPJSampleTest()){
          if(this.subSessions[0] && this.subSessions[2]){ 
            if(this.subSessions[0].date_time_start && this.subSessions[2].date_time_start){
              this.showSessionLangEdit = this.verifyPastDate(this.subSessions[0].date_time_start)? false: true;
              this.showSessionMathEdit = this.verifyPastDate(this.subSessions[2].date_time_start)? false: true;
            } 
            if(this.subSessions[0].date_time_start && !this.subSessions[2].date_time_start){
              this.showSessionLangEdit = this.verifyPastDate(this.subSessions[0].date_time_start)? false: true;
              this.showSessionMathEdit = true;
            } 
            if(!this.subSessions[0].date_time_start && this.subSessions[2].date_time_start){
              this.showSessionLangEdit = this.isLanguageVisibleForFIClass() ? true : false;
              this.showSessionMathEdit = this.verifyPastDate(this.subSessions[2].date_time_start)? false: true;
            }
          }
        }
        if(this.isPJOperationalTest()){
          if(this.subSessions[0] && this.subSessions[4]){ 
            if(this.subSessions[0].date_time_start && this.subSessions[4].date_time_start){
              this.showSessionLangEdit = this.verifyPastDate(this.subSessions[0].date_time_start)? false: true;
              this.showSessionMathEdit = this.verifyPastDate(this.subSessions[4].date_time_start)? false: true;
            } 
            if(this.subSessions[0].date_time_start && !this.subSessions[4].date_time_start){
              this.showSessionLangEdit = this.verifyPastDate(this.subSessions[0].date_time_start)? false: true;
              this.showSessionMathEdit = true;
            } 
            if(!this.subSessions[0].date_time_start && this.subSessions[4].date_time_start){
              this.showSessionLangEdit = this.isLanguageVisibleForFIClass() ? true : false;
              this.showSessionMathEdit = this.verifyPastDate(this.subSessions[4].date_time_start)? false: true;
            }
          }
        }
      }

      // todo:DB_DATA_MODEL
      if(this.isG9OrOsslt()){
        if(this.subSessions[0]){
          this.showSessionAEdit = this.verifyPastDate(this.subSessions[0].date_time_start)? false: true
        }
        if(this.subSessions[1]){
          this.showSessionBEdit = this.verifyPastDate(this.subSessions[1].date_time_start)? false: true
          this.isShowSessionB = true;
        }
      }

      if(this.isABED()){
        if(this.activeSession){
          this.showSessionAEdit = this.verifyPastDate(this.activeSession.date_time_start)? false: true
        }
      }

      this.pageModal.newModal({
        type: TeacherModal.EDIT_MODAL,
        config,
        finish: config => this.editModalFinish(config)
      });
    }

    editModalFinish = async(config) => {
      this.togglethisIsSessionInitializing(true);
      const dateTime = [];
      // todo:DB_DATA_MODEL
      let pjSessionStartDate;
      let pjSessionEndDate;
      let pjCaption;
      if(this.isPrimaryOrJunior()){
        if(config.payload.date_lang && config.payload.date_math){
          pjSessionStartDate = config.payload.date_lang[0] <= config.payload.date_math[0] ? config.payload.date_lang[0] : config.payload.date_math[0];
          pjSessionEndDate = config.payload.date_lang[1] >= config.payload.date_math[1] ? config.payload.date_lang[1] : config.payload.date_math[1];
          if(this.isPJSampleTest()){
            pjCaption = config.payload.date_lang[0] <= config.payload.date_math[0] ? this.subSessions[0].caption : this.subSessions[2].caption;
          }
          if(this.isPJOperationalTest()){
            pjCaption = config.payload.date_lang[0] <= config.payload.date_math[0] ? this.subSessions[0].caption : this.subSessions[4].caption;
          }
        }
        else if(config.payload.date_lang && !config.payload.date_math){
          // todo:DB_DATA_MODEL
          let startDate = this.isPJOperationalTest() ? this.subSessions[4].date_time_start : this.subSessions[2].date_time_start; 
          let endDate = this.isPJOperationalTest() ? this.subSessions[4].date_time_end : this.subSessions[2].date_time_end; 
          if(startDate){
            pjSessionStartDate = config.payload.date_lang[0] <= startDate ? config.payload.date_lang[0] : startDate;
            pjSessionEndDate = config.payload.date_lang[1] <= endDate ? endDate : config.payload.date_lang[1];
            if(this.isPJSampleTest()){
              pjCaption = config.payload.date_lang[0] <= startDate ? this.subSessions[0].caption : this.subSessions[2].caption;
            }
            if(this.isPJOperationalTest()){
              pjCaption = config.payload.date_lang[0] <= startDate ? this.subSessions[0].caption : this.subSessions[4].caption;
            }
          } else {   
            pjSessionStartDate = config.payload.date_lang[0];
            pjSessionEndDate = config.payload.date_lang[1];
            pjCaption = this.subSessions[0].caption;
          }
        }
        else if(config.payload.date_math && !config.payload.date_lang){
          // todo:DB_DATA_MODEL
          let startDate = this.subSessions[0].date_time_start; 
          let endDate = this.subSessions[0].date_time_end; 
          if(startDate){
            pjSessionStartDate = config.payload.date_math[0] <= startDate ? config.payload.date_math[0] : startDate;
            pjSessionEndDate = config.payload.date_math[1] <= endDate ? endDate : config.payload.date_math[1];
            if(this.isPJSampleTest()){
              pjCaption = config.payload.date_math[0] <= startDate ? this.subSessions[2].caption : this.subSessions[0].caption;
            }
            if(this.isPJOperationalTest()){
              pjCaption = config.payload.date_math[0] <= startDate ? this.subSessions[4].caption : this.subSessions[0].caption;
            } 
          } else {  
            pjSessionStartDate = config.payload.date_math[0];
            pjSessionEndDate = config.payload.date_math[1];
            if(this.isPJSampleTest()){
              pjCaption = this.subSessions[2].caption;
            }
            if(this.isPJOperationalTest()){
              pjCaption = this.subSessions[4].caption;
            } 
          }
        }
      }
      if(this.isPJSampleTest() && this.subSessions) { 
        // todo:DB_DATA_MODEL
        for(let i = 0; i < this.subSessions.length; i++){
          dateTime[i] = {
            date_time_start: config.payload.date_lang ? config.payload.date_lang[0] : this.subSessions[i].date_time_start,
            date_time_end: config.payload.date_lang ? config.payload.date_lang[1]: this.subSessions[i].date_time_end,
            id: this.subSessions[i].id,
            caption: this.subSessions[i].caption,
            asmtSlug: this.asmtSlug,
          }
          if(i == 2 || i == 3) {
            dateTime[i] = {
              date_time_start: config.payload.date_math ? config.payload.date_math[0] : this.subSessions[i].date_time_start,
              date_time_end: config.payload.date_math ? config.payload.date_math[1]: this.subSessions[i].date_time_end,
              id: this.subSessions[i].id,
              caption: this.subSessions[i].caption,
              asmtSlug: this.asmtSlug,
            }
          }
        }
      }
      if(this.isPJOperationalTest() && this.subSessions) {
        // todo:DB_DATA_MODEL
        for(let i = 0; i < this.subSessions.length; i++){
          dateTime[i] = {
            date_time_start: config.payload.date_lang ? config.payload.date_lang[0] : this.subSessions[i].date_time_start,
            date_time_end: config.payload.date_lang ? config.payload.date_lang[1]: this.subSessions[i].date_time_end,
            id: this.subSessions[i].id,
            caption: this.subSessions[i].caption,
            asmtSlug: this.asmtSlug,
          }
          if(i == 4 || i == 5 || i == 6 || i == 7) {
            dateTime[i] = {
              date_time_start: config.payload.date_math ? config.payload.date_math[0] : this.subSessions[i].date_time_start,
              date_time_end: config.payload.date_math ? config.payload.date_math[1]: this.subSessions[i].date_time_end,
              id: this.subSessions[i].id,
              caption: this.subSessions[i].caption,
              asmtSlug: this.asmtSlug,
            }
          }
        }
      }
      if(this.isG9OrOsslt()) {
        // todo:DB_DATA_MODEL
        if(this.subSessions[0]){
          dateTime[0] = {
            date_time_start: config.payload.date_time_start_a, 
            id: this.subSessions[0].id,
            caption: this.subSessions[0].caption,
            asmtSlug: this.asmtSlug,
          }
        }
        if(this.subSessions[1]){
          dateTime[1] = {
            date_time_start: config.payload.date_time_start_b, 
            id: this.subSessions[1].id,
            caption: this.subSessions[1].caption,
            asmtSlug: this.asmtSlug,
          }
        }
      }
      if(this.isABED()){
        if(this.activeSession){
          const newDateTime = config.payload?.date_time_start_a;
          if(!newDateTime){
            alert(this.lang.tra("abed_teach_datetime_empty"));
            throw new Error();
          }
          dateTime[0] = {
            date_time_start: newDateTime, 
            id: this.session_id,
            caption: this.activeSession.caption,
            asmtSlug: this.asmtSlug,
          }
        }
      }
      const schoolClassId = this.route.snapshot.paramMap.get('classroomId');
      const schl_class_group_id = this.classroomService.getClassroomById(schoolClassId).group_id;
      const twDates = await this.auth.apiGet(this.routes.EDUCATOR_SCHOOL_SEMESTER_TW_DATES, schoolClassId, {
        query: {
          schl_class_group_id
        }
      })
      const windowStart = moment(twDates.date_start);
      const windowEnd = moment(twDates.date_end);
      let alertSlug
      // todo:DB_DATA_MODEL
      if (this.activeSession.slug === ASSESSMENT.OSSLT_OPERATIONAL){
        alertSlug = 'msg_osslt_administration_window_warning';
        this.validateTestDatesWindow(config.payload.date_time_start_a,config.payload.date_time_start_b, windowStart, windowEnd, alertSlug)
      } 
      else if(this.activeSession.slug === ASSESSMENT.G9_OPERATIONAL){
        alertSlug = 'msg_g9_administration_window_warning';
        this.validateTestDatesWindow(config.payload.date_time_start_a, config.payload.date_time_start_b, windowStart, windowEnd, alertSlug)
      } 
      else if (this.activeSession.slug === ASSESSMENT.PRIMARY_OPERATIONAL){
        alertSlug = 'msg_primary_administration_window_warning';
        this.validateTestDatesWindow(pjSessionStartDate, pjSessionEndDate, windowStart, windowEnd, alertSlug)
      } 
      else if (this.activeSession.slug === ASSESSMENT.JUNIOR_OPERATIONAL){
        alertSlug = 'msg_junior_administration_window_warning';
        this.validateTestDatesWindow(pjSessionStartDate, pjSessionEndDate, windowStart, windowEnd, alertSlug)
      }
      else if (this.isABED()){
        alertSlug = 'msg_abed_administration_window_warning_wdates';
        this.validateTestDatesWindow(config.payload.date_time_start_a, null, windowStart, windowEnd, alertSlug)
      } 

      let isAnyFailed = false;
      Promise.all(dateTime.map(date_time => {
        if(this.isPrimaryOrJunior()){
          // todo:DB_DATA_MODEL
          // not used for ABED
          if (date_time.date_time_start && date_time.date_time_end){
            let payload = {
              ...date_time,
              pjSessionStartDate,
              pjSessionEndDate,
              pjCaption,
            }
            this.finalizePJSubSessionEdit(payload)
            .then( res => {
              if(res.sessionSchedule){
                const theSession = this.getTargetSession();
                if(theSession){
                  theSession.date_time_start = res.sessionSchedule.date_time_start
                  const mStart = moment.tz(theSession.date_time_start, moment.tz.guess());
                  let timezone = mStart.zoneAbbr();
                  timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label')
                  theSession.timeDirectStart = mStart.format(this.lang.tra('datefmt_day_month'));
                  theSession.dateTimeStartLong = `${mStart.format(this.lang.tra('datefmt_sentence'))} ${timezone}`;
                  theSession.hourDirectStart = mStart.format(this.lang.tra('timefmt_hour_time'));
                }
              }
              this.loadSessionInfo();
            })
            .catch(e => {
              isAnyFailed = true
            })
          }
        }
        if(this.isG9OrOsslt()){
          // todo:DB_DATA_MODEL
          // not used for ABED
          if (date_time.date_time_start){
            this.finalizeSubSessionEdit(date_time)
            .then( res => {
              if(res.subsessionRecord.slug === 'session_a'){
                const theSession = this.getTargetSession();
                if(theSession){
                  theSession.date_time_start = res.datetime_start
                  const mStart = moment.tz(theSession.date_time_start, moment.tz.guess());
                  let timezone = mStart.zoneAbbr();
                  timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label')
                  theSession.timeDirectStart = mStart.format(this.lang.tra('datefmt_day_month'));
                  theSession.dateTimeStartLong = `${mStart.format(this.lang.tra('datefmt_sentence'))} ${timezone}`;
                  theSession.hourDirectStart = mStart.format(this.lang.tra('timefmt_hour_time'));
                }
              }
              this.loadSessionInfo()
            })
            .catch(e => {
              isAnyFailed = true
            })
          }
        }
        if(this.isABED()){
          if (date_time.date_time_start){
            const params = this.classroomService.constructClassGroupIdQuery(this.classroomId)
            this.auth
              .apiPatch(this.routes.EDUCATOR_SESSION, this.session_id, { date_time_start: date_time.date_time_start }, params)
              .then(() => {
                let localSessionUpdate = this.activeClassroom.scheduledAssessments.find(sa => sa.test_session_id == this.session_id);
                const convertedToDisplay = moment.tz(date_time.date_time_start, this.whitelabelService.getTimeZone());
                localSessionUpdate.date_time_start = convertedToDisplay;
                localSessionUpdate.hourDirectStart = this.renderTime(convertedToDisplay);
                localSessionUpdate.timeDirectStart = this.renderDay(convertedToDisplay);
              })
              .catch(e => {
                isAnyFailed = true
              })
           }
        }
      })
      ).then((results)=> {
        if (isAnyFailed){
          this.loginGuard.quickPopup('Failed to modify start times')
        }
        else {
          this.loginGuard.quickPopup(this.lang.tra('msg_date_time_edit_success'))
        }
        this.pageModal.closeModal();
      });
       // this.finalizeSubSessionEdit(config.payload,subSession)
      // .then(res =>{ 
      //   this.loadSessionInfo()
      //   this.pageModal.closeModal();
      // })
    }

    private getTargetSession = () => {
      const targetSessionId = +this.session_id;
      const targetClassroom = this.g9demoService.teacherClassrooms.map[this.classroomId]
      const openedSession = targetClassroom.openAssessments.find(session => session.test_session_id === targetSessionId);
      const scheduledSession = targetClassroom.scheduledAssessments.find(session => session.test_session_id === targetSessionId);
      return openedSession || scheduledSession;
    }

    private togglethisIsSessionInitializing(state: boolean): void {
      if(!this.isPrimaryOrJunior()){
        return;
      }
      this.isRefreshingSessionsInfo = state;
    }

    validateTestDatesWindow(aStart, bStart, windowStart, windowEnd, alertSlug){
      const a = aStart ? moment(aStart) : undefined;
      const b = bStart ? moment(bStart) : undefined;
      const alertProps = 
        {
          start: this.isPJOperationalTest()? mtz(windowStart).format(this.lang.tra('datefmt_dashboard_long_pj')): mtz(windowStart).format(this.lang.tra('datefmt_dashboard_long')),
          end: this.isPJOperationalTest()? mtz(windowEnd).format(this.lang.tra('datefmt_dashboard_long_pj')):mtz(windowEnd).format(this.lang.tra('datefmt_dashboard_long')),
        }

      // todo:DB_DATA_MODEL multi-session structure should be defined in the database
      if(!a && b){
        if (b.isBefore(windowStart)){
          alert(this.lang.tra(alertSlug, null, alertProps))
          throw new Error();
        }
        if (b.isAfter(windowEnd)){
          alert(this.lang.tra(alertSlug, null, alertProps))
          throw new Error();
        }
      }
      else if(!b && a){
        if (a.isBefore(windowStart)){
          alert(this.lang.tra(alertSlug, null, alertProps))
          throw new Error();
        }
        if (a.isAfter(windowEnd)){
          alert(this.lang.tra(alertSlug, null, alertProps))
          throw new Error();
        }
      }
      else if(a && b){
        if (a.isBefore(windowStart) || b.isBefore(windowStart)){
          alert(this.lang.tra(alertSlug, null, alertProps))
          throw new Error();
        }
        else if (a.isAfter(windowEnd) || b.isAfter(windowEnd)){
          alert(this.lang.tra(alertSlug, null, alertProps))
          throw new Error();
        }
      }
    }
    
    finalizeSubSessionEdit(payload){
      const refinedPayload = payload;
      refinedPayload['test_session_id'] = this.session_id; 
      refinedPayload['session_rescheduled'] = false; 
      if(payload.caption === 'A'){
        refinedPayload['session_rescheduled'] = true; 
      }
      else{
        if(this.verifyPastDate(this.subSessions[0].date_time_start)){
          refinedPayload['session_rescheduled'] = true; 
        }
      }
      const subSessionId = payload.id;
      const params = this.classroomService.constructClassGroupIdQuery(this.classroomId)
      return this.classroomService.updateSubSessionTime(subSessionId,refinedPayload,params)
    }

    finalizePJSubSessionEdit(payload){
      const refinedPayload = payload;
      refinedPayload['test_session_id'] = this.session_id; 
      refinedPayload['session_rescheduled'] = false; 
      if(payload.caption === refinedPayload.pjCaption){
        refinedPayload['session_rescheduled'] = true; 
      }
      const subSessionId = payload.id;
      const params = this.classroomService.constructClassGroupIdQuery(this.classroomId);
      return this.classroomService.updateSubSessionTime(subSessionId, refinedPayload, params);
    }

    verifyPastDate(dateStr) {
      let currDate = new Date();
      let selectedDate = new Date(dateStr)
      if (currDate.getTime() > selectedDate.getTime()) {
        return true;
      }
      return false;
    }
    
    processWalkinList(walkin_students:{students:any[], student_metas:any[], tw_student_metas:any[]}){
      let processedWalkinList = [];
      const students = walkin_students.students || [];
      const student_metas = walkin_students.student_metas || [];
      const tw_student_metas = walkin_students.tw_student_metas || [];
      for(let ws of students){
        const student = ws;
        for(let meta of student_metas){
          if(meta.uid == student.uid){
            student[meta.key_namespace] = student[meta.key_namespace] || {}
            student[meta.key_namespace][meta.key] = meta.value;
          }
        }
        for(let tw_meta of tw_student_metas){
          if(tw_meta.uid == student.uid){
            student[tw_meta.key_namespace] = student[tw_meta.key_namespace] || {};
            if(tw_meta.meta != null) {
              student[tw_meta.key_namespace][tw_meta.key] = tw_meta.meta;
            }else{
              student[tw_meta.key_namespace][tw_meta.key] = tw_meta.value;
            }
          }
        }
        const studentNormalized = this.g9demoService.apiToClientPayloadStudent(student);
        studentNormalized.diplomaExamInfo = student.diplomaExamInfo || [];
        processedWalkinList.push(studentNormalized);
      }
      this.walkinStudents = processedWalkinList;
    }

    isDurationEnforced:boolean;
    duration_m:number;
    time_ext_m:number;
    // is_soft_lock_enabled:number;
    is_soft_lock_disabled:number;
    date_exceptions: string[];
    is_schedule_range: number;
    scheduledRangeStarted: string;
    scheduledRangeEnded: string;

    async handleSessionInfoRes(res) {
      this.getSessionStart(res);
      this.processWalkinList(res.walkin_students);

      if (res.is_score_entry) {
        this.isScoreEntrySession = true;
        await this.fetchTestDesignForScoreEntry()
      }
      this.isSessionPaused = res.is_paused
      this.isDurationEnforced = res.isDurationEnforced;
      this.duration_m = res.duration_m;
      this.time_ext_m = res.time_ext_m;
      // this.is_soft_lock_enabled = res.is_soft_lock_enabled;
      this.is_soft_lock_disabled = res.is_soft_lock_disabled;
      this.studentStates = res.subSessions.studentStates;
      this.subSessions = res.subSessions.subSessionRecords;
      this.completedSubSessions = res.completedSubSessions;
      this.activeSubSessions = res.activeSubSessions;
      // this.handleStudentSessionMeta(res.tw_session_student_meta)
      this.isNoTdOrder = res.is_no_td_order;
      this.isLoaded = true;
      this.isSessionInitializing = false;
      this.asmtSlug = res.asmt_slug;
      this.isScanSession = res.is_scan_session
      this.questionsForScan = res.questionsForScan
      this.resp_sheet_config = res.tw_resp_sheet_config
      this.isSingleUploadAllowed = this.g9demoService.getIsFromSchoolAdmin() || res.tw_is_allow_teacher_single_scan_upload
      this.isBulkUploadAllowed = this.g9demoService.getIsFromSchoolAdmin() || res.tw_is_allow_teacher_bulk_scan_upload
      this.processPossibleStudentListChange();
      this.updateTestAttemptSoftLockStatus(res.test_attempt_test_status)
      // console.log("soft locks", this.testAttemptSoftLockStatus)

      // if(this.showScanInfo()) {
      //   await this.scanInfo.loadScanInfo(+this.activeClassroom.id);
      // }
      this.is_schedule_range = res.is_schedule_range;
      this.date_exceptions = res.date_exceptions.map(date_exception => date_exception.date_start_override)
      let min_dates_allowed: number[] = [];
      if(this.date_exceptions){
        for (const date_exception of this.date_exceptions){
          min_dates_allowed.push(new Date(date_exception).getTime())
        }
      }
      if(res.twtar_test_date_start){
        this.date_exceptions.push(res.twtar_test_date_start)
        min_dates_allowed.push(new Date(res.twtar_test_date_start).getTime())
      }
      else{
        min_dates_allowed.push(new Date(res.tw_date_start).getTime())
      }
      this.date_exceptions.sort();
      this.scheduledRangeStarted = new Date(Math.min.apply(null,min_dates_allowed)).toISOString();
      if(res.twtar_test_date_end){
        this.scheduledRangeEnded = new Date(Math.min(
          new Date(res.twtar_test_date_end).getTime(),
          new Date(res.tw_date_end).getTime()
        )).toISOString()
      }
    }

    isSoftLockEnabled(){
      return (this.is_soft_lock_disabled == 0)
    }
    async toggleSoftLockEnabled(){
      let val: any = this.is_soft_lock_disabled
      if (this.isSoftLockEnabled()){
        val = await this.auth.apiRemove('public/educator/soft-lock-session', this.session_id, {});
      }
      else {
        val = await this.auth.apiPatch('public/educator/soft-lock-session', this.session_id, {});
      }
      this.is_soft_lock_disabled = val.is_soft_lock_disabled;
    }

    async disableSoftLock(){
      let val: any = this.is_soft_lock_disabled
      if (this.isSoftLockEnabled()){
        val = await this.auth.apiRemove('public/educator/soft-lock-session', this.session_id, {});
      }
      this.is_soft_lock_disabled = val.is_soft_lock_disabled;
    }

    async notifyUserSoftLock(){
      this.isUserSoftLockNotified = true;
    }

    processPossibleStudentListChange(){
      let newStudentList = [];
      const ssKeys = Object.keys(this.studentStates);
      for(let ss of ssKeys){
        const studentDidNotLeaveClass = this.studentList.find(currentStudent => currentStudent.uid == +ss);
        if(studentDidNotLeaveClass) newStudentList.push(studentDidNotLeaveClass);
      }
      this.g9demoService.getStudentsByClassroomId(this.classroomId).list = newStudentList;
      this.studentList = newStudentList;
    }

    updateTestAttemptSoftLockStatus(test_attempt_test_status: any){
      for(const attempt in test_attempt_test_status){
        const student_attempt = test_attempt_test_status[attempt]
        const uid = student_attempt.uid
        this.testAttemptSoftLockStatus[uid] = {
          test_attempt_id: student_attempt.id,
          is_paused: student_attempt.is_paused,
          is_soft_lock_disabled: student_attempt.is_soft_lock_disabled,
          is_submitted: student_attempt.is_submitted,
          is_closed: student_attempt.is_closed
        }
      }
      
    }

    showScanInfo() {
      // todo:DB_DATA_MODEL
      return this.isScanSession;
    }

    onLockActionsUpdate(event) {
      const {studentLockActionMap, studentUids} = event;
      this.studentLockActionMap = studentLockActionMap;
      this.studentsToTrackLockAction = studentUids;
    }

    reloadSessionInfo(result){
      this.isAllowLoadRetry = false
      //this.activeClassroom = this.g9demoService.teacherClassrooms.map[this.classroomId];
      //const params = this.classroomService.constructClassGroupIdQuery(this.classroomId);
      const classroom = this.classroomService.getClassroomById(this.classroomId)
      const params = {
        query: {
          school_class_group_id: classroom.group_id,
          school_class_id:this.classroomId
        }
      }
      this.auth
        .apiGet(this.routes.EDUCATOR_SESSION, this.session_id, params)
        .then(res => {
          this.g9demoService.setIsUnsubmitting(false);
          this.handleSessionInfoRes(res);
          this.calculateDateEndForPrintAndUpload(res);
          if(result !== null){
            const log_entry_data = JSON.parse(result.data)
            if(log_entry_data.isAllowAutoUnsubmit){
              setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_unsubmit_tass_success')), 0);
            }else{
              const unsubmitPendingMsg = this.isABED() ? 'lbl_unsubmit_tass_pending_abed' : `lbl_unsubmit_tass_pending`
              setTimeout(() => this.loginGuard.quickPopup(this.lang.tra(unsubmitPendingMsg)), 0);
            }
          }  
          this.studentsToTrackLockAction.forEach(uid => {
            this.studentLockActionMap.set(uid, false);
          })
        })
        .catch(e =>{
          this.g9demoService.setIsUnsubmitting(false);
          this.studentsToTrackLockAction.forEach(uid => {
            this.studentLockActionMap.set(uid, false);
          })
          setInterval(()=>{
            this.isAllowLoadRetry = true
          }, 30*1000)
        })
    }

    // Print Student Information Modal
    studentInfoModalStart(){
      const config = { 
        studentList: this.studentList, 
        activeClassroom: this.activeClassroom, 
        isScanSession: this.isScanSession,
      };  
      const isProceedOnly = true;
      this.pageModal.newModal({
        type: PrintScanModal.STUDENT_INFO,  
        config,
        isProceedOnly,
        finish: this.studentInfoModalFinish
      });
    }

    studentInfoModalFinish = () => {
      this.pageModal.closeModal();
    }

    initStudentReviewedMap(map: any) {
      this.studentReviewedStatusMap = map;
    }

    allStudentsReviewed() {
      let allReviewed = true;
      for (let reviewed of this.studentReviewedStatusMap.values()) {
        if (!reviewed) {
          allReviewed = false;
          break;
        }
      }
      return allReviewed;
    }

    checkIfOkDisabled() {
      if (!(this.cModal().type === TeacherModal.REVIEW_SUBM_MODAL)) return false;
      
      // review submission modal
      return !this.allStudentsReviewed();
    }

    // Print/Scan Instruction Modal
    instructionModalStart(){
      const config = {};  
      const isProceedOnly = true;
      this.pageModal.newModal({
        type: PrintScanModal.INSTRUCTION_MODAL,  
        config,
        isProceedOnly,
        finish: this.instructionModalFinish
      });
    }

    instructionModalFinish = () => {
      this.pageModal.closeModal();
    }

    getStudentByUid(uid?: number) {
      const student = this.studentList.filter(st => (st.id === uid || st.uid === uid))
      return student[0]
    }

    isABED () { return this.whitelabelService.getSiteFlag('IS_ABED'); } 
    isNBED() { return this.whitelabelService.getSiteFlag('IS_NBED'); }
    isMBED() { return this.whitelabelService.getSiteFlag('IS_MBED'); }

    getTeacherSubmitResponsesSlug(){
      // todo:DB_DATA_MODEL lower priority
      if(this.isNBED() || this.isMBED()){
        return 'teacher_submit_responses_nbed';
      }
      if (this.isABED()){
        return 'teacher_submit_assessment_session_ABED'
      }
      return 'pj_teacher_submit_responses';
    }

    getStudentPostSlug(){
      // todo:DB_DATA_MODEL lower priority
      if(this.isNBED() || this.isMBED()){
        return 'lbl_student_post_nbed';
      }
      if(this.isABED()){
        return this.whitelabelService.getSiteText("invigilationIntro");
      }
      return 'lbl_student_post';
    }
  
    /**
     * Deterimines if unsubmit button should be displayed
     * @returns {boolean} - True to show button
     */
    displayUnsubmitButton(){
      if (!this.isSessionOpened){
        return false;
      }
      if(!this.whitelabelService.getSiteFlag('ALLOW_TEACHER_UNSUBMIT')) {
        return false;
      }
      if (this.isScoreEntrySession) {
        return false;
      }
      return true
    }

    get isSasnLogin():boolean {
      return this.classroomService.isSASNLogin(+this.classroomId);
    }

    //logic for accepting a walk-in student.
    //we send the user_meta key of 'AcceptedByTeacher' to the walk-in endpoint which will turn the value of the meta to 1
    //then for the local data i remove the student from the walkin list and add them to the regular list. which should match the effect of a page refresh.
    async acceptWalkStudent(uid: number, isBulk = false){
      const classroom = this.classroomService.getClassroomById(this.classroomId)
      if(!classroom) return;
      try{
        await this.auth.apiPatch(this.routes.ABED_WALKN_STUDENT, null, 
          {
            uid: uid,
            key: 'AcceptedByTeacher'
          },{
            query: {schl_group_id: this.g9demoService.getSchoolGroupId(), sch_class_group_id: classroom.group_id}
          });
      }catch(e){
        console.log("Error: ", e);
      }
      this.localWalkinAcceptance(uid);

      if(!isBulk) {
        this.loadSessionInfo();
      }
    }

    localWalkinAcceptance(uid: number){
      let walkinToRegular = this.walkinStudents[0]; // Note: Susceptible for race conditions
      let newWalkinList = []; 
      for(let ws of this.walkinStudents){
        if(ws.uid == uid) walkinToRegular = ws;
        else newWalkinList.push(ws);
      }
      this.walkinStudents = newWalkinList;
      let currentState = this.studentStates[walkinToRegular.uid];
      this.studentStates[walkinToRegular.uid] = {...currentState, isAbsent: false};
      this.studentList.push(walkinToRegular);
    }

    //logic for rejecting a walk-in student.
    //we send the user_meta key of 'RejectedByTeacher' to the walk-in endpoint which will turn the value of the meta to 1
    //then for the local data we turn the student property of 'RejectedByTeacher' to true, so that the 'reject' button turns into 'rejected'
    async rejectWalkStudent(uid: number){
      const classroom = this.classroomService.getClassroomById(this.classroomId)
      if(!classroom) return;
      const rejectedStudent = this.walkinStudents.find(w => w.uid == uid);
      rejectedStudent.RejectedByTeacher = true;
      try{
        await this.auth.apiPatch(this.routes.ABED_WALKN_STUDENT, null, 
          {
            uid: uid,
            key: 'RejectedByTeacher'
          },{
            query: {schl_group_id: this.g9demoService.getSchoolGroupId(), sch_class_group_id: classroom.group_id}
          });
      }catch(e){
        console.log("Error: ", e);
      }
    }

    //for accept all, we use the same logic for an individual acceptance but we run the calls at the same time.
    async acceptAllWalkStudents(){
      for(let walkin of this.walkinStudents){
        // Do them one by one to avoid race conditions
        await this.acceptWalkStudent(walkin.uid, true);
      }
      await this.loadSessionInfo();
    }

    //for reject all, we use the same logic for an individual rejections but we run the calls at the same time.
    async rejectAllWalkStudents(){
      for(let walkin of this.walkinStudents) {
        // Do them one by one to avoid race conditions
        await this.rejectWalkStudent(walkin.uid);
      }
    }
    
    getStartedOnSlug(){
      if(this.whitelabelService.isABED()){
        return "abed_started_on"
      }
      return "g9_started_on"
    }

    getPauseSessionSlug(){
      if(this.whitelabelService.isABED()){
        return "abed_pause_session"
      }
      return "g9_pause_session"
    }

    getResumeSessionSlug(){
      if(this.whitelabelService.isABED()){
        return "abed_resume_session"
      }
      return "resume_session"
    }

    getReportIssueSlug(){
      if(this.whitelabelService.isABED()){
        return "abed_report_issue"
      }
      return "btn_report_issue"
    }

    getAccessCodeSlug(){
      return this.isABED() ? 'abed_accesscose_for_sessions' : 'lbl_for_sessions';
    }
    
    getAllowDisableLockAfterNotification(){
      return this.whitelabelService.getSiteFlag("IS_ALLOW_DISABLE_SOFTLOCK_AFTER_NOTIFICATION")
    }

    getEnablePromptBeforeSoftLockPause(){
      return this.whitelabelService.getSiteFlag("IS_ENABLE_PROMPT_BEFORE_SOFTLOCK_PAUSE")
    }

    getCustomMessageScanSecuritySlug(){
      if(this.g9demoService.getIsFromSchoolAdmin()){
        return 'INVIG_SCAN_SECURITY_MSG_ADMIN'
      } else {
        return 'INVIG_SCAN_SECURITY_MSG_TEACHER'
      }
    }

  /**
   * Generates a fixed-length access code based on the provided input number.
   * - The access code is deterministic for a given input, meaning the same input number will always produce the same code.
   * - It consists of uppercase letters and numbers, excluding look-alike characters such as 'I', 'O', '1', and '0' to avoid confusion.
   * @param inputNumber - The number used as a seed to generate the access code.
   * @returns A string representing the generated access code.
   * 
   */
  public generateAccessCode(inputNumber: number): string {
    // Linear Congruential Generator for pseudo-random number generation
    const m = 4294967296; // 2^32
    const a = 1664525;
    const c = 1013904223;
    let state = inputNumber % m;

    const nextFloat = (): number => {
        state = (a * state + c) % m;
        return state / m;
    };

    // Define character set excluding look-alike characters
    const charset = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Excludes I, O, 0, 1

    // Set code length and hyphen position
    const codeLength = 7;
    const hyphenPosition = 3;

    let code = '';

    for (let i = 0; i < codeLength; i++) {
        if (i === hyphenPosition) {
            code += '-';
        } else {
            const randomIndex = Math.floor(nextFloat() * charset.length);
            code += charset.charAt(randomIndex);
        }
    }

    return code;
  }

  showLDBProctorPassword() {
    const accessCode = this.generateAccessCode(+this.classroomId || 123456)
    const popupText = this.lang.tra('ldb_proctor_password_popup', this.lang.c(), {accessCode})
    this.loginGuard.quickPopup(popupText)
  }

  openLDBConfigModal(){
    this.pageModal.newModal({
      type: LDBModal.LDB_CONFIG_MODAL,  
      config: {
        currentLevel: LDBConfigLevel.CLASS,
        query: {
          school_class_id: +this.classroomId,
          school_id: this.g9demoService.schoolData.id,
          school_board_id: this.g9demoService.schoolData.sd_id,
        }
      },
      finish: () => {}
    });
  }
  
  calculateDateEndForPrintAndUpload(session) {
    if(!this.isScanSession || this.resp_sheet_config === null){
      return;
    }
    const {print_before_days, upload_until_days, exception_dates, is_exclude_weekends} = this.resp_sheet_config;
    const isWeekendsExcluded = !!is_exclude_weekends;
    this.printBeforedateEnd = calculateDateEnd(session.date_time_start,+print_before_days,false, exception_dates, isWeekendsExcluded);
    this.uploadUntildateEnd = calculateDateEnd(session.date_time_start,+upload_until_days,true, exception_dates, isWeekendsExcluded);
    this.scanInfo.uploadUntildateEnd = this.uploadUntildateEnd;
  }
  formatPrintUploadDate(date: Date){
    return mtz(date).format(this.lang.tra('datefmt_dashboard_short'))
  }
  

  getPrintUploadButtonDisplayStatus() {
    let isPrintDisabled = false, isUploadDisabled = false;
    const now = new Date(); // 12 < 14
    if(this.printBeforedateEnd !== null  && now <= this.printBeforedateEnd){
      isPrintDisabled = true;
    }
    if((this.uploadUntildateEnd !== null && now >= this.uploadUntildateEnd ) || !this.isSessionOpened){
      isUploadDisabled = true;
    }
    return {isPrintDisabled, isUploadDisabled}
  }

  getStudentDetailAccess(){
    if (this.isScanSession){
      return this.isSingleUploadAllowed || this.isBulkUploadAllowed
    } else {
      return true;
    }
  }

  async onScoreEntryPatch(uid: any, data: IScoreEntryPatchData, params: IScoreEntryPatchParams) {
    return await this.auth.apiPatch('public/educator/score-entry', uid, data, params);
  }

  async fetchTestDesignForScoreEntry() {
    try {
      const classroom = this.classroomService.getClassroomById(this.classroomId);
      const result = await this.auth.apiGet('public/educator/score-entry', this.session_id, {
        query: {
          schl_class_group_id: classroom.group_id,
        }
      })
      
      this.test_designs = result.testDesigns; // just need to feed 1 here. this is the only thing that needs to be inputted. Specifically studentAttempts and testForm.sections
      this.isScoreEntryLoaded = true;
    } catch (err) {
      console.error(err);
      this.loginGuard.quickPopup(`An error occurred loading the score entry session: ${err.message}`);
    }
  }

  // isBulkUploadAllowed(){
  //   rethir
  // }

  // isSingleUploadAllowed(){

  // }
}
