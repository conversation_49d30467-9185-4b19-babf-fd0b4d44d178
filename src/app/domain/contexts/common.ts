export interface IContextData {
    id: string,
    homepageRoute: string,
    loginRoute?: string,
    brandName: string,
    logo: string,
    footer: IFooterDef[][] | null,
    apiAddress: (hostname:string) => string,
    siteFlags: {[key:string]:boolean},
    siteText: {[key:string]:string},
    siteData?: {[key:string]:any},
    hosts: string[],
    defaultTimezone: string,
    defaultTimezoneAbbr?: string,
    sdRoleTypes?: string[],
}


export type IFooterDef = {
    route?: string, // internal links
    link?: string, // external links
    caption: string, // external links
}

export const API_ADDRESS_LOCAL = 'https://abed-api.vretta.com';
