<div class="dont-print standard-header-option modern-header" *ngIf="!hasSidebar && !isLoginPage">
  <div class="header-main-content">
    <div class="brand-section">
      <a *ngIf="!isLoginHidden" class="brand-link" [routerLink]="getHomeScreenRoute()">
        <i class="fas fa-graduation-cap brand-icon" aria-hidden="true"></i>
        <span class="brand-text">
          <tra [slug]="getBranding()"></tra>
        </span>
      </a>
    </div>

    <div class="header-actions">
      <!-- Language switcher moved here -->
      <div class="language-section" *ngIf="!noLangSwitcher">
        <button
          *ngFor="let lang of langs"
          (click)="setLang(lang.code)"
          class="modern-lang-btn"
          [class.active]="isLangActive(lang.code)">
          <i class="fas fa-globe" aria-hidden="true"></i>
          {{lang.caption}}
        </button>

        <button
          *ngIf="langService.isUsingLiveTranslations()"
          class="modern-live-btn"
          (click)="langService.deactivateLiveTranslations()">
          <i class="fas fa-broadcast-tower" aria-hidden="true"></i>
          LIVE UPDATE
        </button>
      </div>

      <!-- Notifications -->
      <div class="notifications-section" *ngIf="isLoggedIn() && isAuthNotifications">
        <a routerLink="/en/test-auth/notifications" class="notification-link">
          <div class="notification-content">
            <i class="fas fa-bell notification-icon" aria-hidden="true"></i>
            <span class="notification-text">{{getNotificationsText()}}</span>
            <i *ngIf="notifications.hasUnreadPersonal" class="fas fa-circle unread-indicator"></i>
          </div>
        </a>
      </div>

      <!-- User section -->
      <div class="user-section">
        <div *ngIf="!userInfo" class="login-section">
          <a [routerLink]="getLoginRoute()" class="login-link">
            <i class="fas fa-sign-in-alt" aria-hidden="true"></i>
            <tra slug="title_login"></tra>
          </a>
        </div>
        <div *ngIf="userInfo" class="user-profile-card">
          <div class="user-info-section">
            <div class="user-details">
              <div class="user-identity">
                <span *ngIf="newMessage" class="message-action" (click)="onClickNewMessage()">
                  <a [routerLink]="'/en/chat/' + messagePoolId" class="message-link">
                    <i class="fas fa-envelope" aria-hidden="true"></i>
                  </a>
                </span>

                <div [ngSwitch]="!!accountInfo" class="user-name-section">
                  <span *ngSwitchCase="true" class="user-link-wrapper">
                    <a [routerLink]="accountInfo" class="user-link">
                      <i class="fas fa-user-circle user-avatar" aria-hidden="true"></i>
                      <span class="user-name">{{getUserDisplayName()}}</span>
                    </a>
                  </span>
                  <span *ngSwitchCase="false" class="user-display">
                    <i class="fas fa-user-circle user-avatar" aria-hidden="true"></i>
                    <span class="user-name">{{getUserDisplayName()}}</span>
                  </span>
                </div>
              </div>

              <div class="user-meta">
                <div class="timezone-info">
                  <i class="fas fa-clock" aria-hidden="true"></i>
                  <span class="timezone-text">
                    <tra slug="timezone_indicator_title"></tra>{{timeZone}}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="logout-section" (click)="logout()" [attr.aria-label]="'Logout'" role="button" tabindex="0">
            <i class="fas fa-sign-out-alt logout-icon" aria-hidden="true"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modern-breadcrumb-section">
    <div class="breadcrumb-container">
      <div class="breadcrumb-item home-item">
        <a
          *ngIf="!isBreadcrumbMenuEnabled()"
          routerLink="/{{langService.c()}}/login-portal"
          [target]="isForceNewTab ? '_blank' : null"
          class="home-link"
        >
          <i class="fas fa-home" aria-hidden="true"></i>
          <span class="sr-only">Home</span>
        </a>
        <a
          class="menu-toggle"
          (click)="toggleMenu()"
          *ngIf="isBreadcrumbMenuEnabled()"
        >
          <i class="fas fa-bars" aria-hidden="true"></i>
          <span>Menu</span>
        </a>
        <i *ngIf="breadcrumbPath && breadcrumbPath.length" class="fas fa-chevron-right breadcrumb-separator"></i>
      </div>
      <div class="breadcrumb-item" *ngFor="let crumb of breadcrumbPath; let isLast = last">
        <a
          class="breadcrumb-link"
          [routerLink]="crumb.route"
          [queryParams]="crumb.params"
          [target]="isForceNewTab ? '_blank' : null"
        >{{langService.tra(crumb.caption)}}</a>
        <i *ngIf="!isLast" class="fas fa-chevron-right breadcrumb-separator"></i>
      </div>
    </div>
    <div *ngIf="isMenuRevealed" class="modern-menu-dropdown">
      <a class="menu-item" routerLink="/{{langService.c()}}/applicant/landing">
        <i class="fas fa-user-graduate" aria-hidden="true"></i>
        <tra slug="tab_test_app"></tra>
      </a>
      <a class="menu-item" routerLink="/{{langService.c()}}/testadmin/landing">
        <i class="fas fa-cogs" aria-hidden="true"></i>
        <tra slug="tab_test_admin"></tra>
      </a>
    </div>
  </div>
</div>

<div class="dont-print modern-sidebar-header" [class.is-opaque]="isOpaque" *ngIf="hasSidebar">
  <div class="sidebar-breadcrumb">
    <div class="breadcrumb-item">
      <a
        routerLink="/{{langService.c()}}/login-portal"
        role="link"
        [attr.aria-label]="langService.tra('lbl_home')"
        [target]="isForceNewTab ? '_blank' : null"
        tabindex="0"
        class="home-link"
      >
        <i class="fas fa-home"></i>
        <span class="sr-only">Home</span>
      </a>
      <i *ngIf="breadcrumbPath && breadcrumbPath.length" class="fas fa-chevron-right breadcrumb-separator"></i>
    </div>
    <div class="breadcrumb-item" *ngFor="let crumb of breadcrumbPath; let isLast = last">
      <a
        class="breadcrumb-link"
        [routerLink]="crumb.route"
        [queryParams]="crumb.params"
        [target]="isForceNewTab ? '_blank' : null"
      >{{langService.tra(crumb.caption)}}</a>
      <i *ngIf="!isLast" class="fas fa-chevron-right breadcrumb-separator"></i>
    </div>
  </div>
  <div class="sidebar-actions">
    <button *ngIf="!hideSupportBtn" (click)="supportReqActivate()" class="modern-support-btn">
      <i class="fas fa-life-ring" aria-hidden="true"></i>
      <tra whitelabel="tech_support" slug="title_feedback"></tra>
    </button>
    <span *wlCtx="'IS_LANG_HEADER_SWITCH'" class="sidebar-lang-switch">
      <div *ngIf="!isEnglish()" class="lang-option">
        <a (click)="setLang('en')" class="lang-link">
          <i class="fas fa-globe" aria-hidden="true"></i>
          English
        </a>
      </div>
      <div *ngIf="isEnglish()" class="lang-option">
        <a (click)="setLang('fr')" class="lang-link">
          <i class="fas fa-globe" aria-hidden="true"></i>
          Français
        </a>
      </div>
    </span>
  </div>
</div>

<div class="dont-print modern-login-header" [class.is-abed-login]="isABED()" *ngIf="isLoginPage">
  <div class="login-header-content" [class.top-row]="isABED()">
    <div class="login-breadcrumb" [class.is-abed-login]="isABED()">
      <div class="breadcrumb-item">
        <a routerLink="/{{langService.c()}}/login-portal" class="home-link">
          <i class="fas fa-home"></i>
          <span class="sr-only">Home</span>
        </a>
        <i *ngIf="breadcrumbPath && breadcrumbPath.length" class="fas fa-chevron-right breadcrumb-separator"></i>
      </div>
      <div class="breadcrumb-item" [class.is-abed-logn]="isABED()" *ngFor="let crumb of breadcrumbPath; let isLast = last">
        <a class="breadcrumb-link" [class.is-abed-login]="isABED()" [routerLink]="crumb.route" [queryParams]="crumb.params">{{langService.tra(crumb.caption)}}</a>
        <i *ngIf="!isLast" class="fas fa-chevron-right breadcrumb-separator"></i>
      </div>
    </div>
    <div class="login-logo-section">
      <img *ngIf="isEnglish()" class="header-logo" [src]="getWhitelabelUrl('header_logo_en')">
      <img *ngIf="!isEnglish()" class="header-logo" [src]="getWhitelabelUrl('header_logo_fr')">
    </div>
    <ng-container *ngIf="isABED()">
      <div *wlCtx="'IS_LANG_HEADER_SWITCH'" class="login-lang-switch">
        <div *ngIf="!isEnglish()" class="lang-option">
          <a (click)="setLang('en')" class="lang-link">
            <i class="fas fa-globe" aria-hidden="true"></i>
            English
          </a>
        </div>
        <div *ngIf="isEnglish()" class="lang-option">
          <a (click)="setLang('fr')" class="lang-link">
            <i class="fas fa-globe" aria-hidden="true"></i>
            Français
          </a>
        </div>
      </div>
    </ng-container>
  </div>
  <ng-container *ngIf="!isABED()">
    <div *wlCtx="'IS_LANG_HEADER_SWITCH'" class="login-lang-switch-bottom">
      <div *ngIf="!isEnglish()" class="lang-option">
        <a (click)="setLang('en')" class="lang-link">
          <i class="fas fa-globe" aria-hidden="true"></i>
          English
        </a>
      </div>
      <div *ngIf="isEnglish()" class="lang-option">
        <a (click)="setLang('fr')" class="lang-link">
          <i class="fas fa-globe" aria-hidden="true"></i>
          Français
        </a>
      </div>
    </div>
  </ng-container>
  <div class="logo-container" *ngIf="isABED()">
    <img [src]="currentLogo" class="abed-logo">
  </div>
</div>