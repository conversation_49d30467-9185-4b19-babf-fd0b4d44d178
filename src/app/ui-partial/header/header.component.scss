
@import '../../../styles/partials/_margins.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_colors.scss';
@import '../../../styles/page-types/standard.scss';
@import '../../core/main-nav/style/common.scss';

// Modern Header Styles
.modern-header {
    background: linear-gradient(135deg, #0081a2 0%, #005f7a 100%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        pointer-events: none;
    }
}

.header-main-content {
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include viewport-sm {
        padding: 0.75rem 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
}

.brand-section {
    .brand-link {
        display: flex;
        align-items: center;
        gap: 1rem;
        color: white;
        text-decoration: none;
        font-size: 1.75rem;
        font-weight: 300;
        letter-spacing: 2px;
        font-family: 'Palanquin', sans-serif;

        .brand-icon {
            font-size: 2rem;
            color: white;
        }

        .brand-text {
            text-transform: uppercase;
        }
    }
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 2rem;

    @include viewport-sm {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
    }
}

.language-section {
    display: flex;
    gap: 0.5rem;
    align-items: center;

    .modern-lang-btn {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);

        &:hover {
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &.active {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            font-weight: 600;
        }

        i {
            font-size: 0.875rem;
        }
    }

    .modern-live-btn {
        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &:hover {
            box-shadow: 0 4px 12px rgba(238, 90, 36, 0.4);
        }

        i {
            animation: pulse 2s infinite;
        }
    }
}

.notifications-section {
    .notification-link {
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        position: relative;

        .notification-icon {
            font-size: 1.25rem;
            transition: all 0.3s ease;
        }

        .notification-text {
            font-weight: 500;
            font-size: 0.875rem;
        }

        .unread-indicator {
            position: absolute;
            top: -8px;
            right: -8px;
            color: #ff4757;
            font-size: 0.5rem;
            animation: pulse 2s infinite;
        }
    }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.user-section {
    .login-section {
        .login-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: white;
            text-decoration: none;
            background: rgba(255, 255, 255, 0.15);
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);

            &:hover {
                background: rgba(255, 255, 255, 0.25);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            }

            i {
                font-size: 1.1rem;
            }
        }
    }

    .user-profile-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        min-width: 280px;
        display: flex;
        overflow: hidden;

        @include viewport-sm {
            min-width: auto;
            width: 100%;
        }

        .user-info-section {
            flex: 1;
            padding: 1rem;

            .user-details {
                .user-identity {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin-bottom: 0.5rem;

                    .message-action {
                        .message-link {
                            color: white;
                            background: rgba(255, 255, 255, 0.15);
                            padding: 0.5rem;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all 0.3s ease;
                            text-decoration: none;

                            &:hover {
                                background: rgba(255, 255, 255, 0.25);
                            }

                            i {
                                font-size: 1rem;
                            }
                        }
                    }

                    .user-name-section {
                        .user-link-wrapper, .user-display {
                            display: flex;
                            align-items: center;
                            gap: 0.5rem;

                            .user-link {
                                color: white;
                                text-decoration: none;
                                display: flex;
                                align-items: center;
                                gap: 0.5rem;
                                transition: all 0.3s ease;

                                &:hover {
                                    opacity: 0.8;
                                }
                            }

                            .user-avatar {
                                font-size: 1.5rem;
                                color: rgba(255, 255, 255, 0.9);
                            }

                            .user-name {
                                font-weight: 600;
                                font-size: 1rem;
                                color: white;
                            }
                        }

                        .user-display {
                            color: white;
                            font-weight: 600;
                            font-size: 1rem;
                        }
                    }
                }

                .user-meta {
                    .timezone-info {
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 0.875rem;

                        i {
                            font-size: 0.875rem;
                        }

                        .timezone-text {
                            font-weight: 400;
                        }
                    }
                }
            }
        }

        .logout-section {
            background: rgba(255, 255, 255, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            min-width: 60px;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 20%;
                bottom: 20%;
                width: 1px;
                background: rgba(255, 255, 255, 0.2);
            }

            &:hover {
                background: rgba(255, 255, 255, 0.25);
            }

            &:focus {
                outline: 2px solid rgba(255, 255, 255, 0.5);
                outline-offset: -2px;
            }

            .logout-icon {
                color: rgba(255, 255, 255, 0.9);
                font-size: 1.25rem;
                transition: all 0.3s ease;
            }

            &:hover .logout-icon {
                color: white;
                transform: scale(1.1);
            }
        }
    }
}



.modern-breadcrumb-section {
    background: #F5F5F5;
    border-top: 1px solid rgba(255, 255, 255, 0.2);

    .breadcrumb-container {
        padding: 1rem 2rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        @include viewport-sm {
            padding: 0.75rem 1rem;
            flex-wrap: wrap;
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            &.home-item {
                .home-link {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.1);
                    padding: 0.5rem;
                    border-radius: 8px;
                    transition: all 0.3s ease;
                    text-decoration: none;

                    &:hover {
                        background: rgba(102, 126, 234, 0.2);
                        transform: translateY(-1px);
                    }

                    i {
                        font-size: 1.1rem;
                    }
                }

                .menu-toggle {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.1);
                    padding: 0.5rem 1rem;
                    border-radius: 8px;
                    transition: all 0.3s ease;
                    text-decoration: none;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-weight: 600;

                    &:hover {
                        background: rgba(102, 126, 234, 0.2);
                        transform: translateY(-1px);
                    }
                }
            }

            .breadcrumb-link {
                color: #4a5568;
                text-decoration: none;
                font-weight: 500;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                transition: all 0.3s ease;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                &:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.1);
                }
            }

            .breadcrumb-separator {
                color: #a0aec0;
                font-size: 0.75rem;
            }
        }
    }

    .modern-menu-dropdown {
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        padding: 1rem 2rem;
        display: flex;
        gap: 1rem;
        background: #F5F5F5;

        @include viewport-sm {
            padding: 1rem;
            flex-direction: column;
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #4a5568;
            text-decoration: none;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            background: rgba(102, 126, 234, 0.05);

            &:hover {
                background: rgba(102, 126, 234, 0.15);
                color: #667eea;
                transform: translateY(-1px);
            }

            i {
                font-size: 1rem;
                color: #667eea;
            }
        }
    }
}

// Modern sidebar header
.modern-sidebar-header {
    height: $topDivHeight;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.8em;
    background: linear-gradient(135deg, #0081a2 0%, #005f7a 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    &.is-opaque {
        z-index: 1;
        position: relative;
        background: linear-gradient(135deg, #0081a2 0%, #005f7a 100%);
    }

    .sidebar-breadcrumb {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.1rem;

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .home-link {
                color: white;
                background: rgba(255, 255, 255, 0.15);
                padding: 0.5rem;
                border-radius: 8px;
                transition: all 0.3s ease;
                text-decoration: none;

                &:hover {
                    background: rgba(255, 255, 255, 0.25);
                    transform: translateY(-1px);
                }

                i {
                    font-size: 1.1rem;
                }
            }

            .breadcrumb-link {
                color: rgba(255, 255, 255, 0.9);
                text-decoration: none;
                font-weight: 500;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                transition: all 0.3s ease;

                &:hover {
                    color: white;
                    background: rgba(255, 255, 255, 0.15);
                }
            }

            .breadcrumb-separator {
                color: #a0aec0;
                font-size: 0.75rem;
            }
        }
    }

    .sidebar-actions {
        display: flex;
        align-items: center;
        gap: 1rem;

        .modern-support-btn {
            background: linear-gradient(45deg, #48bb78, #38a169);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.875rem;

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4);
                background: linear-gradient(45deg, #38a169, #2f855a);
            }

            i {
                font-size: 1rem;
            }
        }

        .sidebar-lang-switch {
            .lang-option {
                .lang-link {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    color: white;
                    text-decoration: none;
                    background: rgba(255, 255, 255, 0.15);
                    padding: 0.5rem 1rem;
                    border-radius: 20px;
                    font-weight: 500;
                    transition: all 0.3s ease;
                    cursor: pointer;

                    &:hover {
                        background: rgba(255, 255, 255, 0.25);
                        transform: translateY(-1px);
                    }

                    i {
                        font-size: 0.875rem;
                    }
                }
            }
        }
    }
}

// Modern login header
.modern-login-header {
    padding: 2rem;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    background: linear-gradient(135deg, #0081a2 0%, #005f7a 100%);
    color: white;

    &.is-abed-login {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        color: #2d3748;
        padding-bottom: 0;
        margin-bottom: 0;
        display: block;
    }

    .login-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        &.top-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }

    .login-breadcrumb {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        &.is-abed-login {
            padding: 0;
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .home-link {
                color: white;
                background: rgba(255, 255, 255, 0.15);
                padding: 0.5rem;
                border-radius: 8px;
                transition: all 0.3s ease;
                text-decoration: none;
                backdrop-filter: blur(10px);

                &:hover {
                    background: rgba(255, 255, 255, 0.25);
                    transform: translateY(-1px);
                }

                i {
                    font-size: 1.1rem;
                }
            }

            .breadcrumb-link {
                color: white;
                text-decoration: none;
                font-weight: 500;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                transition: all 0.3s ease;

                &:hover {
                    background: rgba(255, 255, 255, 0.15);
                }

                &.is-abed-login {
                    color: #2d3748;

                    &:hover {
                        background: rgba(102, 126, 234, 0.1);
                        color: #667eea;
                    }
                }
            }

            .breadcrumb-separator {
                color: rgba(255, 255, 255, 0.7);
                font-size: 0.75rem;
            }
        }
    }

    .login-logo-section {
        .header-logo {
            max-width: 9rem;
            height: auto;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
    }

    .login-lang-switch, .login-lang-switch-bottom {
        .lang-option {
            .lang-link {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                color: white;
                text-decoration: none;
                background: rgba(255, 255, 255, 0.15);
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-weight: 500;
                transition: all 0.3s ease;
                cursor: pointer;
                backdrop-filter: blur(10px);

                &:hover {
                    background: rgba(255, 255, 255, 0.25);
                    transform: translateY(-1px);
                }

                i {
                    font-size: 0.875rem;
                }
            }
        }
    }

    // ABED specific styles
    &.is-abed-login {
        .login-breadcrumb .breadcrumb-item .home-link {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);

            &:hover {
                background: rgba(102, 126, 234, 0.2);
            }
        }

        .login-lang-switch .lang-option .lang-link,
        .login-lang-switch-bottom .lang-option .lang-link {
            color: #4a5568;
            background: rgba(102, 126, 234, 0.1);

            &:hover {
                background: rgba(102, 126, 234, 0.2);
                color: #667eea;
            }
        }
    }
}

.mutable-lang-select {
    display: flex;
    align-items: center;
    flex-direction: row;
    a {
        margin-left: 0.3em
    }
    img {
        width: 1.5em;
    }
}

button.support-link {
    @extend %clean-button;
    color: #333;
    font-weight: 600;
}

.is-opaque {
    z-index: 1;
    position: relative;
    background-color: #fff;
}

.logo-container {
    height: calc(100% + calc(3*9.1%) + 9.1%);
    display: flex;
    align-items: flex-start;
    width: 8%;
    .abed-logo {
        // The blue square of ABED logo is 9.1% of the width of the whole logo
        // These were the requested margins from customer
        width: 100%;
        height: auto;
        margin-bottom: 9.1%;
        margin-top: calc(3 * 9.1%);
    }
}

// Utility classes
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
