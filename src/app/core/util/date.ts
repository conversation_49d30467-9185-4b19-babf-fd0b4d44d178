import mtz, { lang, Moment } from "moment-timezone";
import { LangService } from "../lang.service";


export const getYearMonthMaxDate = (year:number, month_1_based:number) => {
  // Create a date for the first of the month
  const date = new Date(year, month_1_based-1, 1);
  // Set the date to the last day of the month
  date.setMonth(date.getMonth()+1); 
  date.setDate(0);
  // Return the last date of the month
  return date.getDate();
}

export const calculateDateEnd = (startDate: string, daysCount: number, isAdding: boolean, exceptionDates: string[], isWeekendsExcluded: boolean) =>{
  if(!daysCount || daysCount == 0){
    return null;
  }
  let currentDate = new Date(startDate);
  let daysProcessed = 0;
  // Convert exception dates to a set of strings for faster lookup
  const exceptionSet = new Set(exceptionDates.map(date => date.toString()));

  while (daysProcessed < daysCount) {
    // Add or subtract a day based on the isAdding flag
    currentDate.setDate(currentDate.getDate() + (isAdding ? 1 : -1));
    // Check if the current day is a weekend (Saturday or Sunday) or an exception date
    const dayOfWeek = currentDate.getDay();
    const isWeekend = (dayOfWeek === 0 || dayOfWeek === 6); // false and true
    const currentDateCopy = currentDate; // the let is making it mutable and the iso is causing havoc.
    const currentDateFormatted = currentDateCopy.toISOString().split('T')[0]; // convert into format yyyy-dd-mm
    const isException = exceptionSet.has(currentDateFormatted); 
    const ignoreDayIntoCalculation = (!isWeekendsExcluded || !isWeekend) && !isException;
    if (ignoreDayIntoCalculation) {
      daysProcessed++;
    }
  }
  return currentDate;
}

export function renderDateRangeLocal(start: Moment, end: Moment, lang: LangService, startOvrdString?: string, endOvrdString?: string): string {
  
  let startRendered = startOvrdString || start.format('MMM D, YYYY h:mma z')
  let endRendered = endOvrdString || end.format('MMM D, YYYY h:mma z')
  
  const fromTrans = lang.tra("lbl_date_from_lc")
  const toTrans = lang.tra("lbl_date_to_lc")

  return `${fromTrans} ${startRendered} ${toTrans} ${endRendered}`
}

  