<div *ngIf="!isSelectingSession && formEntriesList && (!isActive || isPartialActive)">
    <ng-container *ngIf="isSimplePracticeSetup()">
        <!-- TODO:TRANS -->
        <ng-container *ngIf="!forTeacher">
            <sa-widget-form-entry *ngFor="let entry of saWidgetFormEntries()" [formEntries]="formEntriesList"
                [formEntry]="entry" (selected)="checkSelectionV2(entry, $event)">
            </sa-widget-form-entry>
        </ng-container>
        <ng-container *ngIf="schoolClassGroupId">
            <ng-container *ngIf="!classAssessments">Loading...</ng-container>
            <ng-container *ngIf="classAssessments">

                <div class="field">
                    <label class="label">What type of assessment are you supervising?</label>
                    <div class="control">
                        <div class="select">
                            <select [(ngModel)]="selectedSessionType" (change)="onAssessmentTypeChange()">
                                <option *ngFor="let option of assessmentTypeOptions" [value]="option.id">{{option.caption}}</option>
                            </select>
                        </div>
                    </div>
                </div>
                <ng-container *ngIf="displayCourseSubSelection()">
                    <div class="notification is-small" *ngIf="coursesFiltered.length == 0">
                        There are no courses of this type available for this class/grouping.
                    </div>
                    <div class="field" *ngIf="coursesFiltered.length">
                        <label class="label">What course are you supervising?</label>
                        <div class="control">
                            <div class="select">
                                <select [(ngModel)]="selectedCourse" (change)="onAssessmentCourseChange()">
                                    <option *ngFor="let option of coursesFiltered" [value]="option.id"><tra-md [slug]="option.label"></tra-md></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div>
                        <!-- <mat-slide-toggle 
                            [(ngModel)]="showAllAssessments"
                            (change)="onAssessmentTypeChange()"
                            color="primary"
                            [disabled]="false"
                        > 
                        <tra slug="lbl_session_include_other_lang"></tra>
                        </mat-slide-toggle> -->
                    </div>
                </ng-container>
                <ng-container *ngIf="displayAssessmentSubSelection()">
                    <div class="notification is-small is-warning" *ngIf="getSessionType().subtitleCaption">
                        {{getSessionType().subtitleCaption}}
                    </div>
                    <div class="notification is-small" *ngIf="classAssessmentsFiltered.length == 0">
                        There are no assessments of this type available for this class/grouping.
                    </div>
                    <div class="field" *ngIf="classAssessmentsFiltered.length">
                        <label class="label">Which assessment is being delivered in this session?</label>
                        <div class="control">
                            <div class="select">
                                <select [(ngModel)]="savePayload.slug" (change)="onSelectAssessment()" >
                                    <option></option>
                                    <ng-container *ngFor="let asmt of classAssessmentsFiltered">
                                        <option [value]="asmt.type_slug">
                                            {{asmt.long_name}} 
                                        </option>
                                    </ng-container>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div>
                        <!-- <mat-slide-toggle 
                            [(ngModel)]="showAllAssessments"
                            (change)="onAssessmentTypeChange()"
                            color="primary"
                            [disabled]="false"
                        > 
                        <tra slug="lbl_session_include_other_lang"></tra>
                        </mat-slide-toggle> -->
                    </div>
                    <div *ngIf="savePayload.isScheduled && selectedAssessment">

                        <ng-container *ngIf="!savePayload.is_schedule_range">
                            <div *ngIf="getCurrentAltDates()" class="notification is-info" style="margin-bottom:2em;">
                                <div style="display: flex; flex-direction: row; align-items: center; gap: 1em;">
                                    <span>Scheduled Start:</span>
                                    <select [(ngModel)]="savePayload.scheduled_time" class="select is-small">
                                        <option [value]="selectedAssessment.test_date_start"> {{renderDateTime(selectedAssessment.test_date_start)}} </option>
                                        <option 
                                            *ngFor="let altDate of getCurrentAltDates()" 
                                            [value]="altDate"
                                        > 
                                            {{renderDateTime(altDate)}} 
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div *ngIf="!getCurrentAltDates()">
                                <div class="notification is-info">
                                    <div>Scheduled Start: {{renderDateTime(selectedAssessment.test_date_start)}} </div>
                                </div>
                                <!-- <div>Duration: {{selectedAssessment.test_duration}}</div> -->
                            </div>
                        </ng-container>

                        <ng-container *ngIf="savePayload.is_schedule_range">
                            <div class="notification is-info">
                                Scheduling range: {{renderDateTime(scheduledRangeStarted)}}<br>
                                To: {{renderDateTime(scheduledRangeEnded)}}
                            </div>
                            <div style="margin-bottom:2em;">
                                <tra-md slug="txt_selectdate_invig"></tra-md>
                                <div style="max-width:28em">
                                    <dob-input 
                                        [formControlModel]="selectedDateVal" 
                                        (change)="onChange()"
                                        [isDateHidden]="true"
                                    ></dob-input>
                                </div>
                            </div>
                            <div>
                                <tra-md slug="txt_selecttime_invig"></tra-md>
                                <filterable-dropdown 
                                    label="Time"
                                    [(ngModel)]="selectedTimeOption" 
                                    [onLoadValue]="selectedTimeOption" 
                                    (ngModelChange)="onChange()"
                                    [options]="timeOptions"
                                    inputWidth="8em"
                                ></filterable-dropdown>
                            </div>
                        </ng-container>
                    
                    </div>
                </ng-container>
                

                <div style="margin-top: 1em;" *ngIf="selectedSessionType && savePayload.slug" class="field-rows">
                    <div class="field" style="flex-grow:1" *ngIf="allowCustomName(selectedAssessment)">
                        <label class="label">Name / Description (optional)</label>
                        <div class="control">
                          <input class="input" [(ngModel)]="savePayload.sessionName"  type="text" placeholder="">
                        </div>
                    </div>
                    <div class="field" style="flex-grow:1" *ngIf="allowCustomDuration(selectedAssessment)">
                        <label class="label"><tra slug="lbl_invig_duration_opt"></tra></label>
                        <div class="control">
                          <input class="input" [(ngModel)]="savePayload.duration_m"  type="number" placeholder="minutes">
                        </div>
                    </div>
                    <div class="field" style="flex-grow:1" *ngIf="allowCapacity(selectedAssessment)">
                        <label class="label"> <tra slug="lbl_invig_num_stu"></tra></label>
                        <div class="control">
                          <input class="input" [(ngModel)]="savePayload.capacity"  type="number" placeholder="students">
                        </div>
                    </div>
                </div>

            </ng-container>
        </ng-container>
    </ng-container>
    <ng-container *ngIf="!isSimplePracticeSetup()">
        <sa-widget-form-entry
            *ngFor="let entry of saWidgetFormEntries()"
            [formEntries]="formEntriesList"
            [formEntry]="entry"
            (selected)="checkSelection(entry, $event)">
        </sa-widget-form-entry>

        <div *ngIf="disabledAssessmentTypes.length !== 0 && currentlySelectedAssessmentType == null"  style="color: red;">
        <div
        [innerHTML]="formatDisabledAssessmentTypesText()">
        </div>
        <br>
        </div>

        <div *ngIf="currentlySelectedAssessment != null">
        The <strong> {{formatAssessmentName(currentlySelectedAssessment.asmt_slug, currentlySelectedAssessment.lang)}}
        </strong> assessment is scheduled to start on <strong>
        {{formatAssessmentDateTime(currentlySelectedAssessment.test_date_start)}}</strong>.
        <br>
        <br>
        </div>

        <div *ngIf="isThereAtLeastOneDisabledAssessment() && !currentlySelectedAssessment">
        <div *ngFor="let type of disabledAssessments; let i = index" style="color: red;">
            <div
            *ngIf="type.assessmentNames.length !== 0"
            [innerHTML]="formatDisabledAssessmentsText(type.reason, type.assessmentNames)">
            </div>
            <div *ngIf="i !== disabledAssessments.length - 1">
                <br>
            </div>
          </div>
      </div>
    </ng-container>

    <ng-container *ngIf="showDatePicker">
        <div style="margin:1rem 0 1rem 0">
            <ng-container *ngIf="isG9||isOsslt || whitelabelService.isABED()">
                <ng-container *ngIf="showSessionA()" >
                    <div *ngIf="!removesessionB" style="margin-bottom:0.5rem">
                      <strong><tra slug="session_a" style="color:teal" ></tra></strong>
                    </div>
                    <div *ngIf="removesessionB" style="margin-bottom:0.5rem">
                      <strong>
                        <tra
                            slug="g9_sample_session"
                            style="color:teal">
                        </tra>
                    </strong>
                    </div>
                    <tra-md
                        [slug]="getAssessmentDatesSlug()"
                        [props]="getDateProps()">
                    </tra-md>
                    <div  class="times">
                        <div class="label"><tra slug="lbl_date"></tra></div>
                        <div class="values">
                            <input-date [fc]="formEntries.sessionAStartDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)"></input-date>
                        </div>
                    </div>
                    <div *ngIf="showDatePicker" class="times">
                        <!-- <tra-md slug="g9_operational_test"></tra-md> -->
                        <div class="label"><tra slug="lbl_time"></tra></div>
                        <div class="values">
                            <input-time [fc]="formEntries.sessionAStartTime.formControlRef" type="time"></input-time>
                        </div>
                        <tra class="TZAbbr" [slug]="getWhitelabelTimeZone()"></tra>
                    </div>
                </ng-container>
                <div *ngIf="!showSessionA() && showSessionB() && !whitelabelService.isABED()">
                    <tra slug="msg_session_a_submitted"></tra>
                </div>
                <ng-container *ngIf="showSessionB()">
                    <hr/>
                    <strong><tra slug="session_b"></tra></strong>
                    <div  class="times">
                        <div class="label"><tra slug="lbl_date"></tra></div>
                        <div class="values">
                            <input-date [fc]="formEntries.sessionBStartDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)"></input-date>
                        </div>
                    </div>
                    <div *ngIf="showDatePicker" class="times">
                        <!-- <tra-md slug="g9_operational_test"></tra-md> -->
                        <div class="label"><tra slug="lbl_time"></tra></div>
                        <div class="values">
                            <input-time [fc]="formEntries.sessionBStartTime.formControlRef" type="time"></input-time>
                        </div>
                    </div>
                </ng-container>
                <div *ngIf="!showSessionA() && !showSessionB() && !whitelabelService.isABED()">
                    <tra [slug]="getCompletedSlug()"></tra>
                </div>
            </ng-container>

            <ng-container *ngIf="isPrimary||isJunior">
                <ng-container>
                    <!-- Primary && Junior Session Language -->
                    <div style="margin-bottom: 1.25em;"><strong><tra slug="pj_lang"></tra></strong></div>
                    <div  class="times">
                        <div class="label"><tra slug="pj_lbl_start_date"></tra></div>
                        <div class="values">
                            <input-date [fc]="formEntries.sessionLangStartDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)" [isRequired]="false"></input-date>
                        </div>
                    </div>
                    <div  class="times">
                        <div class="label"><tra slug="pj_lbl_end_date"></tra></div>
                        <div class="values">
                            <input-date [fc]="formEntries.sessionLangEndDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)" [isRequired]="false"></input-date>
                        </div>
                    </div>
                    <hr />
                </ng-container>
                <ng-container>
                    <div>
                        <tra slug="msg_session_language_start"></tra>
                    </div>
                    <hr />
                </ng-container>
                <!-- <ng-template #languageActive>
                    <div>
                        <tra slug="msg_session_language_start"></tra>
                    </div>
                </ng-template> -->

                <ng-container *ngIf="canEditDatetimePicker(datePickerType.Math); else mathActive">
                    <div style="margin-bottom: 1.25em;"><strong><tra slug="pj_math"></tra></strong></div>    <!-- Primary && Junior Session Math -->
                    <div  class="times">
                        <div class="label"><tra slug="pj_lbl_start_date"></tra></div>
                        <div class="values">
                            <input-date [fc]="formEntries.sessionMathStartDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)" [isRequired]="false"></input-date>
                        </div>
                    </div>
                    <div  class="times">
                        <div class="label"><tra slug="pj_lbl_end_date"></tra></div>
                        <div class="values">
                            <input-date [fc]="formEntries.sessionMathEndDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)" [isRequired]="false"></input-date>
                        </div>
                    </div>
                </ng-container>
                <ng-template #mathActive>
                    <div>
                        <tra slug="msg_session_math_start"></tra>
                    </div>
                </ng-template>
            </ng-container>
        </div>

        <ng-container *ngIf="showDatePicker">
            <div style="margin:1rem 0 1rem 0">
                <ng-container *ngIf="isG9||isOsslt || whitelabelService.isABED()">
                    <ng-container *ngIf="showSessionA()" >
                        <div *ngIf="!removesessionB" style="margin-bottom:0.5rem">
                        <strong><tra slug="session_a" style="color:teal" ></tra></strong>
                        </div>
                        <div *ngIf="removesessionB" style="margin-bottom:0.5rem">
                        <strong><tra slug="g9_sample_session" style="color:teal"></tra></strong>
                        </div>
                        <tra-md
                            [slug]="getAssessmentDatesSlug()"
                            [props]="getDateProps()">
                        </tra-md>
                        <div  class="times">
                            <div class="label"><tra slug="lbl_date"></tra></div>
                            <div class="values">
                                <input-date [fc]="formEntries.sessionAStartDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)"></input-date>
                            </div>
                        </div>
                        <div *ngIf="showDatePicker" class="times">
                            <!-- <tra-md slug="g9_operational_test"></tra-md> -->
                            <div class="label"><tra slug="lbl_time"></tra></div>
                            <div class="values">
                                <input-time [fc]="formEntries.sessionAStartTime.formControlRef" type="time"></input-time>
                            </div>
                            <tra class="TZAbbr" [slug]="getWhitelabelTimeZone()"></tra>
                        </div>
                    </ng-container>
                    <div *ngIf="!showSessionA() && showSessionB() && !whitelabelService.isABED()">
                        <tra slug="msg_session_a_submitted"></tra>
                    </div>
                    <ng-container *ngIf="showSessionB()">
                        <hr/>
                        <strong><tra slug="session_b"></tra></strong>
                        <div  class="times">
                            <div class="label"><tra slug="lbl_date"></tra></div>
                            <div class="values">
                                <input-date [fc]="formEntries.sessionBStartDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)"></input-date>
                            </div>
                        </div>
                        <div *ngIf="showDatePicker" class="times">
                            <!-- <tra-md slug="g9_operational_test"></tra-md> -->
                            <div class="label"><tra slug="lbl_time"></tra></div>
                            <div class="values">
                                <input-time [fc]="formEntries.sessionBStartTime.formControlRef" type="time"></input-time>
                            </div>
                        </div>
                    </ng-container>
                    <div *ngIf="!showSessionA() && !showSessionB() && !whitelabelService.isABED()">
                        <tra [slug]="getCompletedSlug()"></tra>
                    </div>
                </ng-container>

                <ng-container *ngIf="isPrimary||isJunior">
                    <ng-container>
                        <!-- Primary && Junior Session Language -->
                        <div style="margin-bottom: 1.25em;"><strong><tra slug="pj_lang"></tra></strong></div>
                        <div  class="times">
                            <div class="label"><tra slug="pj_lbl_start_date"></tra></div>
                            <div class="values">
                                <input-date [fc]="formEntries.sessionLangStartDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)" [isRequired]="false"></input-date>
                            </div>
                        </div>
                        <div  class="times">
                            <div class="label"><tra slug="pj_lbl_end_date"></tra></div>
                            <div class="values">
                                <input-date [fc]="formEntries.sessionLangEndDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)" [isRequired]="false"></input-date>
                            </div>
                        </div>
                        <hr />
                    </ng-container>
                    <ng-container>
                        <div>
                            <tra slug="msg_session_language_start"></tra>
                        </div>
                        <hr />
                    </ng-container>
                    <!-- <ng-template #languageActive>
                        <div>
                            <tra slug="msg_session_language_start"></tra>
                        </div>
                    </ng-template> -->

                    <ng-container *ngIf="canEditDatetimePicker(datePickerType.Math); else mathActive">
                        <div style="margin-bottom: 1.25em;"><strong><tra slug="pj_math"></tra></strong></div>    <!-- Primary && Junior Session Math -->
                        <div  class="times">
                            <div class="label"><tra slug="pj_lbl_start_date"></tra></div>
                            <div class="values">
                                <input-date [fc]="formEntries.sessionMathStartDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)" [isRequired]="false"></input-date>
                            </div>
                        </div>
                        <div  class="times">
                            <div class="label"><tra slug="pj_lbl_end_date"></tra></div>
                            <div class="values">
                                <input-date [fc]="formEntries.sessionMathEndDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)" [isRequired]="false"></input-date>
                            </div>
                        </div>
                    </ng-container>
                    <ng-template #mathActive>
                        <div>
                            <tra slug="msg_session_math_start"></tra>
                        </div>
                    </ng-template>
                </ng-container>
            </div>
        </ng-container>
    </ng-container>

    <ng-container *ngIf="getSystemMessageSlug()">
        <system-message [slug]="getSystemMessageSlug()"></system-message>
    </ng-container>
</div>
