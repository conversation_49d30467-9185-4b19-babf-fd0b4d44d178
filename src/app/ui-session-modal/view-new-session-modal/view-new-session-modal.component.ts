import { mtz } from 'src/app/core/util/moment';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { ISession } from 'src/app/ui-schooladmin/data/types';
import { G9DemoDataService } from 'src/app/ui-schooladmin/g9-demo-data.service';
import { ClassFilterId, MySchoolService } from 'src/app/ui-schooladmin/my-school.service';
import { ASSESSMENT, SCHEDULER } from 'src/app/ui-teacher/data/types';
import { ISaFormEntry, SaFormType, initFormEntries } from 'src/app/ui-schooladmin/sa-widget-form-entry/sa-widget-form-entry.component';
import * as moment from 'moment-timezone';
import { FormGroup, FormControl } from '@angular/forms';
import { dateAndTimeToDbDate, dbDateToDateWithDashes, findClosestTimeOption, initTimeOptions } from 'src/app/ui-partial/input-time/input-time.component';

// todo:DUPE functionality appears to be duplicated from another component

// todo:DB_DATA_MODEL hard-coded value should be sourced from DB
// check: I don't think we are using this outside of EQAO
enum DatePickerType 
{
  Language = 0,
  Math = 1
}

enum DisabledAssessmentReason
{
  NO_DT = "NO_DATETIME_SCHEDULED",
  PAST_DT = "PAST_DATETIME_SCHEDULED"
}

interface DisabledAssessments
{
  reason: string,
  assessmentNames: string[],
  key: string
}

interface ICourse {
  id : number,
  label: string,
  is_sample: number,
  is_field_test: number,
}


interface IClassAssessment {
  is_field_test: number,
  test_window_id : number,
  twtar_id: number,
  type_slug: string,
  long_name: string,
  is_secured: number,
  is_questionnaire: number,
  is_sample: number,
  test_duration: number,
  test_date_start: string,
  date_start: string,
  date_end: string,
  is_school_allowed_strict: number,
  is_session_name_excluded: number,
  is_session_duration_excluded: number,
}

enum KnownSessionFilterFlags { 
  is_field_test = 'is_field_test',
  is_sample = 'is_sample',
}

interface AssessmentTypeOption {
  id: AssessmentTypeOptions,
  caption: string,
  subtitleCaption?: string,
  filter: any,
  label?:string,
  is_use_course_sub_selection?: number
}
// todo:DB_DATA_MODEL hard-coded value should be sourced from DB
export enum AssessmentTypeOptions 
{
  FIELD_TEST =  'FIELD_TEST',
  OPERATIONAL = 'OPERATIONAL'
}
@Component({
  selector: 'view-new-session-modal',
  templateUrl: './view-new-session-modal.component.html',
  styleUrls: ['./view-new-session-modal.component.scss']
})
export class ViewNewSessionModalComponent implements OnInit {
  @Input() sessions: ISession[];
  @Input() savePayload: { [key: string]: any };
  @Input() saveProp: string;
  @Input() isActive:boolean = false;
  @Input() forTeacher :boolean = false;
  @Input() isPartialActive:boolean = false;
  @Input() isEditing:boolean = false;
  @Input() schoolClassGroupId:number;
  @Output() isDateValid = new EventEmitter();

  constructor(
    private g9DemoData: G9DemoDataService,
    public lang: LangService,
    private auth: AuthService,
    private routes: RoutesService,
    public whitelabelService: WhitelabelService,
    private schoolService: MySchoolService,
  ) { }

  classes: any[];
  currentlySelectedAssessment = null; // object
  currentlySelectedAssessmentType = null; // object
  ASSESSMENT = ASSESSMENT;
  disabledAssessmentTypes: string[] = [];
  disabledAssessments: DisabledAssessments[] = 
  [
    {
      reason: this.whitelabelService.isABED() ? this.lang.tra("abed_assessment_cannot_be_picked") : "The following assessments cannot be picked due to having no scheduled date and time: ",
      key: DisabledAssessmentReason.NO_DT,
      assessmentNames: []
    },
    {
      reason: this.whitelabelService.isABED() ? this.lang.tra("abed_past_date") : "The following assessments cannot be picked due to the scheduled date and time being in the past: ",
      assessmentNames: [],
      key: DisabledAssessmentReason.PAST_DT
    }
  ];
  SCHEDULER = SCHEDULER;
  // todo:DB_DATA_MODEL hard-coded value should be sourced from DB
  assessments = [
    ASSESSMENT.PRIMARY_SAMPLE,
    ASSESSMENT.PRIMARY_OPERATIONAL,
    ASSESSMENT.JUNIOR_SAMPLE,
    ASSESSMENT.JUNIOR_OPERATIONAL,
    ASSESSMENT.G9_OPERATIONAL, 
    ASSESSMENT.G9_SAMPLE,
    ASSESSMENT.OSSLT_OPERATIONAL, 
    ASSESSMENT.OSSLT_SAMPLE,
    ASSESSMENT.TCLE_OPERATIONAL,
    ASSESSMENT.TCLE_SAMPLE,
    ASSESSMENT.TCN_OPERATIONAL,
    ASSESSMENT.TCN_SAMPLE,
    ASSESSMENT.SCIENCES8_OPERATIONAL,
    ASSESSMENT.SCIENCES8_SAMPLE,
    ASSESSMENT.MBED_SAMPLE,
    ASSESSMENT.ABED_SAMPLE_FR_FIRST_6,
    ASSESSMENT.ABED_SAMPLE_FR_IMMERSION_6,
    ASSESSMENT.ABED_SAMPLE_SCIENCE_6,
    ASSESSMENT.ABED_SAMPLE_MATH_6,
    ASSESSMENT.ABED_SAMPLE_ELA_6,
    ASSESSMENT.ABED_KE_SOCIAL_STUDIES_9,
    ASSESSMENT.ABED_KE_SCIENCES_9,
    ASSESSMENT.ABED_KE_MATH_9,
    ASSESSMENT.ABED_KE_ELA_9B,
    ASSESSMENT.ABED_KE_ELA_9A,
    ASSESSMENT.ABED_SOCIAL_STUDIES_9,
    ASSESSMENT.ABED_SCIENCES_9,
    ASSESSMENT.ABED_MATH_9B,
    ASSESSMENT.ABED_MATH_9A,
    ASSESSMENT.ABED_FLA_9B,
    ASSESSMENT.ABED_FLA_9A,
    ASSESSMENT.ABED_ELA_9B,
    ASSESSMENT.ABED_ELA_9A,
    ASSESSMENT.ABED_SOCIAL_STUDIES_6,
    ASSESSMENT.ABED_SCIENCES_6,
    ASSESSMENT.ABED_MATH_6B,
    ASSESSMENT.ABED_MATH_6A,
    ASSESSMENT.ABED_MATH_6A2,
    ASSESSMENT.ABED_MATH_6B2,
    ASSESSMENT.ABED_FLA_6B,
    ASSESSMENT.ABED_FLA_6A,
    ASSESSMENT.ABED_ELA_6B,
    ASSESSMENT.ABED_ELA_6A
  ];

  // todo:DB_DATA_MODEL
  selectedSessionType?:AssessmentTypeOptions
  assessmentTypeOptions:AssessmentTypeOption[] = [
    {
      id: AssessmentTypeOptions.OPERATIONAL,
      caption: this.lang.tra((this.whitelabelService.getSiteText('lbl_op_asmts') || 'Operational (not practice)')) ,
      filter: { is_sample: 0 },
      label: this.renderTitle(AssessmentTypeOptions.OPERATIONAL) // deprecated for ABED
    },
    {
      id: AssessmentTypeOptions.FIELD_TEST,
      caption: this.lang.tra('lbl_sample_asmts'),
      filter: { is_sample: 1 },
      subtitleCaption: this.lang.tra('txt_stu_resp_pr_exams'),
      label: this.renderTitle(AssessmentTypeOptions.FIELD_TEST) // deprecated for ABED
    },
  ]
  schedules = [SCHEDULER.LATER, SCHEDULER.NOW]
  assessmentTypes: any[];
  scheduleOptions: any[]
  showDatePicker: boolean = false;
  assessmentToBeScheduled: ASSESSMENT;
  selectedAssessment:IClassAssessment;
  removesessionB: boolean;
  formEntries: { [key: string]: ISaFormEntry};
  timeEntries:{ [key: string]: ISaFormEntry};
  formGroup = new FormGroup({
    date: new FormControl(),
    time: new FormControl()
  })
  formEntriesList: ISaFormEntry[];
  selectedSession: Partial<ISession>;
  isSelectingSession: boolean;
  isSessionCompleted?: boolean[];
  isValidAssessmentType: boolean;
  isPrimary:boolean;
  isJunior:boolean;
  isG9:boolean;
  isOsslt:boolean;
  isFIClass:boolean = false;  
  showAllAssessments:boolean = true;
  scheduledRangeStarted: string;
  scheduledRangeEnded: string;
  timeOptions:{caption:string, val:string}[] = []
  selectedTimeOption:{caption:string, val:string};
  selectedDateVal = new FormControl();

  ngOnInit() {
    this.abedTeacherRestriction();
    this.isValidAssessmentType = Object.values(ClassFilterId).includes(this.savePayload.filter);
    this.initContextInfo();
    this.timeOptions = initTimeOptions();
    if(this.forTeacher){
      this.initAssessmentsV2();
      this.initSelectedSession();
    }else{
      this.initSelectedSession();
      this.initAssessmentsV2();
    }
    if (this.isEditing) {
      this.updateSessionsCompleted();
    }
    this.disableAssessmentTypesIfNeeded();
  }
  initContextInfo() {
    this.initializeScheduleOptions();
    this.initializeAssessments()
    this.initializeClasses();  
  }

  isSimplePracticeSetup(){
    return !!this.whitelabelService.getSiteFlag('IS_SESSION_PRACTICE_ONLY')
  }

  classAssessments:any[]
  async initAssessmentsV2(){
    if (this.isSimplePracticeSetup()){
      if (this.forTeacher){
        this.selectClassV2(this.schoolClassGroupId);
      }else{
        this.formEntriesList = initFormEntries(this.selectedSession, [
          { 
            type: SaFormType.SELECT, 
            valProp: 'classId', 
            label: "abed_class_code", 
            options: this.classes, 
            isLocked: false 
          }
        ]);
      }
    }
  }

  classAltDates:{[prop:string]:string[]} = {};
  courseOptions: ICourse[] = [];

  async selectClassV2(schoolClassGroupId){
    this.schoolClassGroupId = schoolClassGroupId;
    const {assessments, dateExceptions, assessmentTypeOptions, courseOptions, systemMessages} = await this.auth.apiGet(this.routes.EDUCATOR_CLASS_ASSESSMENTS, this.schoolClassGroupId);
    if (assessmentTypeOptions){
      this.assessmentTypeOptions = assessmentTypeOptions.map(record => {
        const label = this.lang.tra(record.label);
        const filter = JSON.parse(record.filter)
        return {
          id: record.asmt_cat_slug,
          caption:  label,
          filter,
          label,
          is_use_course_sub_selection: record.is_use_course_sub_selection,
        }

      });
    }
    this.classAltDates = dateExceptions
    this.classAssessments = assessments;
    this.courseOptions = courseOptions || [];
    if (this.classAssessments.length == 1){
      this.selectAssessment(this.classAssessments[0]);
      this.savePayload.payload.data.slug = this.classAssessments[0].type_slug;
    }
  }

  selectedAsmtDateStart:string;
  getCurrentAltDates(){
    return this.classAltDates[this.selectedAssessment.type_slug];
  }

  renderDateTime(dateStr:string){
    return mtz(dateStr).format(this.lang.tra('datefmt_dashboard_long'))
  }

  getSessionType(){
    for (let option of this.assessmentTypeOptions){
      if (this.selectedSessionType == option.id){
        return option
      }
    }
  }

  displayCourseSubSelection(){
    const threeSelectionsPath = this.shouldCourseSubSelectionBeEnabled();
    return threeSelectionsPath && this.selectedSessionType;
  }

  displayAssessmentSubSelection(){
    const threeSelectionsPath =  this.shouldCourseSubSelectionBeEnabled();
    const twoSelectionsPath   = !this.shouldCourseSubSelectionBeEnabled();

    return (twoSelectionsPath && this.selectedSessionType) || (threeSelectionsPath && this.selectedCourse);
  }

  shouldCourseSubSelectionBeEnabled(){
    for (let option of this.assessmentTypeOptions){
      if (this.selectedSessionType == option.id){
        return !!option.is_use_course_sub_selection
      }
    }
  }

  getSelectedCourse(){
    for (let option of this.courseOptions){
      if (this.selectedCourse == option.id){
        return option;
      }
    }
  }

  selectedCourse: number;
  classAssessmentsFiltered:IClassAssessment[] = [];
  coursesFiltered:ICourse[] = [];
  updateClassAssessmentsFiltered(){
    this.classAssessmentsFiltered = [];
    this.coursesFiltered = [];
    const sessionType = this.getSessionType();
    const course = this.getSelectedCourse();
    if (sessionType){
      const filter = sessionType.filter 
      this.coursesFiltered = this.processFiltering(this.courseOptions, filter);
      if(!this.shouldCourseSubSelectionBeEnabled()){
        this.classAssessmentsFiltered = this.processFiltering(this.classAssessments, filter);
      }
      else if(course){
        this.classAssessmentsFiltered = this.processFiltering(this.classAssessments, filter, course);
      }
    }
  }

  processFiltering(originalArray, filter, course?){
    const filtered = [];
    
    
    for (let item of originalArray){
      let isFilterPass = true;
      if(!this.showAllAssessments){
        const lang = this.g9DemoData.schoolDist[0].brd_lang || 'en';
        // hide en school with is_fi = 0 and fr school with is_fi = 1
        if(lang !== item['lang'] || (lang == 'fr' && item['is_fi'] == 1)){
          isFilterPass = false;
        }
        if((lang == 'en'  && item['is_fi']) == 1){
          isFilterPass = true;
        }
      }
      if (filter){
        Object.keys(filter).forEach((filterProp: any) => {
          if (filter[filterProp] != item[filterProp]){
            isFilterPass  = false
          }
        })
      }
      if(course){
        if (item.ac_id != this.selectedCourse){
          isFilterPass = false;
        }
      }
      if (isFilterPass){
        filtered.push(item);
      }
    }
    return filtered;
  }

  onAssessmentTypeChange(){
    this.clearCourseSelection();
    this.updateClassAssessmentsFiltered()
    if (this.classAssessmentsFiltered.length == 1){
      const asmt = this.classAssessmentsFiltered[0];
      this.selectAssessment(asmt)
    }
  }

  onAssessmentCourseChange(){
    this.clearAssessmentSelection();
    this.updateClassAssessmentsFiltered();
    if (this.classAssessmentsFiltered.length == 1){
      const asmt = this.classAssessmentsFiltered[0];
      this.selectAssessment(asmt)
    }
  }
  
  selectAssessment(asmt:{type_slug: string}){ 
    this.savePayload.slug = asmt.type_slug;
    this.savePayload.payload.data.slug = asmt.type_slug;
    this.onSelectAssessment();
  }
  onSelectAssessment(){
    this.selectedAssessment = null
    for (let asmt of this.classAssessments){
      if (asmt.type_slug == this.savePayload.slug){
        this.selectedAssessment = asmt;
        
        if (asmt.is_sample == 1){
          this.savePayload.duration_m = asmt.test_duration || undefined
        }
        else {
          this.savePayload.duration_m = undefined;
        }
        
        this.savePayload.isScheduled = (+asmt.is_scheduled==1)
        if (this.savePayload.isScheduled){
          this.savePayload.scheduled_time = asmt.test_date_start
        }
        else {
          this.savePayload.scheduled_time = null;
        }
        this.savePayload.is_schedule_range = (+asmt.is_schedule_range==1)
        
        if(this.savePayload.is_schedule_range){
          const dts = this.savePayload.scheduled_time;
          this.selectedDateVal.setValue(dbDateToDateWithDashes(dts));
          this.timeOptions = initTimeOptions();
          const date_time_start_moment = moment.tz(this.savePayload.scheduled_time, moment.tz.guess())
          const hours = parseInt(date_time_start_moment.format("HH"));
          const minutes = parseInt(date_time_start_moment.format("mm"));
          this.selectedTimeOption = findClosestTimeOption(hours, minutes, this.timeOptions);
          let min_dates_allowed: number[] = [];
          this.savePayload.scheduled_time = null;
          const dateExceptions = this.classAltDates[asmt.type_slug]
          if(dateExceptions){
            for (const date_exception of dateExceptions){
              min_dates_allowed.push(new Date(date_exception).getTime())
            }
          }
          if(asmt.test_date_start){
            min_dates_allowed.push(new Date(asmt.test_date_start).getTime())
          }
          else{
            min_dates_allowed.push(new Date(asmt.tw_date_start).getTime())
          }
          this.scheduledRangeStarted = new Date(Math.min.apply(null,min_dates_allowed)).toISOString();
          console.log(this.scheduledRangeStarted)
          if(asmt.test_date_end){
            this.scheduledRangeEnded = new Date(Math.min(
              new Date(asmt.test_date_end).getTime(),
              new Date(asmt.tw_date_end).getTime()
            )).toISOString()
          }

        }
      }
    }
    if (!this.selectedAssessment){
      this.clearAssessmentSelection()
    }
  }
  clearAssessmentSelection(){
    this.selectedAssessment = null
    this.savePayload.slug = null
  }

  clearCourseSelection(){
    this.selectedCourse = null;
    this.selectedAssessment = null;
    this.savePayload.slug = null;
  }

  allowCustomDuration(asmt:{test_duration?: number, is_sample:number, is_session_duration_excluded: number,}){
     if (asmt.is_session_duration_excluded) {
      return false
    }
    else if (!asmt.test_duration || asmt.test_duration == 0){
      return true
    }
    else if (asmt.is_sample == 1){
      return true
    }
    return false
  }

  allowCustomName(asmt: { is_session_name_excluded: number; }) {
    return asmt.is_session_name_excluded ? false : true
  }
  
  allowCapacity(asmt:{is_field_test?: any}){
    if (asmt.is_field_test == 1){
      return true
    }
    return false
  }
  
  getSystemMessageSlug(){
    const sessionType = this.getSessionType();
    if (sessionType){
      const isFilterActive = (prop:string) => (sessionType.filter[prop] == 1)
      if (isFilterActive(KnownSessionFilterFlags.is_field_test)){
        return 'SCHED_FT_MSG'
      }
      else if (isFilterActive(KnownSessionFilterFlags.is_sample)){
        return 'SCHED_PR_MSG'
      }
      else {
        return 'SCHED_MSG'
      }
    }
    return 
  }

  isAssessmentSelected(asmt:{type_slug: string}){ // deprecated
    return this.selectedAssessment === asmt;
  }

  sessionName:string = ''
  getAvailableAssessments(){
    return this.assessments
  }
  createPracticeSession(){
    console.log(this.assessments)
  }

  // todo:DB_DATA_MODEL hard-coded relationship should be modeled in DB (the fact that teachers)
  abedTeacherRestriction(){ // deprecated
    // if(this.whitelabelService.isABED() && this.forTeacher){
    //   this.assessmentTypeOptions = [
    //     {
    //       id: AssessmentTypeOptions.FIELD_TEST,
    //       label: this.renderTitle(AssessmentTypeOptions.FIELD_TEST)
    //     }
    //   ]
    // }
  }
  initializeScheduleOptions() {
    if(this.assessmentToBeScheduled === ASSESSMENT.PRIMARY_OPERATIONAL || this.assessmentToBeScheduled === ASSESSMENT.JUNIOR_OPERATIONAL || this.assessmentToBeScheduled === ASSESSMENT.G9_OPERATIONAL || this.assessmentToBeScheduled === ASSESSMENT.OSSLT_OPERATIONAL){
      this.scheduleOptions = [{
        id:SCHEDULER.LATER,
        label:this.renderTitle(SCHEDULER.LATER)
      }]
    }
    else if(this.whitelabelService.isNBED() || this.whitelabelService.isMBED()) {
      this.scheduleOptions = [{
        id:SCHEDULER.NOW,
        label:this.renderTitle(SCHEDULER.NOW)
      }]
    }
    else{
      this.scheduleOptions = this.schedules.map(schedule => {
        return {
          id: schedule,
          label: this.renderTitle(schedule),
        }
      })
    }
  }

  // checks if the specified session is complete or not
  isSessionComplete(session_i: number) {
    return !this.isSessionCompleted || this.isSessionCompleted[session_i];
  }

  // converts the start and end times of the test window to the local timezone and formats them. It also provides the timezone abbreviation
  getDateProps(){
    const startMomentTime = moment.utc(this.savePayload.testWindow.date_start).tz(this.whitelabelService.getTimeZone());
    const startDate = startMomentTime.format(this.whitelabelService.isABED() ? this.lang.tra("abed_new_session_date_format") : 'ddd MMM DD YYYY' );
    const startTime = startMomentTime.format(this.whitelabelService.isABED() && this.lang.getCurrentLanguage() === 'fr' ? 'H [h] mm' : 'h:mm A');

    const endMomentTime = moment.utc(this.savePayload.testWindow.date_end).tz(this.whitelabelService.getTimeZone());
    const endDate = endMomentTime.format(this.whitelabelService.isABED() ? this.lang.tra("abed_new_session_date_format") : 'ddd MMM DD YYYY');
    const endTime = endMomentTime.format(this.whitelabelService.isABED() && this.lang.getCurrentLanguage() === 'fr' ? 'H [h] m' : 'h:mm A');

    let timezone = this.getWhitelabelTimeZone()
    // timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label')
    return{
      startDate: startDate,
      startTime:`${startTime} ${timezone}`,
      endDate: endDate,
      endTime:`${endTime} ${timezone}`
    }
  }

  // initializes the form entries based on the session. It takes different actions based on whether the session is currently being edited or not. If it's being edited, it sets up different fields based on the session slug. If it's not being edited, it sets up a default form
  initFormEntries(session: Partial<ISession>) {
    if(this.isEditing){
      this.showDatePicker = true;
      // todo:DB_DATA_MODEL hard-coded relationship should be modeled in DB
      if(session.slug && (session.slug === ASSESSMENT.PRIMARY_SAMPLE || session.slug === ASSESSMENT.PRIMARY_OPERATIONAL || session.slug === ASSESSMENT.JUNIOR_SAMPLE || session.slug === ASSESSMENT.JUNIOR_OPERATIONAL)) {
        this.formEntriesList = initFormEntries(session, [
          { valProp: 'sessionLangStartDate', label: 'lbl_date' },
          { valProp: 'sessionLangEndDate', label: 'lbl_date' }, 
          { valProp: 'sessionMathStartDate', label: 'lbl_date' },
          { valProp: 'sessionMathEndDate', label: 'lbl_date' }, 
        ]);
      }
      // todo:DB_DATA_MODEL hard-coded relationship should be modeled in DB
      if(session.slug && session.slug === ASSESSMENT.G9_SAMPLE) {
        this.removesessionB = true;
        this.formEntriesList = initFormEntries(session, [
          { valProp: 'sessionAStartDate', label: 'lbl_date' },
          { valProp: 'sessionAStartTime', label: 'lbl_time' },  // Start Time
        ]);
      } 
      // todo:DB_DATA_MODEL hard-coded relationship should be modeled in DB
      if(session.slug && (session.slug === ASSESSMENT.G9_OPERATIONAL || session.slug === ASSESSMENT.OSSLT_SAMPLE || session.slug === ASSESSMENT.OSSLT_OPERATIONAL)) {
        this.formEntriesList = initFormEntries(session, [
          { valProp: 'sessionAStartDate', label: 'lbl_date' },
          { valProp: 'sessionAStartTime', label: 'lbl_time' },  // Start Time
          { valProp: 'sessionBStartDate', label: 'lbl_date' },
          { valProp: 'sessionBStartTime', label: 'lbl_time' },
        ]);
      }
    }
    else
    { 
      const assessments = this.getAssessmentsForNewSession();
      console.log('session: ' + session);
      this.formEntriesList = initFormEntries(session, [
        //{type: SaFormType.TEXT,    valProp:'invigilator',  label: 'sa_session_teacher' },     // Teacher/Invigilator
        //{type: SaFormType.TEXT,    valProp:'classroom_id', label: 'sa_session_class_code' },
        { type: SaFormType.SELECT, valProp: 'classId', label: this.whitelabelService.isABED() ?  "abed_class_code" :'sa_class_code', options: this.classes, isLocked: this.savePayload.classId ? true : false },  // Class Code
        { type: SaFormType.SELECT, valProp: 'slugType', label: this.whitelabelService.isABED() ?  "abed_assessment_type ":'lbl_assessment_type', options: <any> this.assessmentTypeOptions }, // Field Test / Operational
        { type: SaFormType.SELECT, valProp: 'slug', label: 'lbl_assessment', options: assessments, isHidden: true}, // Description
        { type: SaFormType.SELECT, valProp: 'schedule', label: this.whitelabelService.isABED() ? "abed_start" : 'lbl_start', options: this.scheduleOptions, isHidden: true },
        { valProp: 'schedulingConstraints', label: 'lbl_scheduling_constraint' },
        { valProp: 'sessionAStartDate', label: this.whitelabelService.isABED() ? "abed_date" : 'lbl_date' },
        { valProp: 'sessionAStartTime', label: this.whitelabelService.isABED() ? "abed_time" : 'lbl_time' },  // Start Time
        { valProp: 'sessionBStartDate', label: this.whitelabelService.isABED() ? "abed_date" : 'lbl_date' },
        { valProp: 'sessionBStartTime', label: this.whitelabelService.isABED() ? "abed_time" : 'lbl_time' },
        { valProp: 'sessionLangStartDate', label: this.whitelabelService.isABED() ? "abed_date" : 'lbl_date' },
        { valProp: 'sessionLangEndDate', label: this.whitelabelService.isABED() ? "abed_date" : 'lbl_date' }, 
        { valProp: 'sessionMathStartDate', label: this.whitelabelService.isABED() ? "abed_date" : 'lbl_date' },
        { valProp: 'sessionMathEndDate', label: this.whitelabelService.isABED() ? "abed_date" : 'lbl_date' }, 
        // {                          valProp:'endTime',      label: 'sa_session_end_time' },    // End Time
        // {type: SaFormType.CHECK,   valProp:'isConfirmed',  label: 'sa_session_confirmed', options: GENERAL_SINGLE_CHECK.list } //Confirmed
      ]);
    }

    this.extractFormEntryRef();
  }

  // builds a dictionary with form entries for easier referencing later
  extractFormEntryRef() {
    this.formEntries = {};
    this.formEntriesList.forEach(entry => {
      this.formEntries[entry.valProp] = entry;
    })
  }

  // initializes the assessments by mapping the renderTitle function to each of them
  initializeAssessments() {
    this.assessmentTypes = this.assessments.map(assessment => {
      return {
        id: assessment,
        label: this.renderTitle(assessment),
      }
    })
  }

  // sets the selected session based on the current list of sessions
  initSelectedSession() {
    if (this.sessions) {
      if (this.sessions.length === 1) {
        return this.selectSession(this.sessions[0]);
      }
      if (this.sessions.length > 1) {
        return this.isSelectingSession = true;
      }
    }
    return this.selectSession({});  
  }

  // selects a session and initializes the form entries and payload subscription for it
  selectSession(session: Partial<ISession>) {
    this.isSelectingSession = false;
    this.selectedSession = session;
    this.initFormEntries(this.selectedSession);
    this.initPayloadSubscription(this.selectedSession);
  }
  
  // checks whether the selected session is completed or not
  updateSessionsCompleted() {
    if (this.whitelabelService.isABED()) {
      this.removesessionB = true;
    }

    let classId = null;
    let asmtSlug = null;
    if(this.isEditing) {
      if(this.sessions && this.sessions.length == 1){
        classId = this.sessions[0].classroom_id;
        asmtSlug = this.sessions[0].slug;
      } else {
        this.isSessionCompleted = [false, false]
        return
      }
    } else {
      classId = this.formEntries['classId'].formControlRef.value;
      asmtSlug = this.formEntries['slug'].formControlRef.value;
    }
    
    this.isSessionCompleted = undefined;
    if(!classId || !asmtSlug) {
      return;
    }

    let groupId = this.schoolService.getCurrentSchoolGroupId();
    if (!groupId) {
      groupId = this.g9DemoData.schoolData.group_id;
    }

    this.auth.apiFind(this.routes.SCHOOL_ADMIN_SESSION_COMPLETE, {query: {asmt_slug: asmtSlug, schl_class_id: classId, schl_group_id: groupId}}).then((res) => {
      this.isSessionCompleted = res;
    })  }


    // initializes the classes based on data from the g9DemoData object
  initializeClasses() {
    this.classes = [];
    this.g9DemoData.classrooms.forEach(classroom => {
      // todo:DB_DATA_MODEL hard-coded relationship should be modeled in DB
      const isClassPrimary = (classroom.course_type === "EQAO_G3");
      let course_type = classroom.course_type;
      if (classroom.course_type === "EQAO_G10") {
        course_type = "OSSLT"; // need to do this because OSSLT and EQAO_G10 are not consistently named
      }
      const courseTypeMatchesAssessmentType = course_type.includes(this.savePayload.filter);
      const semester = this.g9DemoData.semesters.map[classroom.semester]
      const testWindow = this.g9DemoData.testWindows.find(tw => tw.id == semester.testWindowId)
      const isActive = new Date(testWindow.date_end) > new Date ()
      const isPlaceholder = classroom.is_placeholder
      if (this.isValidAssessmentType && courseTypeMatchesAssessmentType && isActive && !isPlaceholder) {
        if (isClassPrimary) {
          this.classes.push({
            id: classroom.id,
            label: classroom.class_code,
            is_fi: classroom.is_fi,
            group_id: classroom.group_id,
          })
        } else {
          this.classes.push({
            id: classroom.id,
            label: classroom.class_code,
            group_id: classroom.group_id,
          })
        }
      }
    })
  }

  // sets up a subscription to the form entries. Whenever a form entry value changes, it updates the payload data accordingly.
  initPayloadSubscription(sessionRecord: Partial<ISession>) {
    const payload = {
      record: sessionRecord,
      data: this.savePayload?.payload?.data || {}
    };

    this.savePayload[this.saveProp] = payload;
    this.formEntriesList.forEach(entry => {
      if (Object.keys(payload.data).length > 0) {
        const prop = entry.valProp;
        const val = payload.data[prop];
        if (typeof val !== 'undefined') {
          this.updateNewFormData(entry, val, payload);
          payload.data[prop] = val;
          entry.formControlRef.setValue(val);
        }
      }

      if (sessionRecord || payload.data) { const prop = entry.valProp;
        const val = sessionRecord[prop];
        if (typeof val !== 'undefined') {
          payload.data[prop] = val;
        }
      }

      if (entry.valProp === 'classId' && this.savePayload.classId) {
        entry.formControlRef.setValue(this.savePayload.classId);
        payload.data['classId'] = this.savePayload.classId;
      }

      entry.formControlRef.valueChanges.subscribe(val => {
        this.updateNewFormData(entry, val, payload);
        payload.data[entry.valProp] = val;

        if (entry.valProp === "slug") {
          this.modifyPayloadForOperationalAssessment(payload, val);
        }
      });
    })
  }

  // modifies the payload for operational assessment type, it updates the test_duration and schedule if the slugType is operational
  modifyPayloadForOperationalAssessment(payload: {record: Partial<ISession>, data: any}, assessmentSlug: any) {
    let assessment: any | {} = this.findAssessmentBySlug(assessmentSlug, payload.data.slugType) || {};
    if (assessment != null && payload.data.slugType === AssessmentTypeOptions.OPERATIONAL) {
      payload.data.test_duration = assessment.test_duration;      
      payload.data.schedule = SCHEDULER.LATER;
      payload.data.sessionAStartDateTime = this.generateStartDateTimeForABED(assessment.test_date_start);         
    }
  }

  // converts the provided UTC date and time to the local timezone, then returns the date and time in "YYYY-MM-DDTHH:mm" format
  // todo:DB_DATA_MODEL hard-coded relationship should be modeled in DB
  generateStartDateTimeForABED(utcDateTime: string) {
    let convertedMoment = this.auth.convertUTCToWhitelabelTZ(utcDateTime)
    // console.log(inputDTMoment.utcOffset());
    return convertedMoment.format("YYYY-MM-DDTHH:mm");
  }

  updateNewFormData(entryMain, val, payload) {
    if (entryMain.valProp === 'slugType') {
      this.formEntriesList.forEach((entry) => {
        if (entry.valProp === 'slug') {
          if (val) {
            entry.isHidden = false;
          }
          else {
            entry.isHidden = true;
          }
          
          payload.data['slug'] = undefined;
          entry.formControlRef.setValue(undefined, {emitEvent: false});
          this.setCurrentlySelectedAssessment(val, null);

          entry.options = this.getAssessmentsForNewSession(val, true);
        }

        if (entry.valProp === 'schedule') {
          // todo:DB_DATA_MODEL hard-coded relationship should be modeled in DB
          if (val === AssessmentTypeOptions.FIELD_TEST) {
            entry.isHidden = false;
          }
          else {
            entry.isHidden = true;
            this.showDatePicker = false;
          }
        }
      });
    }
    else if (entryMain.valProp === 'schedule') {
      let assessment = this.formEntriesList.find(entry => entry.valProp === 'slug');
      if (val === SCHEDULER.LATER && assessment.formControlRef.value != '' && 
      assessment.formControlRef.value != null) {
        this.showDatePicker = true;
      }
    }
  }

  shouldAssessmentBeDisabled(testDateStart: string, assessmentType?: AssessmentTypeOptions, asmtName?: string): boolean {
    // todo:DB_DATA_MODEL hard-coded relationship should be modeled in DB
    return assessmentType === AssessmentTypeOptions.OPERATIONAL && !this.isDateTimeValidAndNotInPast(testDateStart, asmtName);
  }

  isDateTimeValidAndNotInPast(UTCDateTime: string, assessmentName?: string): boolean {
    if (UTCDateTime == null || UTCDateTime == "" || !moment(UTCDateTime).isValid())
    {
      this.addToDisabledAssessments(DisabledAssessmentReason.NO_DT, assessmentName);
      return false;
    }

    let DTNotInPast: boolean = !this.isDateTimeInThePast(UTCDateTime);
    if (!DTNotInPast)
    {
      this.addToDisabledAssessments(DisabledAssessmentReason.PAST_DT, assessmentName);
    }

    return DTNotInPast;
  }

  addToDisabledAssessments(key: DisabledAssessmentReason, assessmentName?: string)
  {
    const idx = this.findDisabledAssessmentIdx(key);
    if (idx !== -1 && assessmentName){
      this.disabledAssessments[idx].assessmentNames.push(assessmentName);
    }
  }


  findDisabledAssessmentIdx(key: DisabledAssessmentReason)
  {
    const relevantIdx = this.disabledAssessments.findIndex(each => each.key === key);
    if (relevantIdx === -1)
    {
      console.warn("Unexpected error in setting disabled assessments.")
    }

    return relevantIdx;
  }


  isDateTimeInThePast(UTCDateTime: string): boolean
  {
    const inputDTMoment = moment(UTCDateTime).utc(); // in UTC
    const nowDTMoment = moment().utc(); // in UTC
    return inputDTMoment.isBefore(nowDTMoment);
  }

  disableAssessmentTypesIfNeeded() {

    if (this.formEntriesList == null) {
      return;
    }

    let assessmentTypes = this.formEntriesList.find((entry) => {
      return entry.valProp === 'slugType';
    });

    let assessmentTypeOptions = assessmentTypes?.options;

    if (assessmentTypeOptions != null) {
      assessmentTypeOptions.forEach(assessmentType => {
        let assessments = this.getAssessmentsForNewSession(assessmentType.id as AssessmentTypeOptions);
        if (assessments.length === 0) {
          assessmentType.disabled = true;
          this.disabledAssessmentTypes.push(assessmentType.label);
        }
      });

      assessmentTypes.formControlRef.setValue(undefined);
    }
  }

  formatDisabledAssessmentTypesText(): string
  {
    let output = this.lang.tra("abed_no_test_design")
    this.disabledAssessmentTypes.forEach(aType => 
    {
      let aTypeText = "<b>"+aType+"</b>"+", ";
      output+=aTypeText;
    });
    output = output.slice(0, output.length-2); // remove last ", "
    output+=".";
    return output;
  }

  formatDisabledAssessmentsText(reason: string, disabledAsmts: string[]): string
  {
    // format per disabled assessment reason/type
    let output = reason;
    disabledAsmts.forEach(asmt => {
      let asmtText = "<b>"+asmt+"</b>"+", ";
      output+=asmtText;
    });
    output = output.slice(0, output.length-2); // remove last ", "
    output+=".";
    return output;
  }

  findAssessmentBySlug(assessmentSlug: string, assessmentType?: string)
  {
    // I noticed the existing logic assumes 1 form / slug for now; may need to be changed in the future
    const assessmentOptions = this.filterAssessmentOptionsAsRequired(assessmentType);
    let foundAssessments = assessmentOptions.filter(assessment => assessment.asmt_slug === assessmentSlug);
    if (foundAssessments.length === 0) {
      console.warn("No assessment found when selecting assessment.");
    }
    else if (foundAssessments.length > 1) {
      console.warn("Multiple assessments found when selecting assessment.");
    }
    return foundAssessments[0];
  }

  filterAssessmentsBasedOnSchoolLang(assessments: any[]): any[]
  {
    let schoolLang = this.g9DemoData?.schoolData?.lang;
    if (schoolLang == null)
    {
      return assessments;
    }
    
    return assessments.filter(assessment => assessment.lang === schoolLang.toLocaleLowerCase() && assessment.asmt_type_slug != ASSESSMENT.ABED_MATH_6A && assessment.asmt_type_slug != ASSESSMENT.ABED_MATH_6B);
  }

  filterAssessmentOptionsAsRequired(assessmentType?: string): any[] {
    let assessmentOptions = this.savePayload.testWindow.assessments;
    if (assessmentType) {
      const isFieldTest = assessmentType ===  AssessmentTypeOptions.FIELD_TEST ? 1 : 0;
      assessmentOptions = this.savePayload.testWindow.assessments.filter(asmt => asmt.is_field_test === isFieldTest);
    }
    // todo:DB_DATA_MODEL hard-coded relationship should be modeled in DB
    if (this.savePayload.filter === 'ABED_GRADE_6') { // todo:DB_DATA_MODEL
      assessmentOptions = assessmentOptions.filter(asmt => asmt.asmt_slug.includes('_6'));
    }
    if (this.savePayload.filter === 'ABED_GRADE_9') { // todo:DB_DATA_MODEL
      assessmentOptions = assessmentOptions.filter(asmt => asmt.asmt_slug.includes('_9'));
    }
    return assessmentOptions;
  }

  getAssessmentsForNewSession = (assessmentType?: AssessmentTypeOptions, disableAssessmentsIfNeeded: boolean = false) => {
    this.clearDisabledAssessments();
    let assessmentOptions = this.filterAssessmentOptionsAsRequired(assessmentType);
    assessmentOptions = this.filterAssessmentsBasedOnSchoolLang(assessmentOptions);
    const assessmentData = assessmentOptions.map(asmt => {
      return ( {
        id: asmt?.asmt_slug,
        slug: asmt?.asmt_slug,
        label: this.renderTitle(asmt?.asmt_slug, asmt?.lang),
        disabled: disableAssessmentsIfNeeded &&
        this.shouldAssessmentBeDisabled(asmt.test_date_start, assessmentType, this.renderTitle(asmt?.asmt_slug, asmt?.lang))
      })
    });

    // let str = "";
    // assessmentData.forEach(each => str+=each.id+"\n");
    // console.log("assessments for tw\n", str);
    return assessmentData;
  }

  clearDisabledAssessments() {
    // resets disabled assessments to show on UI
    for (let disAss of this.disabledAssessments) {
      disAss.assessmentNames = [];
    }
  }

  isThereAtLeastOneDisabledAssessment(): boolean {
    for (let disAss of this.disabledAssessments) {
      if (disAss.assessmentNames.length > 0) {
        return true;
      }
    }
    return false;
  }

  // renders the title of an assessment based on the title string and the provided language. It uses constants like ASSESSMENT.PRIMARY_SAMPLE and ASSESSMENT.JUNIOR_OPERATIONAL to determine the appropriate translated string for the title. Some of these titles are static, while others are being fetched from a language translation service
  renderTitle(title: string, lang?: string) {
    // todo:DB_DATA_MODEL hard-coded relationship should be modeled in DB
    switch (title) {
      case ASSESSMENT.PRIMARY_SAMPLE: return this.lang.tra("primary_sample_test");
      case ASSESSMENT.PRIMARY_OPERATIONAL: return this.lang.tra("primary_operational_test");
      
      case ASSESSMENT.JUNIOR_SAMPLE: return this.lang.tra("junior_sample_test");
      case ASSESSMENT.JUNIOR_OPERATIONAL: return this.lang.tra("junior_operational_test");
     
      case ASSESSMENT.G9_OPERATIONAL: return this.lang.tra("g9_operational_test");
      //case ASSESSMENT.G9_SAMPLE: return 'Sample Assessment (G9 Math)'
      case ASSESSMENT.G9_SAMPLE: return this.lang.tra('lbl_sample_g9');
      
      case ASSESSMENT.OSSLT_OPERATIONAL: return this.lang.tra('lbl_osslt_test');
      //case ASSESSMENT.OSSLT_SAMPLE: return this.lang.tra('lbl_sample_assessment');
      case ASSESSMENT.OSSLT_SAMPLE: return this.lang.tra('lbl_sample_test_osslt');

      case ASSESSMENT.TCLE_OPERATIONAL : return 'TCLE OPERATIONAL' //cretae label 
      case ASSESSMENT.TCLE_SAMPLE : return 'TCLE SAMPLE' // create label

      case ASSESSMENT.TCN_OPERATIONAL : return 'TCN OPERATIONAL'
      case ASSESSMENT.TCN_SAMPLE : return 'TCN SAMPLE'

      case ASSESSMENT.SCIENCES8_OPERATIONAL : return 'SCIENCES8 OPERATIONAL'
      case ASSESSMENT.SCIENCES8_SAMPLE : return 'SCIENCES8 SAMPLE'

      case ASSESSMENT.MBED_SAMPLE : return 'MBED SAMPLE';
      
      case ASSESSMENT.ABED_ELA_6A: return this.lang.tra('assessment_abed_ela_part_a');
      case ASSESSMENT.ABED_ELA_6B: return this.lang.tra('assessment_abed_ela_part_b');
      case ASSESSMENT.ABED_FLA_6A: return this.lang.tra('assessment_abed_fla_part_a');
      case ASSESSMENT.ABED_FLA_6B: return this.lang.tra('assessment_abed_fla_part_b');
      case ASSESSMENT.ABED_MATH_6A: return this.lang.tra('assessment_abed_math_part_a');
      case ASSESSMENT.ABED_MATH_6B: return this.lang.tra('assessment_abed_math_part_b');
      case ASSESSMENT.ABED_MATH_6A2: return this.lang.tra('assessment_abed_math_part_a2');
      case ASSESSMENT.ABED_MATH_6B2: return this.lang.tra('assessment_abed_math_part_b2');
      case ASSESSMENT.ABED_SCIENCES_6: return this.lang.tra('assessment_abed_g6_science');
      case ASSESSMENT.ABED_SOCIAL_STUDIES_6: return this.lang.tra('assessment_abed_g6_social');

      case ASSESSMENT.ABED_ELA_9A: return this.lang.tra('assessment_abed_g9_ela_part_a');
      case ASSESSMENT.ABED_ELA_9B: return this.lang.tra('assessment_abed_g9_ela_part_b');
      case ASSESSMENT.ABED_FLA_9A: return this.lang.tra('assessment_abed_g9_fla_part_a');
      case ASSESSMENT.ABED_FLA_9B: return this.lang.tra('assessment_abed_g9_fla_part_b');
      case ASSESSMENT.ABED_MATH_9A: return this.lang.tra('assessment_abed_g9_math_part_a');
      case ASSESSMENT.ABED_MATH_9B: return this.lang.tra('assessment_abed_g9_math_part_b');
      case ASSESSMENT.ABED_SCIENCES_9: return this.lang.tra('assessment_abed_g9_science');
      case ASSESSMENT.ABED_SOCIAL_STUDIES_9: return this.lang.tra('assessment_abed_g9_social');

      case ASSESSMENT.ABED_KE_ELA_9A: return this.lang.tra('assessment_abed_g9_K&E_ela_part_a');
      case ASSESSMENT.ABED_KE_ELA_9B: return this.lang.tra('assessment_abed_g9_K&E_ela_part_b');
      case ASSESSMENT.ABED_KE_MATH_9: return this.lang.tra('assessment_abed_g9_K&E_math');
      case ASSESSMENT.ABED_KE_SCIENCES_9: return this.lang.tra('assessment_abed_g9_K&E_science');
      case ASSESSMENT.ABED_KE_SOCIAL_STUDIES_9: return this.lang.tra('assessment_abed_g9_K&E_social');

      case ASSESSMENT.ABED_SAMPLE_ELA_6: return this.lang.tra('assessment_abed_sample_ela_6');
      case ASSESSMENT.ABED_SAMPLE_MATH_6: return this.lang.tra('assessment_abed_sample_math_6');
      case ASSESSMENT.ABED_SAMPLE_SCIENCE_6: return this.lang.tra('assessment_abed_sample_science_6');
      case ASSESSMENT.ABED_SAMPLE_FR_IMMERSION_6: return this.lang.tra('assessment_abed_sample_fr_immersion_6');
      case ASSESSMENT.ABED_SAMPLE_FR_FIRST_6: return this.lang.tra('assessment_abed_sample_fr_first_6');

      case ASSESSMENT.ABED_SAMPLE: return lang ? this.lang.tra('assessment_abed_2', lang) : this.lang.tra('assessment_abed_2');
      case ASSESSMENT.ABED_OPERATIONAL: return lang ? this.lang.tra('assessment_abed_1', lang) : this.lang.tra('assessment_abed_1');

      case ASSESSMENT.SMCS_EN_G7 : return 'SMCS_EN_G7';

      // todo:STREAMLINE_LOGIC separate this chunk
      case AssessmentTypeOptions.FIELD_TEST: return this.whitelabelService.isABED() ? this.lang.tra('abed_lbl_field_test') : 'Field Test';
      case AssessmentTypeOptions.OPERATIONAL: return this.whitelabelService.isABED() ? this.lang.tra('abed_lbl_op_ass') : 'Operational';
      
      // todo:STREAMLINE_LOGIC separate this chunk
      case SCHEDULER.NOW: return this.whitelabelService.isABED() ? this.lang.tra("abed_now") : this.lang.tra('lbl_now');
      case SCHEDULER.LATER: return this.whitelabelService.isABED() ? this.lang.tra("abed_later") : this.lang.tra('lbl_later');      
    }
  }

  // returns a list of form entries. The list is filtered to include only entries that have a defined type
  saWidgetFormEntries(): any {
    return this.formEntriesList.filter(entry => !!entry.type)
  }

  // checks and processes the selection based on the entry and value provided. It updates certain fields based on conditions, initializes the schedule options for specific assessments, sets the visibility of a date picker, and checks if the selected assessment type is a field test
  checkSelection(entry, value) {
    if(entry.valProp === 'classId') {
      this.updateSessionsCompleted();
      this.isFIClass = this.checkFIClass(value);       
    } 
    else if(entry.valProp === 'slug') {
      this.updateSessionsCompleted();
      let scheduler = this.formEntriesList.find((entry) => {
        return entry.valProp === 'schedule';
      });
      let assessmentType = this.formEntriesList.find((entry) => {
        return entry.valProp === 'slugType';
      });
      if (value != '' && value != null && scheduler.formControlRef.value === SCHEDULER.LATER && assessmentType.formControlRef.value === AssessmentTypeOptions.FIELD_TEST) {
        this.showDatePicker = true;
      }
      this.setCurrentlySelectedAssessment(assessmentType.formControlRef.value, value);
      // todo:DB_DATA_MODEL hard-coded relationship should be modeled in DB
      switch(value) {
        case(ASSESSMENT.PRIMARY_SAMPLE):
        case(ASSESSMENT.JUNIOR_SAMPLE):
          this.assessmentToBeScheduled = value;
          this.initializeScheduleOptions();
          this.formEntriesList[2].options = this.scheduleOptions;
        break;
        case(ASSESSMENT.PRIMARY_OPERATIONAL):
        case(ASSESSMENT.JUNIOR_OPERATIONAL):
          this.assessmentToBeScheduled = value;
          this.initializeScheduleOptions();
          this.formEntriesList[2].options = this.scheduleOptions;
        break;
        case(ASSESSMENT.OSSLT_SAMPLE):
          this.assessmentToBeScheduled = value;
          this.initializeScheduleOptions();
          this.formEntriesList[2].options = this.scheduleOptions;
          this.removesessionB = false;
          //this.initSelectedSession()
        break;
        case(ASSESSMENT.G9_OPERATIONAL):
        case(ASSESSMENT.OSSLT_OPERATIONAL):
          this.assessmentToBeScheduled = value;
          this.initializeScheduleOptions();
          this.formEntriesList[2].options = this.scheduleOptions;
          this.removesessionB = false;
          //this.initSelectedSession()
          break;
        case(ASSESSMENT.G9_SAMPLE):
          this.assessmentToBeScheduled = value;
          this.initializeScheduleOptions();
          this.removesessionB = true;
          this.formEntriesList[2].options = this.scheduleOptions;
          break;
        case(ASSESSMENT.OSSLT_SAMPLE):
          this.assessmentToBeScheduled = value;
          this.initializeScheduleOptions();
          this.formEntriesList[2].options = this.scheduleOptions;
          this.removesessionB = false;
          //this.initSelectedSession()
          break;
        case(SCHEDULER.LATER):
          this.showDatePicker = true;
          break;
        case(SCHEDULER.NOW):
          this.showDatePicker = false;
          break;
      }
    } 
    else if (entry.valProp === 'schedule') {
      switch(value) {
        case(SCHEDULER.LATER):
          let assessment = this.formEntriesList.find(entry => entry.valProp === 'slug');
          if (assessment.formControlRef.value != '' && assessment.formControlRef.value != null) {
            this.showDatePicker = true;
          }
          break;
        case(SCHEDULER.NOW):
          this.showDatePicker = false;
          break;
      }
    }
    else if (entry.valProp === 'slugType') {
      // if switching back to field test, set scheduler back to now if it was on later
      this.currentlySelectedAssessmentType = value;
      if (value === AssessmentTypeOptions.FIELD_TEST) {
        let scheduler = this.formEntriesList.find((entry) => {
          return entry.valProp === 'schedule';
        });
        if (scheduler.formControlRef.value === SCHEDULER.LATER) {
          scheduler.formControlRef.setValue(SCHEDULER.NOW, {emitEvent: true});
        }
      }
    }
  }

  checkSelectionV2(entry, value) {
    if(entry.valProp === 'classId') {
      this.classAssessments = null;
      const chosenClass = entry.options.find(c => c.id == value);
      this.savePayload.payload.data.classId = chosenClass.id;
      this.selectClassV2(chosenClass.group_id); 
    } 
  }

  // sets the currently selected assessment, given the selected assessment type and selected assessment
  setCurrentlySelectedAssessment(selectedAssessmentType: string, selectedAssessment: string | null)
  {
    if (
      selectedAssessment == '' 
      || selectedAssessment == null 
      || selectedAssessmentType == '' 
      || selectedAssessmentType == null
      || selectedAssessmentType !== AssessmentTypeOptions.OPERATIONAL
    ){
      this.currentlySelectedAssessment = null;
    }
    else {
      this.currentlySelectedAssessment = this.findAssessmentBySlug(selectedAssessment, selectedAssessmentType);  
    }
  }

  // formats the date and time of the assessment based on the provided date and time string
  formatAssessmentDateTime(dateTime: string) {
    return this.auth.formatDateForWhitelabel(dateTime);
  }

  // formats the assessment name, given the assessment's slug and the language
  formatAssessmentName(assessmentSlug: string, assessmentLang: string) {
    return this.renderTitle(assessmentSlug, assessmentLang);
  }
  
  // checks if the class with the provided ID is a French Immersion (FI) class
  checkFIClass(classId: string): boolean {
    let currentClass = this.classes.find(c => c.id == classId && c?.is_fi === 1);
    return currentClass ? true : false;
  }

  // These methods determine whether to show Session A and Session B based on certain conditions
  // todo:DB_DATA_MODEL hard-coded relationship should be modeled in DB
  showSessionA() {
    return !this.isSessionComplete(0);
  }
  showSessionB() {
    return !this.removesessionB && !this.isSessionComplete(1);
  }

  setDateValidity($event) {
    this.isDateValid.emit($event)
  }

  // determines whether the date and time picker can be edited based on certain conditions
  canEditDatetimePicker(type: DatePickerType): boolean {
    if (!this.isEditing && !this.sessions) {
      return true;
    }
    const currentSession = this.sessions ? this.sessions[0] : null;
    this.isFIClass = currentSession ? this.checkFIClass(currentSession.classroom_id) : false;
    if (!this.isActive) {
      return true;
    }
    // const currentSession = this.sessions[0];
    const isFutureDate = (startDate: string): boolean => {
      if (!startDate) {
        return true;
      }
      const processedDate = moment(startDate, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD');
      const now = moment().format('YYYY-MM-DD');
      if (processedDate <= now) {
        return false;
      }
      return true;
      };
    switch(type) {
      case this.datePickerType.Language:
        if ((!currentSession.session_a.datetime_start && !currentSession.session_a.datetime_end) || isFutureDate(currentSession.session_a.datetime_start)) {
          return true;
        }
        break;
      case this.datePickerType.Math:
        if ((!currentSession.session_b.datetime_start && !currentSession.session_b.datetime_end) || isFutureDate(currentSession.session_b.datetime_start)) {
          return true;
        }
        break;
    }
    return false;
  }

  get datePickerType() {
    return DatePickerType;
  }

  get schoolDistFIOption():string {
    return this.g9DemoData.schoolDist[0].fi_option; 
  }
  
  getCompletedSlug() {
    return this.isOsslt ? 'msg_all_students_comp_sa_osslt' : 'msg_all_students_comp_sa'
  }

  isLanguageVisibleForFIClass(): boolean {
    // todo:DB_DATA_MODEL hard-coded relationship should be modeled in DB
    if(this.schoolDistFIOption === 'C' && this.isFIClass) {
      return false;
    }
    return true;
  }

  getAssessmentDatesSlug(){
    if(this.whitelabelService.isABED()){
      return "abed_time_open_writing_session"
    }
    return "lbl_assessment_dates"
  }

  getWhitelabelTimeZone(){
    if(this.whitelabelService.isABED()){
      return this.lang.tra("abed_timezone")
    }
    return this.whitelabelService.getTimezoneAbbr()
  }

  onChange(){
    if (this.selectedDateVal.value && this.selectedTimeOption.val){
      this.savePayload.scheduled_time = dateAndTimeToDbDate(this.selectedDateVal.value, this.selectedTimeOption.val, 'HH:mm')
    }
    else {
      this.savePayload.scheduled_time = undefined
    }
  }
}
